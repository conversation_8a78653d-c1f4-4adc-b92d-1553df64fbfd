#include "InputManager.h"

// Initialize static members
std::map<int, KeyState> InputManager::keyStates;
bool InputManager::debugMode = false;
bool InputManager::initialized = false;
std::vector<int> InputManager::monitoredKeys;

void InputManager::Initialize(bool debug) {
    debugMode = debug;
    initialized = true;

    if (debugMode) {
        std::cout << "InputManager initialized with debug mode " << (debug ? "enabled" : "disabled") << std::endl;
    }
}

void InputManager::Update() {
    if (!initialized) {
        Initialize(true); // Always initialize with debug mode for better diagnostics
    }

    // Update the state of all monitored keys
    for (int keyCode : monitoredKeys) {
        // Get the current state of the key - use both 0x8000 (currently down) and 0x0001 (pressed since last call)
        // This helps catch quick key presses that might be missed between frames
        SHORT keyState = GetAsyncKeyState(keyCode);
        bool isDown = (keyState & 0x8000) != 0;
        bool wasPressed = (keyState & 0x0001) != 0;

        // If the key is not in the map, add it
        if (keyStates.find(keyCode) == keyStates.end()) {
            keyStates[keyCode] = KeyState();
            if (debugMode) {
                std::cout << "Added new key state for: " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
            }
        }

        // Store previous state for debugging
        bool wasDown = keyStates[keyCode].isDown;
        bool wasJustPressed = keyStates[keyCode].justPressed;
        bool wasJustReleased = keyStates[keyCode].justReleased;

        // Special handling for key press detection
        if (wasPressed) {
            // Key was pressed since last call
            if (!wasDown) {
                // If it wasn't down before, mark it as just pressed
                isDown = true;
                keyStates[keyCode].justPressed = true;

                if (debugMode) {
                    std::cout << "Detected key press via 0x0001 flag for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                }
            }
        }

        // Also check with ::GetKeyState (Windows API) for more reliable detection
        SHORT directKeyState = ::GetKeyState(keyCode);
        bool directIsDown = (directKeyState & 0x8000) != 0;

        // If GetKeyState says the key is down but GetAsyncKeyState doesn't, trust GetKeyState
        if (directIsDown && !isDown) {
            isDown = true;
            if (!wasDown) {
                keyStates[keyCode].justPressed = true;

                if (debugMode) {
                    std::cout << "Detected key press via GetKeyState for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                }
            }
        }

        // If GetKeyState says the key is up but GetAsyncKeyState doesn't, trust GetKeyState
        if (!directIsDown && isDown) {
            // Key is up according to GetKeyState but down according to GetAsyncKeyState
            if (wasDown) {
                // If it was down before, mark it as just released
                isDown = false;
                keyStates[keyCode].justReleased = true;

                if (debugMode) {
                    std::cout << "Detected key release via GetKeyState for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                }
            }
        }

        // Update the key state
        keyStates[keyCode].Update(isDown);

        // Debug output if enabled
        if (debugMode && (keyStates[keyCode].justPressed || keyStates[keyCode].justReleased ||
                          (isDown != wasDown) || (keyStates[keyCode].justPressed != wasJustPressed) ||
                          (keyStates[keyCode].justReleased != wasJustReleased))) {
            std::cout << "Key state changed: ";
            DebugKey(keyCode);
        }
    }
}

void InputManager::MonitorKey(int keyCode) {
    // Don't add invalid keys or duplicates
    if (keyCode == 0 || std::find(monitoredKeys.begin(), monitoredKeys.end(), keyCode) != monitoredKeys.end()) {
        return;
    }

    monitoredKeys.push_back(keyCode);

    if (debugMode) {
        std::cout << "Now monitoring key: " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
    }
}

void InputManager::UnmonitorKey(int keyCode) {
    auto it = std::find(monitoredKeys.begin(), monitoredKeys.end(), keyCode);
    if (it != monitoredKeys.end()) {
        monitoredKeys.erase(it);

        if (debugMode) {
            std::cout << "Stopped monitoring key: " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
        }
    }
}

bool InputManager::IsKeyDown(int keyCode) {
    if (keyCode == 0) return false;

    // If the key is not being monitored, check its state directly
    if (std::find(monitoredKeys.begin(), monitoredKeys.end(), keyCode) == monitoredKeys.end()) {
        // Use both 0x8000 (currently down) and 0x0001 (pressed since last call)
        SHORT keyState = GetAsyncKeyState(keyCode);
        bool isDown = (keyState & 0x8000) != 0 || (keyState & 0x0001) != 0;

        // Also check with ::GetKeyState (Windows API) for more reliable detection
        SHORT directKeyState = ::GetKeyState(keyCode);
        bool directIsDown = (directKeyState & 0x8000) != 0;

        // If either method says the key is down, consider it down
        return isDown || directIsDown;
    }

    // Otherwise, use the cached state
    return keyStates[keyCode].isDown;
}

bool InputManager::IsKeyJustPressed(int keyCode) {
    if (keyCode == 0) return false;

    // If the key is not being monitored, add it
    if (std::find(monitoredKeys.begin(), monitoredKeys.end(), keyCode) == monitoredKeys.end()) {
        MonitorKey(keyCode);
        Update(); // Update immediately to get the current state
    }

    // Check if the key is just pressed
    bool justPressed = keyStates[keyCode].justPressed;

    // Also check the current state directly to catch quick presses
    if (!justPressed) {
        SHORT keyState = GetAsyncKeyState(keyCode);
        bool isDown = (keyState & 0x8000) != 0 || (keyState & 0x0001) != 0;

        // If the key is down but not marked as just pressed, check if it was up in the previous frame
        if (isDown && !keyStates[keyCode].isDown) {
            // Force update the key state
            keyStates[keyCode].Update(isDown);
            justPressed = true; // Force it to be just pressed
            keyStates[keyCode].justPressed = true; // Update the state directly

            if (debugMode) {
                std::cout << "Direct key press detected for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                std::cout << "Forced justPressed = true for " << GetKeyName(keyCode) << std::endl;
            }
        }
    }

    // Double-check with GetAsyncKeyState for the most up-to-date state
    if (!justPressed) {
        SHORT keyState = GetAsyncKeyState(keyCode);
        if ((keyState & 0x0001) != 0) { // Key was pressed since the last call
            justPressed = true;
            keyStates[keyCode].justPressed = true;
            keyStates[keyCode].isDown = true;

            if (debugMode) {
                std::cout << "GetAsyncKeyState detected press for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                std::cout << "Forced justPressed = true for " << GetKeyName(keyCode) << std::endl;
            }
        }
    }

    // If the key is still not detected as just pressed, try one more direct check
    if (!justPressed) {
        // Try a direct check with ::GetKeyState (Windows API)
        SHORT keyState = ::GetKeyState(keyCode);
        bool isDown = (keyState & 0x8000) != 0;

        // If the key is down, force it to be just pressed
        if (isDown) {
            justPressed = true;
            keyStates[keyCode].justPressed = true;
            keyStates[keyCode].isDown = true;

            if (debugMode) {
                std::cout << "GetKeyState detected press for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                std::cout << "Forced justPressed = true for " << GetKeyName(keyCode) << std::endl;
            }
        }
    }

    // If the key is detected as just pressed, print a debug message
    if (justPressed && debugMode) {
        std::cout << "Key " << GetKeyName(keyCode) << " (" << keyCode << ") is just pressed" << std::endl;
    }

    return justPressed;
}

bool InputManager::IsKeyJustReleased(int keyCode) {
    if (keyCode == 0) return false;

    // If the key is not being monitored, add it
    if (std::find(monitoredKeys.begin(), monitoredKeys.end(), keyCode) == monitoredKeys.end()) {
        MonitorKey(keyCode);
        Update(); // Update immediately to get the current state
    }

    // Check if the key is just released
    bool justReleased = keyStates[keyCode].justReleased;

    // Also check the current state directly to catch quick releases
    if (!justReleased) {
        SHORT keyState = GetAsyncKeyState(keyCode);
        bool isDown = (keyState & 0x8000) != 0 || (keyState & 0x0001) != 0;

        // If the key is up but not marked as just released, check if it was down in the previous frame
        if (!isDown && keyStates[keyCode].isDown) {
            // Force update the key state
            keyStates[keyCode].Update(isDown);
            justReleased = true; // Force it to be just released
            keyStates[keyCode].justReleased = true; // Update the state directly
            keyStates[keyCode].isDown = false; // Make sure isDown is false

            if (debugMode) {
                std::cout << "Direct key release detected for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                std::cout << "Forced justReleased = true for " << GetKeyName(keyCode) << std::endl;
            }
        }
    }

    // Double-check with GetAsyncKeyState for the most up-to-date state
    if (!justReleased && keyStates[keyCode].isDown) {
        SHORT keyState = GetAsyncKeyState(keyCode);
        if ((keyState & 0x8000) == 0) { // Key is not down now
            justReleased = true;
            keyStates[keyCode].justReleased = true;
            keyStates[keyCode].isDown = false;

            if (debugMode) {
                std::cout << "GetAsyncKeyState detected release for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                std::cout << "Forced justReleased = true for " << GetKeyName(keyCode) << std::endl;
            }
        }
    }

    // If the key is still not detected as just released, try one more direct check
    if (!justReleased && keyStates[keyCode].isDown) {
        // Try a direct check with ::GetKeyState (Windows API)
        SHORT keyState = ::GetKeyState(keyCode);
        bool isDown = (keyState & 0x8000) != 0;

        // If the key is not down, force it to be just released
        if (!isDown) {
            justReleased = true;
            keyStates[keyCode].justReleased = true;
            keyStates[keyCode].isDown = false;

            if (debugMode) {
                std::cout << "GetKeyState detected release for " << GetKeyName(keyCode) << " (" << keyCode << ")" << std::endl;
                std::cout << "Forced justReleased = true for " << GetKeyName(keyCode) << std::endl;
            }
        }
    }

    // If the key is detected as just released, print a debug message
    if (justReleased && debugMode) {
        std::cout << "Key " << GetKeyName(keyCode) << " (" << keyCode << ") is just released" << std::endl;
    }

    return justReleased;
}

std::string InputManager::GetKeyName(int keyCode) {
    return VirtualKeyToString(keyCode);
}

std::string InputManager::VirtualKeyToString(int keyCode) {
    if (keyCode == 0) return "None";

    // Mouse buttons
    if (keyCode == VK_LBUTTON) return "Left Mouse";
    if (keyCode == VK_RBUTTON) return "Right Mouse";
    if (keyCode == VK_MBUTTON) return "Middle Mouse";
    if (keyCode == VK_XBUTTON1) return "Mouse 4";
    if (keyCode == VK_XBUTTON2) return "Mouse 5";

    // Common keyboard keys
    if (keyCode == VK_BACK) return "Backspace";
    if (keyCode == VK_TAB) return "Tab";
    if (keyCode == VK_RETURN) return "Enter";
    if (keyCode == VK_SHIFT) return "Shift";
    if (keyCode == VK_CONTROL) return "Ctrl";
    if (keyCode == VK_MENU) return "Alt";
    if (keyCode == VK_PAUSE) return "Pause";
    if (keyCode == VK_CAPITAL) return "Caps Lock";
    if (keyCode == VK_ESCAPE) return "Escape";
    if (keyCode == VK_SPACE) return "Space";
    if (keyCode == VK_PRIOR) return "Page Up";
    if (keyCode == VK_NEXT) return "Page Down";
    if (keyCode == VK_END) return "End";
    if (keyCode == VK_HOME) return "Home";
    if (keyCode == VK_LEFT) return "Left";
    if (keyCode == VK_UP) return "Up";
    if (keyCode == VK_RIGHT) return "Right";
    if (keyCode == VK_DOWN) return "Down";
    if (keyCode == VK_INSERT) return "Insert";
    if (keyCode == VK_DELETE) return "Delete";

    // Number keys
    if (keyCode >= '0' && keyCode <= '9') {
        return std::string(1, (char)keyCode);
    }

    // Letter keys
    if (keyCode >= 'A' && keyCode <= 'Z') {
        return std::string(1, (char)keyCode);
    }

    // Function keys
    if (keyCode >= VK_F1 && keyCode <= VK_F24) {
        return "F" + std::to_string(keyCode - VK_F1 + 1);
    }

    // Numpad keys
    if (keyCode >= VK_NUMPAD0 && keyCode <= VK_NUMPAD9) {
        return "Num " + std::to_string(keyCode - VK_NUMPAD0);
    }

    // Other keys
    if (keyCode == VK_MULTIPLY) return "Num *";
    if (keyCode == VK_ADD) return "Num +";
    if (keyCode == VK_SUBTRACT) return "Num -";
    if (keyCode == VK_DECIMAL) return "Num .";
    if (keyCode == VK_DIVIDE) return "Num /";

    // If we don't have a specific mapping, return the hex code
    char buffer[16];
    sprintf_s(buffer, "Key 0x%02X", keyCode);
    return buffer;
}

bool InputManager::IsValidKey(int keyCode) {
    // Invalid keys
    if (keyCode == 0) return false;

    // Valid key ranges
    // Mouse buttons
    if (keyCode >= VK_LBUTTON && keyCode <= VK_XBUTTON2) return true;

    // Common keyboard keys
    if (keyCode == VK_BACK || keyCode == VK_TAB || keyCode == VK_RETURN ||
        keyCode == VK_SHIFT || keyCode == VK_CONTROL || keyCode == VK_MENU ||
        keyCode == VK_PAUSE || keyCode == VK_CAPITAL || keyCode == VK_ESCAPE ||
        keyCode == VK_SPACE || keyCode == VK_PRIOR || keyCode == VK_NEXT ||
        keyCode == VK_END || keyCode == VK_HOME || keyCode == VK_LEFT ||
        keyCode == VK_UP || keyCode == VK_RIGHT || keyCode == VK_DOWN ||
        keyCode == VK_INSERT || keyCode == VK_DELETE) return true;

    // Number keys
    if (keyCode >= '0' && keyCode <= '9') return true;

    // Letter keys
    if (keyCode >= 'A' && keyCode <= 'Z') return true;

    // Function keys
    if (keyCode >= VK_F1 && keyCode <= VK_F24) return true;

    // Numpad keys
    if ((keyCode >= VK_NUMPAD0 && keyCode <= VK_NUMPAD9) ||
        keyCode == VK_MULTIPLY || keyCode == VK_ADD ||
        keyCode == VK_SUBTRACT || keyCode == VK_DECIMAL ||
        keyCode == VK_DIVIDE) return true;

    return false;
}

KeyState InputManager::GetKeyState(int keyCode) {
    if (keyCode == 0 || keyStates.find(keyCode) == keyStates.end()) {
        return KeyState(); // Return a default state for invalid keys
    }

    return keyStates[keyCode];
}

void InputManager::DebugKey(int keyCode) {
    if (!debugMode || keyCode == 0) return;

    KeyState state = GetKeyState(keyCode);
    std::cout << "Key: " << GetKeyName(keyCode) << " (" << keyCode << ") - "
              << "Down: " << (state.isDown ? "Yes" : "No") << ", "
              << "Just Pressed: " << (state.justPressed ? "Yes" : "No") << ", "
              << "Just Released: " << (state.justReleased ? "Yes" : "No") << std::endl;
}

void InputManager::DebugAllKeys() {
    if (!debugMode) return;

    std::cout << "=== Monitored Keys (" << monitoredKeys.size() << ") ===" << std::endl;
    for (int keyCode : monitoredKeys) {
        DebugKey(keyCode);
    }
    std::cout << "=========================" << std::endl;
}
