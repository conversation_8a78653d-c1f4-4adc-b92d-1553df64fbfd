#pragma once
#include <Windows.h>
#include "../GameClass/GameSettings.h"

// Main settings struct to store all game settings
struct SettingsStruct {
    // ESP Settings
    struct EspSettings {
        bool Enable = true;
        // Player settings
        Playerss Player;
        // Item settings
        Itemss Item;
    } Esp;

    // Aimbot Settings
    struct AimSettings {
        Aimbots AimbotConfig;
        WeaponSettings WeaponConfigs;
        Triggerbots TriggerConfig;
    } Aim;

    // Colors
    struct ColorSettings {
        PlayersColorss PlayerColors;
        AimbotColorss AimbotColors;
    } Colors;

    // Radar
    struct RadarSettings {
        radars RadarConfig;
    } Radar;

    // Key Bindings
    struct KeyBindings {
        Keyss Keys;
    } Keys;

    // Configs
    struct ConfigSettings {
        ConfigsMenus ConfigMenu;
    } Config;
};

// Global settings instance
extern SettingsStruct Settings;

// Helper functions for settings management
namespace SettingsHelper {
    // Sync settings between struct and global variables (call every frame)
    void SyncSettings();

    // Initialize settings with defaults
    void InitializeSettings();

    // Save settings to file
    bool SaveSettings();

    // Load settings from file
    bool LoadSettings();

    // Reset settings to defaults
    void ResetSettings();
}