#include "Config.h"
#include <thread>
#include <filesystem>
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <sstream>
#include <ShlObj.h>
#include <algorithm>
#include "../../Utils.h"
#include "../../../Menu UI/ImGui/imgui.h"
#include "../../../Menu UI/ImGui/imgui_internal.h"
#include "../../../Menu UI/Framwork/GUI.h"
#include "../Hotkeys/HotkeySystem.h"

using namespace std;

// Global instances
NotificationManager g_NotificationManager;
ConfigSnapshot g_ConfigSnapshot;
NotificationSettings g_NotificationSettings;

// Global instance of the config system
ConfigSystem g_ConfigSystem;

// Find an existing change by category and name
int NotificationManager::FindExistingChange(const std::string& category, const std::string& name) {
    for (int i = 0; i < m_changes.size(); i++) {
        if (m_changes[i].category == category && m_changes[i].name == name) {
            return i;
        }
    }
    return -1; // Not found
}

// Update change time for an existing entry
void NotificationManager::UpdateChangeTime(const std::string& category, const std::string& name) {
    int index = FindExistingChange(category, name);
    if (index >= 0) {
        m_changes[index].changeTime = ImGui::GetTime();
        m_notificationStartTime = ImGui::GetTime(); // Reset notification timer
        m_notificationsActive = true;
    }
}

// Add a boolean change
void NotificationManager::AddChange(const std::string& category, const std::string& name, bool oldValue, bool newValue) {
    if (oldValue == newValue) return; // Don't track if no change
    if (!g_NotificationSettings.Enable) return; // Skip if notifications are disabled

    int existingIndex = FindExistingChange(category, name);
    if (existingIndex >= 0) {
        // Update existing change
        SettingChange& change = m_changes[existingIndex];
        change.boolChange.oldValue = oldValue;
        change.boolChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();

        // Reset notification timer
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    } else {
        // Add new change
        SettingChange change;
        change.category = category;
        change.name = name;
        change.type = ChangeType::Enable;
        change.boolChange.oldValue = oldValue;
        change.boolChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();

        m_changes.push_back(change);

        // Reset notification timer
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }
}

// Add a value change
void NotificationManager::AddChange(const std::string& category, const std::string& name, float oldValue, float newValue) {
    if (abs(oldValue - newValue) < 0.001f) return; // Don't track if negligible change
    if (!g_NotificationSettings.Enable) return; // Skip if notifications are disabled

    int existingIndex = FindExistingChange(category, name);
    if (existingIndex >= 0) {
        // Update existing change
        SettingChange& change = m_changes[existingIndex];
        change.valueChange.oldValue = oldValue;
        change.valueChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();

        // Reset notification timer
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    } else {
        // Add new change
        SettingChange change;
        change.category = category;
        change.name = name;
        change.type = ChangeType::Value;
        change.valueChange.oldValue = oldValue;
        change.valueChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();

        m_changes.push_back(change);

        // Reset notification timer
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }
}

// Add a color change
void NotificationManager::AddChange(const std::string& category, const std::string& name, float oldColor[3], float newColor[3]) {
    // Check if colors are actually different
    if (abs(oldColor[0] - newColor[0]) < 0.001f &&
        abs(oldColor[1] - newColor[1]) < 0.001f &&
        abs(oldColor[2] - newColor[2]) < 0.001f) {
        return;
    }

    if (!g_NotificationSettings.Enable) return; // Skip if notifications are disabled

    int existingIndex = FindExistingChange(category, name);
    if (existingIndex >= 0) {
        // Update existing change
        SettingChange& change = m_changes[existingIndex];
        for (int i = 0; i < 3; i++) {
            change.colorChange.oldColor[i] = oldColor[i];
            change.colorChange.newColor[i] = newColor[i];
        }
        change.changeTime = ImGui::GetTime();

        // Reset notification timer
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    } else {
        // Add new change
        SettingChange change;
        change.category = category;
        change.name = name;
        change.type = ChangeType::Color;

        for (int i = 0; i < 3; i++) {
            change.colorChange.oldColor[i] = oldColor[i];
            change.colorChange.newColor[i] = newColor[i];
        }
        change.changeTime = ImGui::GetTime();

        m_changes.push_back(change);

        // Reset notification timer
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }
}

void NotificationManager::ClearChanges() {
    m_changes.clear();
    m_notificationsActive = false;
}

const std::vector<SettingChange>& NotificationManager::GetChanges() const {
    return m_changes;
}

// Main notification rendering function
void NotificationManager::ShowNotifications() {
    if (m_changes.empty() || !g_NotificationSettings.Enable) return;

    // If not yet active, set start time
    if (!m_notificationsActive) {
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }

    // Calculate elapsed time
    float elapsedTime = ImGui::GetTime() - m_notificationStartTime;

    // Check if notification time is up
    if (elapsedTime > g_NotificationSettings.DisplayDuration) {
        ClearChanges();
        return;
    }

    // Calculate animation progress and alpha
    float progress = elapsedTime / g_NotificationSettings.DisplayDuration;
    float alpha = 1.0f;

    // Fade in/out animations
    if (progress < 0.1f) { // Fade in during first 10%
        alpha = progress / 0.1f;
    }
    else if (progress > 0.8f) { // Fade out during last 20%
        alpha = (1.0f - progress) / 0.2f;
    }

    // Get screen size from ImGui
    ImVec2 screenSize = ImGui::GetIO().DisplaySize;

    // Set window position to top left
    ImVec2 windowPos(
        20.0f,  // 20 pixels from left edge
        20.0f   // 20 pixels from top edge
    );

    // Modern color scheme
    ImColor enabledColor = ImColor(0.2f, 0.8f, 0.2f, 0.8f * alpha); // Green for enabled
    ImColor disabledColor = ImColor(0.2f, 0.2f, 0.8f, 0.8f * alpha); // Red for disabled
    ImColor bgColor = ImColor(0.08f, 0.08f, 0.08f, 0.95f * alpha); // Dark background
    ImColor headerBgColor = ImColor(0.13f, 0.14f, 0.15f, 0.95f * alpha); // Slightly lighter for header
    ImColor accentColor = ImColor(accent_color[2], accent_color[1], accent_color[0], alpha);

    // Begin notification window
    ImGui::SetNextWindowPos(windowPos);
    ImGui::SetNextWindowSize(ImVec2(320, 0)); // Auto height, slightly wider
    ImGui::SetNextWindowBgAlpha(bgColor.Value.w);

    ImGui::PushStyleVar(ImGuiStyleVar_Alpha, alpha);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(12, 12));
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(8, 6));
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(8, 6));

    ImGui::PushStyleColor(ImGuiCol_WindowBg, ImGui::ColorConvertFloat4ToU32(bgColor));
    ImGui::PushStyleColor(ImGuiCol_Border, ImGui::ColorConvertFloat4ToU32(ImColor(accent_color[2], accent_color[1], accent_color[0], 0.3f)));

    ImGui::Begin("##Notifications", nullptr,
        ImGuiWindowFlags_NoTitleBar |
        ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoMove |
        ImGuiWindowFlags_NoSavedSettings |
        ImGuiWindowFlags_AlwaysAutoResize |
        ImGuiWindowFlags_NoFocusOnAppearing
    );

    // Use RenderSectionHeader for title
    pGUi.RenderSectionHeader("Settings Changed", 0, 0);

    ImGui::Dummy(ImVec2(0, 5)); // Space after header

    // Group changes by category
    std::unordered_map<std::string, std::vector<const SettingChange*>> categorizedChanges;
    for (const auto& change : m_changes) {
        categorizedChanges[change.category].push_back(&change);
    }

    // Draw each category of changes
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    for (const auto& category : categorizedChanges) {
        // Modern styled category header with blue accent
        ImColor headerBgColor = ImColor(0.05f, 0.05f, 0.08f, 0.9f * alpha); // Subtle dark background
        ImColor headerAccentColor = ImColor(accentColor); // Blue accent color

        // Category header with shadow rect
        ImVec2 headerPos = ImGui::GetCursorScreenPos();
        ImVec2 headerSize = ImVec2(ImGui::GetContentRegionAvail().x, 32.0f);

        // Header background with subtle gradient
        drawList->AddRectFilled(
            headerPos,
            ImVec2(headerPos.x + headerSize.x, headerPos.y + headerSize.y),
            headerBgColor,
            4.0f
        );

        // Add subtle border
        drawList->AddRect(
            headerPos,
            ImVec2(headerPos.x + headerSize.x, headerPos.y + headerSize.y),
            ImColor(1.0f, 1.0f, 1.0f, 0.05f * alpha),
            4.0f, 0, 1.0f
        );

        // Add shadow for depth
        drawList->AddShadowRect(
            headerPos,
            ImVec2(headerPos.x + headerSize.x, headerPos.y + headerSize.y),
            ImColor(0.0f, 0.0f, 0.0f, 0.4f * alpha),
            12.0f,
            ImVec2(0, 2),
            ImDrawFlags_RoundCornersAll
        );

        // Blue horizontal bottom line with glow effect
        const float line_height = 2.0f;
        const float line_padding = 8.0f;
        ImVec2 line_start = ImVec2(headerPos.x + line_padding, headerPos.y + headerSize.y - 2);
        ImVec2 line_end = ImVec2(headerPos.x + headerSize.x - line_padding, headerPos.y + headerSize.y - 2 + line_height);

        // Shadow/glow for horizontal line (blue)
        drawList->AddShadowRect(
            line_start,
            line_end,
            headerAccentColor,
            20.f,
            ImVec2(0, 3),
            ImDrawFlags_RoundCornersAll,
            60.f
        );

        // Main blue glow line
        drawList->AddRectFilled(
            line_start,
            line_end,
            headerAccentColor,
            360.f,
            ImDrawFlags_RoundCornersAll
        );

        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]); // Use the main font

        // Center header text with category name
        ImVec2 textSize = ImGui::CalcTextSize(category.first.c_str());
        ImVec2 textPos = ImVec2(
            headerPos.x + (headerSize.x - textSize.x) * 0.5f,
            headerPos.y + (headerSize.y - textSize.y) * 0.5f - 1
        );

        // Text shadow for better readability
        drawList->AddText(
            ImVec2(textPos.x + 1, textPos.y + 1),
            ImColor(0.0f, 0.0f, 0.0f, 0.5f * alpha),
            category.first.c_str()
        );

        // Main text
        drawList->AddText(
            textPos,
            ImColor(1.0f, 1.0f, 1.0f, 0.95f * alpha),
            category.first.c_str()
        );

        ImGui::PopFont();
        ImGui::Dummy(ImVec2(0, headerSize.y)); // Space for the header we just drew
        ImGui::Dummy(ImVec2(0, 6)); // Space after header

        // Draw changes for this category
        for (const auto* change : category.second) {
            ImGui::Indent(10.0f);

            switch (change->type) {
                case ChangeType::Enable: {
                    // Setting name
                    ImGui::Text("%s:", change->name.c_str());

                    // Container for the status indicators
                    ImGui::BeginGroup();

                    // Calculate animation values - use similar pattern as requested
                    float time = ImGui::GetTime() - change->changeTime;
                    float line_opacity = ImLerp(0.0f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    float element_opacity = ImLerp(0.0f, 0.04f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

                    // Draw "from" box with modern design
                    ImVec2 boxStart = ImGui::GetCursorScreenPos();
                    ImVec2 boxSize(80.0f, 24.0f);
                    ImColor fromColor = change->boolChange.oldValue ? enabledColor : disabledColor;
                    ImColor borderColor = ImColor(255, 255, 255, 10);

                    // Background rectangle
                    drawList->AddRectFilled(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImColor(1.0f, 1.0f, 1.0f, element_opacity),
                        3.0f
                    );

                    // Border
                    drawList->AddRect(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImGui::ColorConvertFloat4ToU32(borderColor),
                        3.0f, 0, 1.0f
                    );

                    // Center text
                    ImVec2 fromTextSize = ImGui::CalcTextSize(change->boolChange.oldValue ? "Enabled" : "Disabled");
                    ImVec2 label_pos = ImVec2(
                        boxStart.x + (boxSize.x - fromTextSize.x) * 0.5f,
                        boxStart.y + (boxSize.y - fromTextSize.y) * 0.5f
                    );

                    // Text with animated opacity
                    float text_opacity = ImLerp(0.3f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    drawList->AddText(
                        label_pos,
                        ImColor(1.0f, 1.0f, 1.0f, text_opacity),
                        change->boolChange.oldValue ? "Enabled" : "Disabled"
                    );

                    // Horizontal bottom line
                    const float line_height = 1.0f;
                    const float line_padding = 10.0f;
                    ImVec2 line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    ImVec2 line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);

                    // Shadow for horizontal line
                    drawList->AddShadowRect(
                        line_start,
                        line_end,
                        fromColor,
                        25.f,
                        ImVec2(0, 2),
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight,
                        60.f
                    );

                    // Main glow line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        fromColor,
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    // Subtle background line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f),
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    ImGui::Dummy(boxSize); // Space for the box

                    // Arrow with animation based on UI theme
                    ImGui::SameLine();
                    ImVec2 arrowStart = ImGui::GetCursorScreenPos();
                    ImVec2 arrowSize = ImVec2(40.0f, boxSize.y);

                    // Draw animated arrow
                    float centerY = arrowStart.y + arrowSize.y * 0.5f;
                    float arrowWidth = arrowSize.x - 10.0f;
                    float head_size = 6.0f;
                    float line_thickness = 2.0f;

                    // Main line with glow (horizontal part)
                    ImVec2 lineStart = ImVec2(arrowStart.x + 5.0f, centerY);
                    ImVec2 lineEnd = ImVec2(arrowStart.x + arrowWidth, centerY);

                    // Shadow glow for line
                    drawList->AddShadowRect(
                        ImVec2(lineStart.x, lineStart.y - line_thickness * 0.5f),
                        ImVec2(lineEnd.x, lineEnd.y + line_thickness * 0.5f),
                        accentColor,
                        8.0f,
                        ImVec2(0, 0),
                        ImDrawFlags_RoundCornersAll
                    );

                    // Main horizontal line
                    drawList->AddLine(lineStart, lineEnd, accentColor, line_thickness);

                    // Draw arrow head (triangle)
                    ImVec2 head_left = ImVec2(lineEnd.x - head_size, centerY - head_size);
                    ImVec2 head_right = ImVec2(lineEnd.x - head_size, centerY + head_size);
                    ImVec2 head_tip = ImVec2(lineEnd.x + 2.0f, centerY);

                    // Calculate pulsing animation for arrow head
                    float pulse = sinf(time * 3.0f) * 0.2f + 1.0f; // Pulsing between 0.8 and 1.2 times size

                    // Adjust arrow head points with pulse
                    head_left = ImVec2(
                        lineEnd.x - head_size * pulse,
                        centerY - head_size * pulse
                    );
                    head_right = ImVec2(
                        lineEnd.x - head_size * pulse,
                        centerY + head_size * pulse
                    );
                    head_tip = ImVec2(
                        lineEnd.x + 2.0f * pulse,
                        centerY
                    );

                    // Add glow to arrow head
                    ImVec2 headPoints[3] = { head_left, head_tip, head_right };
                    drawList->AddConvexPolyFilled(headPoints, 3, accentColor);

                    // Add shadow/glow to arrow head
                    ImVec2 shadowOffset(1.0f, 1.0f);
                    float shadowSize = 8.0f;
                    ImColor shadowColor = ImColor(
                        accentColor.Value.x,
                        accentColor.Value.y,
                        accentColor.Value.z,
                        accentColor.Value.w * 0.5f
                    );

                    drawList->AddCircle(head_tip, shadowSize, shadowColor, 0, 2.0f);

                    ImGui::Dummy(arrowSize); // Space for the arrow

                    // Draw "to" box with modern design
                    ImGui::SameLine();
                    boxStart = ImGui::GetCursorScreenPos();
                    ImColor toColor = change->boolChange.newValue ? enabledColor : disabledColor;

                    // Background rectangle
                    drawList->AddRectFilled(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImColor(1.0f, 1.0f, 1.0f, element_opacity),
                        3.0f
                    );

                    // Border
                    drawList->AddRect(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImGui::ColorConvertFloat4ToU32(borderColor),
                        3.0f, 0, 1.0f
                    );

                    // Center text
                    ImVec2 toTextSize = ImGui::CalcTextSize(change->boolChange.newValue ? "Enabled" : "Disabled");
                    label_pos = ImVec2(
                        boxStart.x + (boxSize.x - toTextSize.x) * 0.5f,
                        boxStart.y + (boxSize.y - toTextSize.y) * 0.5f
                    );

                    // Text with animated opacity
                    text_opacity = change->boolChange.newValue ? 1.0f : 0.5f;
                    drawList->AddText(
                        label_pos,
                        ImColor(1.0f, 1.0f, 1.0f, text_opacity),
                        change->boolChange.newValue ? "Enabled" : "Disabled"
                    );

                    // Horizontal bottom line
                    line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);

                    // Shadow for horizontal line
                    drawList->AddShadowRect(
                        line_start,
                        line_end,
                        toColor,
                        25.f,
                        ImVec2(0, 2),
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight,
                        60.f
                    );

                    // Main glow line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        toColor,
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    // Subtle background line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f),
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    ImGui::Dummy(boxSize); // Space for the box

                    ImGui::EndGroup();
                    ImGui::Dummy(ImVec2(0, 6)); // Spacing after the group
                    break;
                }

                case ChangeType::Value: {
                    // Value changed
                    ImGui::Text("%s:", change->name.c_str());

                    // Container for the value indicators
                    ImGui::BeginGroup();

                    // Format values to strings
                    char oldValueText[32], newValueText[32];
                    snprintf(oldValueText, sizeof(oldValueText), "%.2f", change->valueChange.oldValue);
                    snprintf(newValueText, sizeof(newValueText), "%.2f", change->valueChange.newValue);

                    // Calculate animation values
                    float time = ImGui::GetTime() - change->changeTime;
                    float line_opacity = ImLerp(0.0f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    float element_opacity = ImLerp(0.0f, 0.04f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

                    // Draw "from" box with modern design
                    ImVec2 boxStart = ImGui::GetCursorScreenPos();
                    ImVec2 boxSize(80.0f, 24.0f);
                    ImColor fromColor = ImColor(0.7f, 0.3f, 0.2f, 0.8f * alpha); // Orange-red for previous value
                    ImColor borderColor = ImColor(255, 255, 255, 10);

                    // Background rectangle
                    drawList->AddRectFilled(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImColor(1.0f, 1.0f, 1.0f, element_opacity),
                        3.0f
                    );

                    // Border
                    drawList->AddRect(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImGui::ColorConvertFloat4ToU32(borderColor),
                        3.0f, 0, 1.0f
                    );

                    // Center text
                    ImVec2 fromTextSize = ImGui::CalcTextSize(oldValueText);
                    ImVec2 label_pos = ImVec2(
                        boxStart.x + (boxSize.x - fromTextSize.x) * 0.5f,
                        boxStart.y + (boxSize.y - fromTextSize.y) * 0.5f
                    );

                    // Text with animated opacity
                    float text_opacity = ImLerp(0.3f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    drawList->AddText(
                        label_pos,
                        ImColor(1.0f, 1.0f, 1.0f, text_opacity),
                        oldValueText
                    );

                    // Horizontal bottom line
                    const float line_height = 1.0f;
                    const float line_padding = 10.0f;
                    ImVec2 line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    ImVec2 line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);

                    // Shadow for horizontal line
                    drawList->AddShadowRect(
                        line_start,
                        line_end,
                        fromColor,
                        25.f,
                        ImVec2(0, 2),
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight,
                        60.f
                    );

                    // Main glow line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        fromColor,
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    // Subtle background line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f),
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    ImGui::Dummy(boxSize); // Space for the box

                    // Draw arrow same as for boolean changes
                    ImGui::SameLine();
                    ImVec2 arrowStart = ImGui::GetCursorScreenPos();
                    ImVec2 arrowSize = ImVec2(40.0f, boxSize.y);

                    // Draw animated arrow
                    float centerY = arrowStart.y + arrowSize.y * 0.5f;
                    float arrowWidth = arrowSize.x - 10.0f;
                    float head_size = 6.0f;
                    float line_thickness = 2.0f;

                    // Main line with glow (horizontal part)
                    ImVec2 lineStart = ImVec2(arrowStart.x + 5.0f, centerY);
                    ImVec2 lineEnd = ImVec2(arrowStart.x + arrowWidth, centerY);

                    // Shadow glow for line
                    drawList->AddShadowRect(
                        ImVec2(lineStart.x, lineStart.y - line_thickness * 0.5f),
                        ImVec2(lineEnd.x, lineEnd.y + line_thickness * 0.5f),
                        accentColor,
                        8.0f,
                        ImVec2(0, 0),
                        ImDrawFlags_RoundCornersAll
                    );

                    // Main horizontal line
                    drawList->AddLine(lineStart, lineEnd, accentColor, line_thickness);

                    // Draw arrow head (triangle)
                    ImVec2 head_left = ImVec2(lineEnd.x - head_size, centerY - head_size);
                    ImVec2 head_right = ImVec2(lineEnd.x - head_size, centerY + head_size);
                    ImVec2 head_tip = ImVec2(lineEnd.x + 2.0f, centerY);

                    // Adjust arrow head points with pulse
                    head_left = ImVec2(
                        lineEnd.x - head_size,
                        centerY - head_size
                    );
                    head_right = ImVec2(
                        lineEnd.x - head_size,
                        centerY + head_size
                    );
                    head_tip = ImVec2(
                        lineEnd.x + 2.0f,
                        centerY
                    );

                    // Add glow to arrow head
                    ImVec2 headPoints[3] = { head_left, head_tip, head_right };
                    drawList->AddConvexPolyFilled(headPoints, 3, accentColor);

                    ImGui::Dummy(arrowSize); // Space for the arrow

                    // Draw "to" box with modern design
                    ImGui::SameLine();
                    boxStart = ImGui::GetCursorScreenPos();
                    ImColor toColor = ImColor(0.2f, 0.7f, 0.3f, 0.8f * alpha); // Green for new value

                    // Background rectangle
                    drawList->AddRectFilled(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImColor(1.0f, 1.0f, 1.0f, element_opacity),
                        3.0f
                    );

                    // Border
                    drawList->AddRect(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        ImGui::ColorConvertFloat4ToU32(borderColor),
                        3.0f, 0, 1.0f
                    );

                    // Center text
                    ImVec2 toTextSize = ImGui::CalcTextSize(newValueText);
                    label_pos = ImVec2(
                        boxStart.x + (boxSize.x - toTextSize.x) * 0.5f,
                        boxStart.y + (boxSize.y - toTextSize.y) * 0.5f
                    );

                    // Text with animated opacity
                    text_opacity = 1.0f; // Full opacity for new value
                    drawList->AddText(
                        label_pos,
                        ImColor(1.0f, 1.0f, 1.0f, text_opacity),
                        newValueText
                    );

                    // Horizontal bottom line
                    line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);

                    // Shadow for horizontal line
                    drawList->AddShadowRect(
                        line_start,
                        line_end,
                        toColor,
                        25.f,
                        ImVec2(0, 2),
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight,
                        60.f
                    );

                    // Main glow line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        toColor,
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    // Subtle background line
                    drawList->AddRectFilled(
                        line_start,
                        line_end,
                        ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f),
                        360.f,
                        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
                    );

                    ImGui::Dummy(boxSize); // Space for the box

                    ImGui::EndGroup();
                    ImGui::Dummy(ImVec2(0, 6)); // Spacing after the group
                    break;
                }

                case ChangeType::Color: {
                    // Color changed
                    ImGui::Text("%s:", change->name.c_str());

                    // Container for the color indicators
                    ImGui::BeginGroup();

                    // Draw "from" color box
                    ImVec2 boxStart = ImGui::GetCursorScreenPos();
                    ImVec2 boxSize(40.0f, 24.0f);
                    ImColor fromColor = ImColor(
                        change->colorChange.oldColor[0],
                        change->colorChange.oldColor[1],
                        change->colorChange.oldColor[2],
                        1.0f * alpha
                    );

                    // Shadow for "from" color box
                    drawList->AddShadowRect(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        fromColor,
                        6.0f,
                        ImVec2(0, 0),
                        ImDrawFlags_RoundCornersAll
                    );

                    // "From" color box
                    drawList->AddRectFilled(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        fromColor,
                        6.0f
                    );

                    ImGui::Dummy(boxSize); // Space for the box

                    // Arrow with animation based on UI theme
                    ImGui::SameLine();
                    ImVec2 arrowStart = ImGui::GetCursorScreenPos();
                    ImVec2 arrowSize = ImVec2(40.0f, boxSize.y);

                    // Calculate animation parameters
                    float time = ImGui::GetTime() - change->changeTime;
                    float animSpeed = 0.07f * (1.0f - ImGui::GetIO().DeltaTime);

                    // Draw animated arrow - more elaborate than before
                    float centerY = arrowStart.y + arrowSize.y * 0.5f;
                    float arrowWidth = arrowSize.x - 10.0f;
                    float head_size = 6.0f;
                    float line_thickness = 2.0f;

                    // Main line with glow (horizontal part)
                    ImVec2 lineStart = ImVec2(arrowStart.x + 5.0f, centerY);
                    ImVec2 lineEnd = ImVec2(arrowStart.x + arrowWidth, centerY);

                    // Shadow glow for line
                    drawList->AddShadowRect(
                        ImVec2(lineStart.x, lineStart.y - line_thickness * 0.5f),
                        ImVec2(lineEnd.x, lineEnd.y + line_thickness * 0.5f),
                        accentColor,
                        8.0f,
                        ImVec2(0, 0),
                        ImDrawFlags_RoundCornersAll
                    );

                    // Main horizontal line
                    drawList->AddLine(lineStart, lineEnd, accentColor, line_thickness);

                    // Draw arrow head (triangle)
                    ImVec2 head_left = ImVec2(lineEnd.x - head_size, centerY - head_size);
                    ImVec2 head_right = ImVec2(lineEnd.x - head_size, centerY + head_size);
                    ImVec2 head_tip = ImVec2(lineEnd.x + 2.0f, centerY);

                    // Calculate pulsing animation for arrow head
                    float pulse = sinf(time * 3.0f) * 0.2f + 1.0f; // Pulsing between 0.8 and 1.2 times size

                    // Adjust arrow head points with pulse
                    head_left = ImVec2(
                        lineEnd.x - head_size * pulse,
                        centerY - head_size * pulse
                    );
                    head_right = ImVec2(
                        lineEnd.x - head_size * pulse,
                        centerY + head_size * pulse
                    );
                    head_tip = ImVec2(
                        lineEnd.x + 2.0f * pulse,
                        centerY
                    );

                    // Add glow to arrow head
                    ImVec2 headPoints[3] = { head_left, head_tip, head_right };
                    drawList->AddConvexPolyFilled(headPoints, 3, accentColor);

                    // Add shadow/glow to arrow head
                    ImVec2 shadowOffset(1.0f, 1.0f);
                    float shadowSize = 8.0f;
                    ImColor shadowColor = ImColor(
                        accentColor.Value.x,
                        accentColor.Value.y,
                        accentColor.Value.z,
                        accentColor.Value.w * 0.5f
                    );

                    drawList->AddCircle(head_tip, shadowSize, shadowColor, 0, 2.0f);

                    ImGui::Dummy(arrowSize); // Space for the arrow

                    // Draw "to" color box
                    ImGui::SameLine();
                    boxStart = ImGui::GetCursorScreenPos();
                    ImColor toColor = ImColor(
                        change->colorChange.newColor[0],
                        change->colorChange.newColor[1],
                        change->colorChange.newColor[2],
                        1.0f * alpha
                    );

                    // Shadow for "to" color box
                    drawList->AddShadowRect(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        toColor,
                        6.0f,
                        ImVec2(0, 0),
                        ImDrawFlags_RoundCornersAll
                    );

                    // "To" color box
                    drawList->AddRectFilled(
                        boxStart,
                        ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y),
                        toColor,
                        6.0f
                    );

                    ImGui::Dummy(boxSize); // Space for the box

                    ImGui::EndGroup();
                    ImGui::Dummy(ImVec2(0, 6)); // Spacing after the group
                    break;
                }
            }

            ImGui::Unindent(10.0f);
        }

        ImGui::Dummy(ImVec2(0, 6)); // Add spacing between categories
    }

    // Add space before progress bar
    ImGui::Dummy(ImVec2(0, 8));

    // Draw progress bar at the bottom
    ImVec2 windowSize = ImGui::GetWindowSize();
    ImVec2 barSize(ImGui::GetContentRegionAvail().x, 5.0f);
    ImVec2 barStart = ImGui::GetCursorScreenPos();
    ImVec2 barEnd = ImVec2(barStart.x + barSize.x, barStart.y + barSize.y);

    // Progress bar background with glow
    drawList->AddShadowRect(
        barStart,
        barEnd,
        ImColor(0.2f, 0.2f, 0.2f, 0.8f * alpha),
        10.0f, // Blur factor for glow
        ImVec2(0, 0),
        ImDrawFlags_RoundCornersAll
    );

    // Draw progress bar fill with accent color and glow
    float fillWidth = barSize.x * (1.0f - progress);
    ImVec2 fillEnd = ImVec2(barStart.x + fillWidth, barEnd.y);

    // Glow for progress bar fill
    drawList->AddShadowRect(
        barStart,
        fillEnd,
        ImColor(accent_color[2], accent_color[1], accent_color[0], 0.9f * alpha),
        8.0f, // Blur factor for glow
        ImVec2(0, 0),
        ImDrawFlags_RoundCornersAll
    );

    // The actual progress bar fill
    drawList->AddRectFilled(
        barStart,
        fillEnd,
        ImColor(accent_color[2], accent_color[1], accent_color[0], 1.0f * alpha),
        4.0f
    );

    ImGui::Dummy(ImVec2(0, barSize.y + 4.0f)); // Space after progress bar

    ImGui::End();

    // Pop all styles
    ImGui::PopStyleColor(2); // Window and border colors
    ImGui::PopStyleVar(5);  // Style variables
}

// ConfigSnapshot implementation
void ConfigSnapshot::CaptureSettings() {
    // Copy all settings to our snapshot
    aimbot = Aimbot;
    triggerbot = Triggerbot;
    items = Items;
    players = Players;
    keys = Keys;
    aimbotColors = AimbotColors;
    playersColors = PlayersColors;
    radar = Radar;
    configsMenu = ConfigsMenu;
}

void ConfigSnapshot::DetectChanges() {
    // Check for Aimbot setting changes
    g_NotificationManager.AddChange("Aimbot", "Enable", aimbot.Enable, Aimbot.Enable);
    g_NotificationManager.AddChange("Aimbot", "Save Target", aimbot.SaveTarget, Aimbot.SaveTarget);
    g_NotificationManager.AddChange("Aimbot", "Aim Lock", aimbot.AimLock, Aimbot.AimLock);
    g_NotificationManager.AddChange("Aimbot", "Visibility Check", aimbot.VisibilityCheck, Aimbot.VisibilityCheck);
    g_NotificationManager.AddChange("Aimbot", "Humanized Smooth", aimbot.HumanizedSmooth, Aimbot.HumanizedSmooth);
    g_NotificationManager.AddChange("Aimbot", "Weapon Only", aimbot.WeaponOnly, Aimbot.WeaponOnly);
    g_NotificationManager.AddChange("Aimbot", "Ignore Downed", aimbot.IgnoreDowned, Aimbot.IgnoreDowned);
    g_NotificationManager.AddChange("Aimbot", "Prediction", aimbot.Predict, Aimbot.Predict);
    g_NotificationManager.AddChange("Aimbot", "Player AI", aimbot.PlayerAi, Aimbot.PlayerAi);
    g_NotificationManager.AddChange("Aimbot", "FOV", aimbot.FOV, Aimbot.FOV);
    g_NotificationManager.AddChange("Aimbot", "Smooth", aimbot.Smooth, Aimbot.Smooth);
    g_NotificationManager.AddChange("Aimbot", "Humanized Smooth %", aimbot.HumanizedSmoothPercent, Aimbot.HumanizedSmoothPercent);
    g_NotificationManager.AddChange("Aimbot", "Max Distance", (float)aimbot.MaxDistance, (float)Aimbot.MaxDistance);
    g_NotificationManager.AddChange("Aimbot", "By Weapon", aimbot.ByWeapon, Aimbot.ByWeapon);
    g_NotificationManager.AddChange("Aimbot", "Draw FOV", aimbot.DrawFov, Aimbot.DrawFov);
    g_NotificationManager.AddChange("Aimbot", "Draw FOV Filled", aimbot.DrawFovFilled, Aimbot.DrawFovFilled);
    g_NotificationManager.AddChange("Aimbot", "Draw FOV Outline", aimbot.DrawFovOutline, Aimbot.DrawFovOutline);
    g_NotificationManager.AddChange("Aimbot", "Draw FOV RGB", aimbot.DrawFovRGB, Aimbot.DrawFovRGB);
    g_NotificationManager.AddChange("Aimbot", "Draw Crosshair", aimbot.DrawCrossHair, Aimbot.DrawCrossHair);
    g_NotificationManager.AddChange("Aimbot", "Crosshair Type", (float)aimbot.DrawCrossHairType, (float)Aimbot.DrawCrossHairType);
    g_NotificationManager.AddChange("Aimbot", "Draw Target", aimbot.DrawTarget, Aimbot.DrawTarget);
    g_NotificationManager.AddChange("Aimbot", "Target Type", (float)aimbot.DrawTargetType, (float)Aimbot.DrawTargetType);
    g_NotificationManager.AddChange("Aimbot", "Draw Thickness", aimbot.DrawThickness, Aimbot.DrawThickness);
    g_NotificationManager.AddChange("Aimbot", "Draw Size", aimbot.DrawSize, Aimbot.DrawSize);
    g_NotificationManager.AddChange("Aimbot", "Hitbox", (float)aimbot.HitBox, (float)Aimbot.HitBox);

    // If weapon-specific settings are enabled, check those as well
    if (Aimbot.ByWeapon) {
        for (const auto& weapon : weaponSettings.weaponAimSettings) {
            if (weapon.first == ConfigsMenu.selectedWeapon) {
                const auto& currentWeaponSettings = weapon.second;
                const auto& prevWeaponSettings = weaponSettings.weaponAimSettings[weapon.first];

                std::string category = "Weapon " + std::to_string(weapon.first);

                g_NotificationManager.AddChange(category, "Enable", prevWeaponSettings.Enable, currentWeaponSettings.Enable);
                g_NotificationManager.AddChange(category, "Save Target", prevWeaponSettings.SaveTarget, currentWeaponSettings.SaveTarget);
                g_NotificationManager.AddChange(category, "Aim Lock", prevWeaponSettings.AimLock, currentWeaponSettings.AimLock);
                g_NotificationManager.AddChange(category, "Visibility Check", prevWeaponSettings.VisibilityCheck, currentWeaponSettings.VisibilityCheck);
                g_NotificationManager.AddChange(category, "Humanized Smooth", prevWeaponSettings.HumanizedSmooth, currentWeaponSettings.HumanizedSmooth);
                g_NotificationManager.AddChange(category, "Ignore Downed", prevWeaponSettings.IgnoreDowned, currentWeaponSettings.IgnoreDowned);
                g_NotificationManager.AddChange(category, "Prediction", prevWeaponSettings.Predict, currentWeaponSettings.Predict);
                g_NotificationManager.AddChange(category, "FOV", prevWeaponSettings.FOV, currentWeaponSettings.FOV);
                g_NotificationManager.AddChange(category, "Smooth", prevWeaponSettings.Smooth, currentWeaponSettings.Smooth);
                g_NotificationManager.AddChange(category, "Max Distance", (float)prevWeaponSettings.MaxDistance, (float)currentWeaponSettings.MaxDistance);
            }
        }
    }

    // Check for Triggerbot setting changes
    g_NotificationManager.AddChange("Triggerbot", "Enable", triggerbot.Enable, Triggerbot.Enable);
    g_NotificationManager.AddChange("Triggerbot", "All Weapons", triggerbot.EnableAllWeapons, Triggerbot.EnableAllWeapons);
    g_NotificationManager.AddChange("Triggerbot", "Only Shotguns", triggerbot.EnableOnlyShutguns, Triggerbot.EnableOnlyShutguns);
    g_NotificationManager.AddChange("Triggerbot", "By Weapon", triggerbot.ByWeapon, Triggerbot.ByWeapon);
    g_NotificationManager.AddChange("Triggerbot", "Delay", (float)triggerbot.Delay, (float)Triggerbot.Delay);
    g_NotificationManager.AddChange("Triggerbot", "Max Distance", triggerbot.MaxDistance, Triggerbot.MaxDistance);

    // Player ESP settings
    g_NotificationManager.AddChange("ESP", "Players Enabled", players.Enable, Players.Enable);
    g_NotificationManager.AddChange("ESP", "Box", players.Box, Players.Box);
    g_NotificationManager.AddChange("ESP", "Box Type", (float)players.BoxType, (float)Players.BoxType);
    g_NotificationManager.AddChange("ESP", "Box Style 1", players.BoxStyle[0], Players.BoxStyle[0]);
    g_NotificationManager.AddChange("ESP", "Box Style 2", players.BoxStyle[1], Players.BoxStyle[1]);
    g_NotificationManager.AddChange("ESP", "Box Thickness", players.BoxThickness, Players.BoxThickness);
    g_NotificationManager.AddChange("ESP", "Box Rounding", players.BoxRounding, Players.BoxRounding);
    g_NotificationManager.AddChange("ESP", "Distance", players.Distance, Players.Distance);
    g_NotificationManager.AddChange("ESP", "Nickname", players.NickName, Players.NickName);
    g_NotificationManager.AddChange("ESP", "Platform", players.Platform, Players.Platform);
    g_NotificationManager.AddChange("ESP", "Skeleton", players.Skeleton, Players.Skeleton);
    g_NotificationManager.AddChange("ESP", "Skeleton Thickness", players.SkeletonThickness, Players.SkeletonThickness);
    g_NotificationManager.AddChange("ESP", "Head Circle", players.HeadCircle, Players.HeadCircle);
    g_NotificationManager.AddChange("ESP", "Head Circle Thickness", players.HeadCircleThickness, Players.HeadCircleThickness);
    g_NotificationManager.AddChange("ESP", "Player AI", players.PlayerAi, Players.PlayerAi);
    g_NotificationManager.AddChange("ESP", "Lines", players.Lines, Players.Lines);
    g_NotificationManager.AddChange("ESP", "Lines Position", (float)players.LinesPosition, (float)Players.LinesPosition);
    g_NotificationManager.AddChange("ESP", "Lines Thickness", players.LinesThickness, Players.LinesThickness);
    g_NotificationManager.AddChange("ESP", "Weapon", players.Weapon, Players.Weapon);
    g_NotificationManager.AddChange("ESP", "Weapon Rarity", players.WeaponRarity, Players.WeaponRarity);
    g_NotificationManager.AddChange("ESP", "Weapon Ammo", players.WeaponAmmo, Players.WeaponAmmo);
    g_NotificationManager.AddChange("ESP", "Max Distance", (float)players.MaxDistance, (float)Players.MaxDistance);
    g_NotificationManager.AddChange("ESP", "Font Size", players.FontSize, Players.FontSize);
    g_NotificationManager.AddChange("ESP", "Ignore Downed", players.IgnoreDowned, Players.IgnoreDowned);
    g_NotificationManager.AddChange("ESP", "Team Check", players.TeamCheck, Players.TeamCheck);
    g_NotificationManager.AddChange("ESP", "Kills", players.Kills, Players.Kills);
    g_NotificationManager.AddChange("ESP", "Level", players.Level, Players.Level);
    g_NotificationManager.AddChange("ESP", "Rank", players.Rank, Players.Rank);

    // Radar settings
    g_NotificationManager.AddChange("Radar", "Enable", radar.Enable, Radar.Enable);
    g_NotificationManager.AddChange("Radar", "Distance", radar.Distance, Radar.Distance);
    g_NotificationManager.AddChange("Radar", "Position X", (float)radar.PositionX, (float)Radar.PositionX);
    g_NotificationManager.AddChange("Radar", "Position Y", (float)radar.PositionY, (float)Radar.PositionY);
    g_NotificationManager.AddChange("Radar", "Circle Size", (float)radar.CirleSize, (float)Radar.CirleSize);
    g_NotificationManager.AddChange("Radar", "Rectangle Size", (float)radar.RectangleSize, (float)Radar.RectangleSize);
    g_NotificationManager.AddChange("Radar", "Visible Color", radar.VisibleColor, Radar.VisibleColor);
    g_NotificationManager.AddChange("Radar", "Closest Color", radar.ClosestColor, Radar.ClosestColor);
    g_NotificationManager.AddChange("Radar", "Aiming At Me Color", radar.AimingAtMeColor, Radar.AimingAtMeColor);
    g_NotificationManager.AddChange("Radar", "Max Distance", (float)radar.MaxDistance, (float)Radar.MaxDistance);
    g_NotificationManager.AddChange("Radar", "Distance Font Size", (float)radar.DistanceFontSize, (float)Radar.DistanceFontSize);
    g_NotificationManager.AddChange("Radar", "Radar Type", (float)radar.RadarType, (float)Radar.RadarType);

    // Items - Consumables
    g_NotificationManager.AddChange("Items-Consumable", "Enable", items.Consumable.Enable, Items.Consumable.Enable);
    g_NotificationManager.AddChange("Items-Consumable", "Name", items.Consumable.Name, Items.Consumable.Name);
    g_NotificationManager.AddChange("Items-Consumable", "Distance", items.Consumable.Distance, Items.Consumable.Distance);
    g_NotificationManager.AddChange("Items-Consumable", "Icons", items.Consumable.Icons, Items.Consumable.Icons);
    g_NotificationManager.AddChange("Items-Consumable", "Icon Size", items.Consumable.IconSize, Items.Consumable.IconSize);
    g_NotificationManager.AddChange("Items-Consumable", "Max Distance", (float)items.Consumable.MaxDistance, (float)Items.Consumable.MaxDistance);
    g_NotificationManager.AddChange("Items-Consumable", "Font Size", items.Consumable.FontSize, Items.Consumable.FontSize);
    g_NotificationManager.AddChange("Items-Consumable", "Bandages", items.Consumable.Bandages, Items.Consumable.Bandages);
    g_NotificationManager.AddChange("Items-Consumable", "Medkit", items.Consumable.Medkit, Items.Consumable.Medkit);
    g_NotificationManager.AddChange("Items-Consumable", "Small Shield", items.Consumable.SmallShieldPotion, Items.Consumable.SmallShieldPotion);
    g_NotificationManager.AddChange("Items-Consumable", "Shield Potion", items.Consumable.ShieldPotion, Items.Consumable.ShieldPotion);
    g_NotificationManager.AddChange("Items-Consumable", "Flow Berry", items.Consumable.FlowBerryFizz, Items.Consumable.FlowBerryFizz);
    g_NotificationManager.AddChange("Items-Consumable", "Chug Splash", items.Consumable.ChugSplash, Items.Consumable.ChugSplash);
    g_NotificationManager.AddChange("Items-Consumable", "Nitro Splash", items.Consumable.NitroSplash, Items.Consumable.NitroSplash);
    g_NotificationManager.AddChange("Items-Consumable", "Nuka Cola", items.Consumable.NukaCola, Items.Consumable.NukaCola);

    // Items - Weapons
    g_NotificationManager.AddChange("Items-Weapon", "Enable", items.Weapon.Enable, Items.Weapon.Enable);
    g_NotificationManager.AddChange("Items-Weapon", "Name", items.Weapon.Name, Items.Weapon.Name);
    g_NotificationManager.AddChange("Items-Weapon", "Distance", items.Weapon.Distance, Items.Weapon.Distance);
    g_NotificationManager.AddChange("Items-Weapon", "Icons", items.Weapon.Icons, Items.Weapon.Icons);
    g_NotificationManager.AddChange("Items-Weapon", "Icon Size", items.Weapon.IconSize, Items.Weapon.IconSize);
    g_NotificationManager.AddChange("Items-Weapon", "Max Distance", (float)items.Weapon.MaxDistance, (float)Items.Weapon.MaxDistance);
    g_NotificationManager.AddChange("Items-Weapon", "Font Size", items.Weapon.FontSize, Items.Weapon.FontSize);

    // Track individual weapon settings - just checking a few important ones to avoid making the method too long
    g_NotificationManager.AddChange("Items-Weapon", "Ranger Pistol", items.Weapon.RangerPistol, Items.Weapon.RangerPistol);
    g_NotificationManager.AddChange("Items-Weapon", "Combat AR", items.Weapon.CombatAssaultRifle, Items.Weapon.CombatAssaultRifle);
    g_NotificationManager.AddChange("Items-Weapon", "Tactical AR", items.Weapon.TacticalAssaultRifle, Items.Weapon.TacticalAssaultRifle);
    g_NotificationManager.AddChange("Items-Weapon", "Hammer Pump", items.Weapon.HammerPumpShotgun, Items.Weapon.HammerPumpShotgun);
    g_NotificationManager.AddChange("Items-Weapon", "Sniper Rifle", items.Weapon.HeavyImpactSniperRifle, Items.Weapon.HeavyImpactSniperRifle);

    // Items - Ammo
    g_NotificationManager.AddChange("Items-Ammo", "Enable", items.Ammo.Enable, Items.Ammo.Enable);
    g_NotificationManager.AddChange("Items-Ammo", "Name", items.Ammo.Name, Items.Ammo.Name);
    g_NotificationManager.AddChange("Items-Ammo", "Distance", items.Ammo.Distance, Items.Ammo.Distance);
    g_NotificationManager.AddChange("Items-Ammo", "Icons", items.Ammo.Icons, Items.Ammo.Icons);
    g_NotificationManager.AddChange("Items-Ammo", "Icon Size", items.Ammo.IconSize, Items.Ammo.IconSize);
    g_NotificationManager.AddChange("Items-Ammo", "Max Distance", (float)items.Ammo.MaxDistance, (float)Items.Ammo.MaxDistance);
    g_NotificationManager.AddChange("Items-Ammo", "Font Size", items.Ammo.FontSize, Items.Ammo.FontSize);
    g_NotificationManager.AddChange("Items-Ammo", "Light Ammo", items.Ammo.AmmoLight, Items.Ammo.AmmoLight);
    g_NotificationManager.AddChange("Items-Ammo", "Medium Ammo", items.Ammo.AmmoMedium, Items.Ammo.AmmoMedium);
    g_NotificationManager.AddChange("Items-Ammo", "Heavy Ammo", items.Ammo.AmmoHeavy, Items.Ammo.AmmoHeavy);
    g_NotificationManager.AddChange("Items-Ammo", "Shells", items.Ammo.AmmoShells, Items.Ammo.AmmoShells);
    g_NotificationManager.AddChange("Items-Ammo", "Rockets", items.Ammo.AmmoRockets, Items.Ammo.AmmoRockets);

    // Items - Other
    g_NotificationManager.AddChange("Items-Other", "Enable", items.Other.Enable, Items.Other.Enable);
    g_NotificationManager.AddChange("Items-Other", "Name", items.Other.Name, Items.Other.Name);
    g_NotificationManager.AddChange("Items-Other", "Distance", items.Other.Distance, Items.Other.Distance);
    g_NotificationManager.AddChange("Items-Other", "Icons", items.Other.Icons, Items.Other.Icons);
    g_NotificationManager.AddChange("Items-Other", "Icon Size", items.Other.IconSize, Items.Other.IconSize);
    g_NotificationManager.AddChange("Items-Other", "Max Distance", (float)items.Other.MaxDistance, (float)Items.Other.MaxDistance);
    g_NotificationManager.AddChange("Items-Other", "Font Size", items.Other.FontSize, Items.Other.FontSize);
    g_NotificationManager.AddChange("Items-Other", "Chest", items.Other.Chest, Items.Other.Chest);
    g_NotificationManager.AddChange("Items-Other", "Vehicle", items.Other.Vehicle, Items.Other.Vehicle);
    g_NotificationManager.AddChange("Items-Other", "Llama", items.Other.Llama, Items.Other.Llama);
    g_NotificationManager.AddChange("Items-Other", "Supply Drop", items.Other.SupplyDrop, Items.Other.SupplyDrop);

    // Color changes - AimbotColors
    g_NotificationManager.AddChange("Colors", "FOV", aimbotColors.Fov, AimbotColors.Fov);
    g_NotificationManager.AddChange("Colors", "CrossHair", aimbotColors.CrossHair, AimbotColors.CrossHair);
    g_NotificationManager.AddChange("Colors", "Target", aimbotColors.Target, AimbotColors.Target);

    // Player Colors - Check important ones (visible/non-visible boxes, lines)
    g_NotificationManager.AddChange("PlayerColors", "Box Visible", playersColors.BoxVisible, PlayersColors.BoxVisible);
    g_NotificationManager.AddChange("PlayerColors", "Box Non-Visible", playersColors.BoxNonVisible, PlayersColors.BoxNonVisible);
    g_NotificationManager.AddChange("PlayerColors", "Line Visible", playersColors.LineVisible, PlayersColors.LineVisible);
    g_NotificationManager.AddChange("PlayerColors", "Line Non-Visible", playersColors.LineNonVisible, PlayersColors.LineNonVisible);
    g_NotificationManager.AddChange("PlayerColors", "Skeleton Visible", playersColors.SkeletonVisible, PlayersColors.SkeletonVisible);
    g_NotificationManager.AddChange("PlayerColors", "Skeleton Non-Visible", playersColors.SkeletonNonVisible, PlayersColors.SkeletonNonVisible);

    // Add keybinds
    g_NotificationManager.AddChange("Keybinds", "Hold Trigger", (float)keys.HoldTrigger, (float)Keys.HoldTrigger);
    g_NotificationManager.AddChange("Keybinds", "Hold Primary", (float)keys.HoldPrimary, (float)Keys.HoldPrimary);
    g_NotificationManager.AddChange("Keybinds", "Hold Secondary", (float)keys.HoldSecondary, (float)Keys.HoldSecondary);
    g_NotificationManager.AddChange("Keybinds", "Switch To Head", (float)keys.SwitchToHead, (float)Keys.SwitchToHead);
    g_NotificationManager.AddChange("Keybinds", "Toggle", (float)keys.Toggle, (float)Keys.Toggle);
    g_NotificationManager.AddChange("Keybinds", "Toggle Players", (float)keys.TogglePlayers, (float)Keys.TogglePlayers);
    g_NotificationManager.AddChange("Keybinds", "Toggle Items", (float)keys.ToggleItems, (float)Keys.ToggleItems);
    g_NotificationManager.AddChange("Keybinds", "Toggle Radar", (float)keys.ToggleRadar, (float)Keys.ToggleRadar);
    g_NotificationManager.AddChange("Keybinds", "Toggle Panic Mode", (float)keys.TogglePanicMode, (float)Keys.TogglePanicMode);
    g_NotificationManager.AddChange("Keybinds", "Menu", (float)keys.Menu, (float)Keys.Menu);

    // ConfigsMenu settings
    g_NotificationManager.AddChange("Config", "Language", (float)configsMenu.Language, (float)ConfigsMenu.Language);
    g_NotificationManager.AddChange("Config", "Feature Definitions", configsMenu.FeaturesDefinition, ConfigsMenu.FeaturesDefinition);
}

// Function to track changes in config when menu is hidden
void TrackConfigChanges() {
    // Detect changes from previous snapshot
    g_ConfigSnapshot.DetectChanges();

    // Capture new settings for next comparison
    g_ConfigSnapshot.CaptureSettings();

    // Save settings to file
    SaveAimSettingsToFile();
}

void ParseItemssSettings(const std::string& key, const std::string& value) {
    // Consumables
    if (key == "ConsumableEnable") Items.Consumable.Enable = (value == "1");
    else if (key == "ConsumableName") Items.Consumable.Name = (value == "1");
    else if (key == "ConsumableDistance") Items.Consumable.Distance = (value == "1");
    else if (key == "ConsumableIcons") Items.Consumable.Icons = (value == "1");
    else if (key == "ConsumableIconSize") Items.Consumable.IconSize = std::stof(value);
    else if (key == "ConsumableMaxDistance") Items.Consumable.MaxDistance = std::stoi(value);
    else if (key == "ConsumableFontSize") Items.Consumable.FontSize = std::stof(value);
    else if (key == "ConsumableBandages") Items.Consumable.Bandages = (value == "1");
    else if (key == "ConsumableMedkit") Items.Consumable.Medkit = (value == "1");
    else if (key == "ConsumableSmallShieldPotion") Items.Consumable.SmallShieldPotion = (value == "1");
    else if (key == "ConsumableShieldPotion") Items.Consumable.ShieldPotion = (value == "1");
    else if (key == "ConsumableFlowBerryFizz") Items.Consumable.FlowBerryFizz = (value == "1");
    else if (key == "ConsumableChugSplash") Items.Consumable.ChugSplash = (value == "1");
    else if (key == "ConsumableNitroSplash") Items.Consumable.NitroSplash = (value == "1");
    else if (key == "ConsumableNukaCola") Items.Consumable.NukaCola = (value == "1");

    // Weapons
    else if (key == "WeaponEnable") Items.Weapon.Enable = (value == "1");
    else if (key == "WeaponName") Items.Weapon.Name = (value == "1");
    else if (key == "WeaponDistance") Items.Weapon.Distance = (value == "1");
    else if (key == "WeaponIcons") Items.Weapon.Icons = (value == "1");
    else if (key == "WeaponIconSize") Items.Weapon.IconSize = std::stof(value);
    else if (key == "WeaponMaxDistance") Items.Weapon.MaxDistance = std::stoi(value);
    else if (key == "WeaponFontSize") Items.Weapon.FontSize = std::stof(value);
    else if (key == "WeaponRangerPistol") Items.Weapon.RangerPistol = (value == "1");
    else if (key == "WeaponHarbingerSMG") Items.Weapon.HarbingerSMG = (value == "1");
    else if (key == "WeaponThunderBurstSMG") Items.Weapon.ThunderBurstSMG = (value == "1");
    else if (key == "WeaponWarforgedAssaultRifle") Items.Weapon.WarforgedAssaultRifle = (value == "1");
    else if (key == "WeaponTacticalAssaultRifle") Items.Weapon.TacticalAssaultRifle = (value == "1");
    else if (key == "WeaponEnforcerAssaultRifle") Items.Weapon.EnforcerAssaultRifle = (value == "1");
    else if (key == "WeaponCombatAssaultRifle") Items.Weapon.CombatAssaultRifle = (value == "1");
    else if (key == "WeaponCombatShotgun") Items.Weapon.CombatShotgun = (value == "1");
    else if (key == "WeaponGatekeeperShotgun") Items.Weapon.GatekeeperShotgun = (value == "1");
    else if (key == "WeaponHammerPumpShotgun") Items.Weapon.HammerPumpShotgun = (value == "1");
    else if (key == "WeaponFrenzyAutoShotgun") Items.Weapon.FrenzyAutoShotgun = (value == "1");
    else if (key == "WeaponHandCannon") Items.Weapon.HandCannon = (value == "1");
    else if (key == "WeaponHuntressDMR") Items.Weapon.HuntressDMR = (value == "1");
    else if (key == "WeaponBoomBolt") Items.Weapon.BoomBolt = (value == "1");
    else if (key == "WeaponNitroFists") Items.Weapon.NitroFists = (value == "1");
    else if (key == "WeaponShockwavegrenade") Items.Weapon.Shockwavegrenade = (value == "1");
    else if (key == "WeaponHeavyImpactSniperRifle") Items.Weapon.HeavyImpactSniperRifle = (value == "1");

    // ReloadWeapons
    else if (key == "WeaponMKSevenAssaultRifle") Items.Weapon.MKSevenAssaultRifle = (value == "1");
    else if (key == "WeaponNewPump_Shotgun") Items.Weapon.NewPump_Shotgun = (value == "1");
    else if (key == "WeaponOGPump_Shotgun") Items.Weapon.OGPump_Shotgun = (value == "1");
    else if (key == "WeaponBottomlessChugJug") Items.Weapon.BottomlessChugJug = (value == "1");
    else if (key == "WeaponBurstAR") Items.Weapon.BurstAR = (value == "1");
    else if (key == "WeaponDrumGun") Items.Weapon.DrumGun = (value == "1");
    else if (key == "WeaponSkyesAR") Items.Weapon.SkyesAR = (value == "1");
    else if (key == "WeaponGrappler") Items.Weapon.Grappler = (value == "1");
    else if (key == "WeaponStingerSMG") Items.Weapon.StingerSMG = (value == "1");
    else if (key == "WeaponHeistedBreacherShotgun") Items.Weapon.HeistedBreacherShotgun = (value == "1");
    else if (key == "WeaponHeistedAccelerantShotgun") Items.Weapon.HeistedAccelerantShotgun = (value == "1");
    else if (key == "WeaponHeistedExplosiveAR") Items.Weapon.HeistedExplosiveAR = (value == "1");
    else if (key == "WeaponHeistedBlinkMagSMG") Items.Weapon.HeistedBlinkMagSMG = (value == "1");
    else if (key == "WeaponHeistedRunGunSMG") Items.Weapon.HeistedRunGunSMG = (value == "1");
    else if (key == "WeaponTacticalShotgun") Items.Weapon.TacticalShotgun = (value == "1");
    else if (key == "WeaponLeverActionShotgun") Items.Weapon.LeverActionShotgun = (value == "1");
    else if (key == "WeaponHeavyShotgun") Items.Weapon.HeavyShotgun = (value == "1");
    else if (key == "WeaponRangerShotgun") Items.Weapon.RangerShotgun = (value == "1");
    else if (key == "WeaponAssaultRifle") Items.Weapon.AssaultRifle = (value == "1");
    else if (key == "WeaponScarAssaultRifle") Items.Weapon.ScarAssaultRifle = (value == "1");
    else if (key == "WeaponHammerAssaultRifle") Items.Weapon.HammerAssaultRifle = (value == "1");
    else if (key == "WeaponHeavyAssaultRifle") Items.Weapon.HeavyAssaultRifle = (value == "1");
    else if (key == "WeaponInfantryRifle") Items.Weapon.InfantryRifle = (value == "1");
    else if (key == "WeaponSubmachineGun") Items.Weapon.SubmachineGun = (value == "1");
    else if (key == "WeaponTacticalSubmachineGun") Items.Weapon.TacticalSubmachineGun = (value == "1");
    else if (key == "WeaponBoltActionSniperRifle") Items.Weapon.BoltActionSniperRifle = (value == "1");
    else if (key == "WeaponHuntingRifle") Items.Weapon.HuntingRifle = (value == "1");
    else if (key == "WeaponPistol") Items.Weapon.Pistol = (value == "1");
    else if (key == "WeaponRevolver") Items.Weapon.Revolver = (value == "1");
    else if (key == "WeaponRocketLauncher") Items.Weapon.RocketLauncher = (value == "1");
    else if (key == "WeaponCrashPad") Items.Weapon.CrashPad = (value == "1");

    // Ammos
    else if (key == "AmmoEnable") Items.Ammo.Enable = (value == "1");
    else if (key == "AmmoName") Items.Ammo.Name = (value == "1");
    else if (key == "AmmoDistance") Items.Ammo.Distance = (value == "1");
    else if (key == "AmmoIcons") Items.Ammo.Icons = (value == "1");
    else if (key == "AmmoIconSize") Items.Ammo.IconSize = std::stof(value);
    else if (key == "AmmoMaxDistance") Items.Ammo.MaxDistance = std::stoi(value);
    else if (key == "AmmoFontSize") Items.Ammo.FontSize = std::stof(value);
    else if (key == "AmmoLight") Items.Ammo.AmmoLight = (value == "1");
    else if (key == "AmmoMedium") Items.Ammo.AmmoMedium = (value == "1");
    else if (key == "AmmoHeavy") Items.Ammo.AmmoHeavy = (value == "1");
    else if (key == "AmmoShells") Items.Ammo.AmmoShells = (value == "1");
    else if (key == "AmmoRockets") Items.Ammo.AmmoRockets = (value == "1");

    // Others
    else if (key == "OtherEnable") Items.Other.Enable = (value == "1");
    else if (key == "OtherName") Items.Other.Name = (value == "1");
    else if (key == "OtherDistance") Items.Other.Distance = (value == "1");
    else if (key == "OtherIcons") Items.Other.Icons = (value == "1");
    else if (key == "OtherIconSize") Items.Other.IconSize = std::stof(value);
    else if (key == "OtherMaxDistance") Items.Other.MaxDistance = std::stoi(value);
    else if (key == "OtherFontSize") Items.Other.FontSize = std::stof(value);
    else if (key == "OtherChest") Items.Other.Chest = (value == "1");
    else if (key == "OtherVehicle") Items.Other.Vehicle = (value == "1");
    else if (key == "OtherLlama") Items.Other.Llama = (value == "1");
    else if (key == "OtherSupplyDrop") Items.Other.SupplyDrop = (value == "1");
}

void ParsePlayerssSettings(const std::string& key, const std::string& value) {
    if (key == "Enable") Players.Enable = (value == "1");
    else if (key == "MaxDistance") Players.MaxDistance = std::stoi(value);
    else if (key == "FontSize") Players.FontSize = std::stof(value);
    else if (key == "PlayerAi") Players.PlayerAi = (value == "1");
    else if (key == "Box") Players.Box = (value == "1");
    else if (key == "BoxStyle1") Players.BoxStyle[0] = (value == "1");
    else if (key == "BoxStyle2") Players.BoxStyle[1] = (value == "1");
    else if (key == "BoxType") Players.BoxType = std::stoi(value);
    else if (key == "BoxThickness") Players.BoxThickness = std::stof(value);
    else if (key == "BoxRounding") Players.BoxRounding = std::stof(value);
    else if (key == "IgnoreDowned") Players.IgnoreDowned = (value == "1");
    else if (key == "HeadCircle") Players.HeadCircle = (value == "1");
    else if (key == "HeadCircleThickness") Players.HeadCircleThickness = std::stof(value);
    else if (key == "Lines") Players.Lines = (value == "1");
    else if (key == "LinesPosition") Players.LinesPosition = std::stoi(value);
    else if (key == "LinesThickness") Players.LinesThickness = std::stof(value);
    else if (key == "Distance") Players.Distance = (value == "1");
    else if (key == "NickName") Players.NickName = (value == "1");
    else if (key == "Platform") Players.Platform = (value == "1");
    else if (key == "Kills") Players.Kills = (value == "1");
    else if (key == "Level") Players.Level = (value == "1");
    else if (key == "Rank") Players.Rank = (value == "1");
    else if (key == "Weapon") Players.Weapon = (value == "1");
    else if (key == "WeaponRarity") Players.WeaponRarity = (value == "1");
    else if (key == "WeaponAmmo") Players.WeaponAmmo = (value == "1");
    else if (key == "Skeleton") Players.Skeleton = (value == "1");
    else if (key == "SkeletonThickness") Players.SkeletonThickness = std::stof(value);

}

void ParseRadarSettings(const std::string& key, const std::string& value) {
    if (key == "Enable") {
        Radar.Enable = (value == "1");
    }
    else if (key == "Distance") {
        Radar.Distance = (value == "1");
    }
    else if (key == "PositionX") {
        Radar.PositionX = std::stoi(value);
    }
    else if (key == "PositionY") {
        Radar.PositionY = std::stoi(value);
    }
    else if (key == "CirleSize") {
        Radar.CirleSize = std::stoi(value);
    }
    else if (key == "RectangleSize") {
        Radar.RectangleSize = std::stoi(value);
    }
    else if (key == "VisibleColor") {
        Radar.VisibleColor = (value == "true");
    }
    else if (key == "ClosestColor") {
        Radar.ClosestColor = (value == "true");
    }
    else if (key == "AimingAtMeColor") {
        Radar.AimingAtMeColor = (value == "true");
    }
    else if (key == "MaxDistance") {
        Radar.MaxDistance = std::stoi(value);
    }
    else if (key == "DistanceFontSize") {
        Radar.DistanceFontSize = std::stoi(value);
    }
    else if (key == "RadarType") {
        Radar.RadarType = std::stoi(value);
    }
}
void ParseKeyssSettings(const std::string& key, const std::string& value) {
    // Debug output
    std::cout << "ParseKeyssSettings: Loading key=" << key << ", value=" << value << std::endl;

    // Basic keys
    if (key == "HoldTrigger") Keys.HoldTrigger = std::stoi(value);
    else if (key == "HoldPrimary") Keys.HoldPrimary = std::stoi(value);
    else if (key == "HoldSecondary") Keys.HoldSecondary = std::stoi(value);
    else if (key == "SwitchToHead") Keys.SwitchToHead = std::stoi(value);
    else if (key == "Toggle") Keys.Toggle = std::stoi(value);
    else if (key == "TogglePlayers") Keys.TogglePlayers = std::stoi(value);
    else if (key == "ToggleItems") Keys.ToggleItems = std::stoi(value);
    else if (key == "ToggleRadar") Keys.ToggleRadar = std::stoi(value);
    else if (key == "TogglePanicMode") Keys.TogglePanicMode = std::stoi(value);
    else if (key == "Menu") Keys.Menu = std::stoi(value);

    // Aimbot feature hotkeys
    else if (key == "AimbotEnable") {
        Keys.AimbotEnable = std::stoi(value);
        std::cout << "Loaded AimbotEnable hotkey: " << Keys.AimbotEnable << std::endl;
    }
    else if (key == "AimbotAimLock") {
        Keys.AimbotAimLock = std::stoi(value);
        std::cout << "Loaded AimbotAimLock hotkey: " << Keys.AimbotAimLock << std::endl;
    }
    else if (key == "AimbotPrediction") Keys.AimbotPrediction = std::stoi(value);
    else if (key == "AimbotSaveTarget") Keys.AimbotSaveTarget = std::stoi(value);
    else if (key == "AimbotVisibilityCheck") Keys.AimbotVisibilityCheck = std::stoi(value);
    else if (key == "AimbotHumanizedSmooth") Keys.AimbotHumanizedSmooth = std::stoi(value);
    else if (key == "AimbotIgnoreDowned") Keys.AimbotIgnoreDowned = std::stoi(value);
    else if (key == "AimbotPlayerAi") Keys.AimbotPlayerAi = std::stoi(value);
    else if (key == "AimbotWeaponOnly") Keys.AimbotWeaponOnly = std::stoi(value);
    else if (key == "AimbotDrawFov") Keys.AimbotDrawFov = std::stoi(value);
    else if (key == "AimbotDrawCrossHair") Keys.AimbotDrawCrossHair = std::stoi(value);
    else if (key == "AimbotDrawTarget") Keys.AimbotDrawTarget = std::stoi(value);

    // Player ESP feature hotkeys
    else if (key == "PlayerEspEnable") {
        Keys.PlayerEspEnable = std::stoi(value);
        std::cout << "Loaded PlayerEspEnable hotkey: " << Keys.PlayerEspEnable << std::endl;
    }
    else if (key == "PlayerEspBox") Keys.PlayerEspBox = std::stoi(value);
    else if (key == "PlayerEspSkeleton") Keys.PlayerEspSkeleton = std::stoi(value);
    else if (key == "PlayerEspHeadCircle") Keys.PlayerEspHeadCircle = std::stoi(value);
    else if (key == "PlayerEspLines") Keys.PlayerEspLines = std::stoi(value);
    else if (key == "PlayerEspDistance") Keys.PlayerEspDistance = std::stoi(value);
    else if (key == "PlayerEspNickName") Keys.PlayerEspNickName = std::stoi(value);

    // Item ESP feature hotkeys
    else if (key == "ItemEspEnable") Keys.ItemEspEnable = std::stoi(value);
    else if (key == "ItemConsumableEnable") Keys.ItemConsumableEnable = std::stoi(value);
    else if (key == "ItemWeaponEnable") Keys.ItemWeaponEnable = std::stoi(value);
    else if (key == "ItemAmmoEnable") Keys.ItemAmmoEnable = std::stoi(value);
    else if (key == "ItemOtherEnable") Keys.ItemOtherEnable = std::stoi(value);

    // Radar feature hotkeys
    else if (key == "RadarEnable") Keys.RadarEnable = std::stoi(value);
    else if (key == "RadarDistance") Keys.RadarDistance = std::stoi(value);
    else if (key == "RadarVisibleColor") Keys.RadarVisibleColor = std::stoi(value);
    else if (key == "RadarClosestColor") Keys.RadarClosestColor = std::stoi(value);
    else if (key == "RadarAimingAtMeColor") Keys.RadarAimingAtMeColor = std::stoi(value);

    // Hotkey modes (true = toggle, false = hold)
    else if (key == "AimbotEnableMode") {
        Keys.AimbotEnableMode = (value == "1");
        std::cout << "Loaded AimbotEnableMode: " << (Keys.AimbotEnableMode ? "Toggle" : "Hold") << " (value=" << value << ")" << std::endl;

        // Also update Settings struct
        Keys.AimbotEnableMode = Keys.AimbotEnableMode;
    }
    else if (key == "AimbotAimLockMode") {
        Keys.AimbotAimLockMode = (value == "1");
        std::cout << "Loaded AimbotAimLockMode: " << (Keys.AimbotAimLockMode ? "Toggle" : "Hold") << " (value=" << value << ")" << std::endl;

        // Also update Settings struct
        Keys.AimbotAimLockMode = Keys.AimbotAimLockMode;
    }
    else if (key == "AimbotPredictionMode") {
        Keys.AimbotPredictionMode = (value == "1");
        Keys.AimbotPredictionMode = Keys.AimbotPredictionMode;
    }
    else if (key == "AimbotSaveTargetMode") {
        Keys.AimbotSaveTargetMode = (value == "1");
        Keys.AimbotSaveTargetMode = Keys.AimbotSaveTargetMode;
    }
    else if (key == "AimbotVisibilityCheckMode") {
        Keys.AimbotVisibilityCheckMode = (value == "1");
        Keys.AimbotVisibilityCheckMode = Keys.AimbotVisibilityCheckMode;
    }
    else if (key == "AimbotHumanizedSmoothMode") {
        Keys.AimbotHumanizedSmoothMode = (value == "1");
        Keys.AimbotHumanizedSmoothMode = Keys.AimbotHumanizedSmoothMode;
    }
    else if (key == "AimbotIgnoreDownedMode") {
        Keys.AimbotIgnoreDownedMode = (value == "1");
        Keys.AimbotIgnoreDownedMode = Keys.AimbotIgnoreDownedMode;
    }
    else if (key == "AimbotPlayerAiMode") {
        Keys.AimbotPlayerAiMode = (value == "1");
        Keys.AimbotPlayerAiMode = Keys.AimbotPlayerAiMode;
    }
    else if (key == "AimbotWeaponOnlyMode") {
        Keys.AimbotWeaponOnlyMode = (value == "1");
        Keys.AimbotWeaponOnlyMode = Keys.AimbotWeaponOnlyMode;
    }
    else if (key == "AimbotDrawFovMode") {
        Keys.AimbotDrawFovMode = (value == "1");
        Keys.AimbotDrawFovMode = Keys.AimbotDrawFovMode;
    }
    else if (key == "AimbotDrawCrossHairMode") {
        Keys.AimbotDrawCrossHairMode = (value == "1");
        Keys.AimbotDrawCrossHairMode = Keys.AimbotDrawCrossHairMode;
    }
    else if (key == "AimbotDrawTargetMode") {
        Keys.AimbotDrawTargetMode = (value == "1");
        Keys.AimbotDrawTargetMode = Keys.AimbotDrawTargetMode;
    }

    else if (key == "PlayerEspEnableMode") {
        Keys.PlayerEspEnableMode = (value == "1");
        std::cout << "Loaded PlayerEspEnableMode: " << (Keys.PlayerEspEnableMode ? "Toggle" : "Hold") << " (value=" << value << ")" << std::endl;

        // Also update Settings struct
        Keys.PlayerEspEnableMode = Keys.PlayerEspEnableMode;
    }
    else if (key == "PlayerEspBoxMode") {
        Keys.PlayerEspBoxMode = (value == "1");
        Keys.PlayerEspBoxMode = Keys.PlayerEspBoxMode;
    }
    else if (key == "PlayerEspSkeletonMode") {
        Keys.PlayerEspSkeletonMode = (value == "1");
        Keys.PlayerEspSkeletonMode = Keys.PlayerEspSkeletonMode;
    }
    else if (key == "PlayerEspHeadCircleMode") {
        Keys.PlayerEspHeadCircleMode = (value == "1");
        Keys.PlayerEspHeadCircleMode = Keys.PlayerEspHeadCircleMode;
    }
    else if (key == "PlayerEspLinesMode") {
        Keys.PlayerEspLinesMode = (value == "1");
        Keys.PlayerEspLinesMode = Keys.PlayerEspLinesMode;
    }
    else if (key == "PlayerEspDistanceMode") {
        Keys.PlayerEspDistanceMode = (value == "1");
        Keys.PlayerEspDistanceMode = Keys.PlayerEspDistanceMode;
    }
    else if (key == "PlayerEspNickNameMode") {
        Keys.PlayerEspNickNameMode = (value == "1");
        Keys.PlayerEspNickNameMode = Keys.PlayerEspNickNameMode;
    }

    else if (key == "ItemEspEnableMode") {
        Keys.ItemEspEnableMode = (value == "1");
        Keys.ItemEspEnableMode = Keys.ItemEspEnableMode;
    }
    else if (key == "ItemConsumableEnableMode") {
        Keys.ItemConsumableEnableMode = (value == "1");
        Keys.ItemConsumableEnableMode = Keys.ItemConsumableEnableMode;
    }
    else if (key == "ItemWeaponEnableMode") {
        Keys.ItemWeaponEnableMode = (value == "1");
        Keys.ItemWeaponEnableMode = Keys.ItemWeaponEnableMode;
    }
    else if (key == "ItemAmmoEnableMode") {
        Keys.ItemAmmoEnableMode = (value == "1");
        Keys.ItemAmmoEnableMode = Keys.ItemAmmoEnableMode;
    }
    else if (key == "ItemOtherEnableMode") {
        Keys.ItemOtherEnableMode = (value == "1");
        Keys.ItemOtherEnableMode = Keys.ItemOtherEnableMode;
    }

    else if (key == "RadarEnableMode") {
        Keys.RadarEnableMode = (value == "1");
        Keys.RadarEnableMode = Keys.RadarEnableMode;
    }
    else if (key == "RadarDistanceMode") {
        Keys.RadarDistanceMode = (value == "1");
        Keys.RadarDistanceMode = Keys.RadarDistanceMode;
    }
    else if (key == "RadarVisibleColorMode") {
        Keys.RadarVisibleColorMode = (value == "1");
        Keys.RadarVisibleColorMode = Keys.RadarVisibleColorMode;
    }
    else if (key == "RadarClosestColorMode") {
        Keys.RadarClosestColorMode = (value == "1");
        Keys.RadarClosestColorMode = Keys.RadarClosestColorMode;
    }
    else if (key == "RadarAimingAtMeColorMode") {
        Keys.RadarAimingAtMeColorMode = (value == "1");
        Keys.RadarAimingAtMeColorMode = Keys.RadarAimingAtMeColorMode;
    }
}

#include <sstream>

void ParseAimbotColorssSettings(const std::string& key, const std::string& value) {
    if (key == "Fov") {
        std::string temp_value = value;
        std::replace(temp_value.begin(), temp_value.end(), ',', ' ');
        std::istringstream ss(temp_value);
        ss >> AimbotColors.Fov[0] >> AimbotColors.Fov[1] >> AimbotColors.Fov[2];
    }
    else if (key == "CrossHair") {
        std::string temp_value = value;
        std::replace(temp_value.begin(), temp_value.end(), ',', ' ');
        std::istringstream ss(temp_value);
        ss >> AimbotColors.CrossHair[0] >> AimbotColors.CrossHair[1] >> AimbotColors.CrossHair[2];
    }
    else if (key == "Target") {
        std::string temp_value = value;
        std::replace(temp_value.begin(), temp_value.end(), ',', ' ');
        std::istringstream ss(temp_value);
        ss >> AimbotColors.Target[0] >> AimbotColors.Target[1] >> AimbotColors.Target[2];
    }
}

void ParsePlayerColorsSettings(const std::string& key, const std::string& value) {
    std::string temp_value = value;
    std::replace(temp_value.begin(), temp_value.end(), ',', ' ');
    std::istringstream ss(temp_value);

    if (key == "BoxVisible") {
        ss >> PlayersColors.BoxVisible[0] >> PlayersColors.BoxVisible[1] >> PlayersColors.BoxVisible[2];
    }
    else if (key == "BoxNonVisible") {
        ss >> PlayersColors.BoxNonVisible[0] >> PlayersColors.BoxNonVisible[1] >> PlayersColors.BoxNonVisible[2];
    }
    else if (key == "KnockedBoxVisible") {
        ss >> PlayersColors.KnockedBoxVisible[0] >> PlayersColors.KnockedBoxVisible[1] >> PlayersColors.KnockedBoxVisible[2];
    }
    else if (key == "KnockedBoxNonVisible") {
        ss >> PlayersColors.KnockedBoxNonVisible[0] >> PlayersColors.KnockedBoxNonVisible[1] >> PlayersColors.KnockedBoxNonVisible[2];
    }
    else if (key == "BoxFillVisible") {
        ss >> PlayersColors.BoxFillVisible[0] >> PlayersColors.BoxFillVisible[1] >> PlayersColors.BoxFillVisible[2] >> PlayersColors.BoxFillVisible[3];
    }
    else if (key == "BoxFillNonVisible") {
        ss >> PlayersColors.BoxFillNonVisible[0] >> PlayersColors.BoxFillNonVisible[1] >> PlayersColors.BoxFillNonVisible[2] >> PlayersColors.BoxFillNonVisible[3];
    }
    else if (key == "KnockedBoxFillVisible") {
        ss >> PlayersColors.KnockedBoxFillVisible[0] >> PlayersColors.KnockedBoxFillVisible[1] >> PlayersColors.KnockedBoxFillVisible[2] >> PlayersColors.KnockedBoxFillVisible[3];
    }
    else if (key == "KnockedBoxFillNonVisible") {
        ss >> PlayersColors.KnockedBoxFillNonVisible[0] >> PlayersColors.KnockedBoxFillNonVisible[1] >> PlayersColors.KnockedBoxFillNonVisible[2] >> PlayersColors.KnockedBoxFillNonVisible[3];
    }
    else if (key == "SkeletonVisible") {
        ss >> PlayersColors.SkeletonVisible[0] >> PlayersColors.SkeletonVisible[1] >> PlayersColors.SkeletonVisible[2];
    }
    else if (key == "SkeletonNonVisible") {
        ss >> PlayersColors.SkeletonNonVisible[0] >> PlayersColors.SkeletonNonVisible[1] >> PlayersColors.SkeletonNonVisible[2];
    }
    else if (key == "KnockedSkeletonVisible") {
        ss >> PlayersColors.KnockedSkeletonVisible[0] >> PlayersColors.KnockedSkeletonVisible[1] >> PlayersColors.KnockedSkeletonVisible[2];
    }
    else if (key == "KnockedSkeletonNonVisible") {
        ss >> PlayersColors.KnockedSkeletonNonVisible[0] >> PlayersColors.KnockedSkeletonNonVisible[1] >> PlayersColors.KnockedSkeletonNonVisible[2];
    }
    else if (key == "HeadVisible") {
        ss >> PlayersColors.HeadVisible[0] >> PlayersColors.HeadVisible[1] >> PlayersColors.HeadVisible[2];
    }
    else if (key == "HeadNonVisible") {
        ss >> PlayersColors.HeadNonVisible[0] >> PlayersColors.HeadNonVisible[1] >> PlayersColors.HeadNonVisible[2];
    }
    else if (key == "KnockedHeadVisible") {
        ss >> PlayersColors.KnockedHeadVisible[0] >> PlayersColors.KnockedHeadVisible[1] >> PlayersColors.KnockedHeadVisible[2];
    }
    else if (key == "KnockedHeadNonVisible") {
        ss >> PlayersColors.KnockedHeadNonVisible[0] >> PlayersColors.KnockedHeadNonVisible[1] >> PlayersColors.KnockedHeadNonVisible[2];
    }
    else if (key == "LineVisible") {
        ss >> PlayersColors.LineVisible[0] >> PlayersColors.LineVisible[1] >> PlayersColors.LineVisible[2];
    }
    else if (key == "LineNonVisible") {
        ss >> PlayersColors.LineNonVisible[0] >> PlayersColors.LineNonVisible[1] >> PlayersColors.LineNonVisible[2];
    }
    else if (key == "KnockedLineVisible") {
        ss >> PlayersColors.KnockedLineVisible[0] >> PlayersColors.KnockedLineVisible[1] >> PlayersColors.KnockedLineVisible[2];
    }
    else if (key == "KnockedLineNonVisible") {
        ss >> PlayersColors.KnockedLineNonVisible[0] >> PlayersColors.KnockedLineNonVisible[1] >> PlayersColors.KnockedLineNonVisible[2];
    }
    else if (key == "RadarVisible") {
        ss >> PlayersColors.RadarVisible[0] >> PlayersColors.RadarVisible[1] >> PlayersColors.RadarVisible[2];
    }
    else if (key == "RadarNonVisible") {
        ss >> PlayersColors.RadarNonVisible[0] >> PlayersColors.RadarNonVisible[1] >> PlayersColors.RadarNonVisible[2];
    }
    else if (key == "KnockedRadarVisible") {
        ss >> PlayersColors.KnockedRadarVisible[0] >> PlayersColors.KnockedRadarVisible[1] >> PlayersColors.KnockedRadarVisible[2];
    }
    else if (key == "KnockedRadarNonVisible") {
        ss >> PlayersColors.KnockedRadarNonVisible[0] >> PlayersColors.KnockedRadarNonVisible[1] >> PlayersColors.KnockedRadarNonVisible[2];
    }
    else if (key == "Distance") {
        ss >> PlayersColors.Distance[0] >> PlayersColors.Distance[1] >> PlayersColors.Distance[2];
    }
    else if (key == "NickName") {
        ss >> PlayersColors.NickName[0] >> PlayersColors.NickName[1] >> PlayersColors.NickName[2];
    }
}


void ParseConfigsMenusSettings(const std::string& key, const std::string& value) {
    if (key == "iConfig") ConfigsMenu.iConfig = std::stoi(value);
    else if (key == "oldiConfig") ConfigsMenu.oldiConfig = std::stoi(value);
    else if (key == "selectedWeapon") ConfigsMenu.selectedWeapon = std::stoi(value);
    else if (key == "Language") ConfigsMenu.Language = std::stoi(value);
    else if (key == "FeaturesDefinition") ConfigsMenu.FeaturesDefinition = (value == "1");
}


void ParseAimbotsSettings(const std::string& key, const std::string& value, Aimbots& settings) {
    if (key == "Enable") settings.Enable = (value == "1");
    else if (key == "SaveTarget") settings.SaveTarget = (value == "1");
    else if (key == "AimLock") settings.AimLock = (value == "1");
    else if (key == "VisibilityCheck") settings.VisibilityCheck = (value == "1");
    else if (key == "HumanizedSmooth") settings.HumanizedSmooth = (value == "1");
    else if (key == "WeaponOnly") settings.WeaponOnly = (value == "1");
    else if (key == "IgnoreDowned") settings.IgnoreDowned = (value == "1");
    else if (key == "Predict") settings.Predict = (value == "1");
    else if (key == "PlayerAi") settings.PlayerAi = (value == "1");
    else if (key == "ByWeapon") settings.ByWeapon = (value == "1");
    else if (key == "DrawFov") settings.DrawFov = (value == "1");
    else if (key == "DrawFovFilled") settings.DrawFovFilled = (value == "1");
    else if (key == "DrawFovOutline") settings.DrawFovOutline = (value == "1");
    else if (key == "DrawFovRGB") settings.DrawFovRGB = (value == "1");
    else if (key == "DrawCrossHair") settings.DrawCrossHair = (value == "1");
    else if (key == "DrawCrossHairType") settings.DrawCrossHairType = std::stoi(value);
    else if (key == "DrawTarget") settings.DrawTarget = (value == "1");
    else if (key == "DrawTargetType") settings.DrawTargetType = std::stoi(value);
    else if (key == "DrawThickness") settings.DrawThickness = std::stof(value);
    else if (key == "DrawSize") settings.DrawSize = std::stof(value);
    else if (key == "HitBox") settings.HitBox = std::stoi(value);
    else if (key == "FOV") settings.FOV = std::stof(value);
    else if (key == "Smooth") settings.Smooth = std::stof(value);
    else if (key == "HumanizedSmoothPercent") settings.HumanizedSmoothPercent = std::stof(value);
    else if (key == "MaxDistance") settings.MaxDistance = std::stoi(value);
}


void ParseTriggerbotsSettings(const std::string& key, const std::string& value, Triggerbots& settings) {
    if (key == "Enable") settings.Enable = (value == "1");
    else if (key == "EnableAllWeapons") settings.EnableAllWeapons = (value == "1");
    else if (key == "EnableOnlyShutguns") settings.EnableOnlyShutguns = (value == "1");
    else if (key == "ByWeapon") settings.ByWeapon = (value == "1");
    else if (key == "Delay") settings.Delay = std::stoi(value);
    else if (key == "MaxDistance") settings.MaxDistance = std::stof(value);
    else if (key == "Hotkey") settings.Hotkey = std::stoi(value);
}



void ParseSettings(const std::string& section, const std::string& key, const std::string& value) {

    if (section == "[Aim]") {
        ParseAimbotsSettings(key, value, Aimbot);
    }
    else if (section.find("[AimNew") == 0) {
        int weaponIndex = std::stoi(section.substr(7, section.size() - 8));
        ParseAimbotsSettings(key, value, weaponSettings.weaponAimSettings[weaponIndex]);
    }
    else if (section == "[Trigger]") {
        ParseTriggerbotsSettings(key, value, Triggerbot);
    }
    else if (section == "[Items]") {
        ParseItemssSettings(key, value);
    }
    else if (section == "[Players]") {
        ParsePlayerssSettings(key, value);
    }
    else if (section == "[Keys]") {
        ParseKeyssSettings(key, value);
    }
    else if (section == "[AimbotColors]") {
        ParseAimbotColorssSettings(key, value);
    }
    else if (section == "[PlayersColors]") {
        ParsePlayerColorsSettings(key, value);
    }
    else if (section == "[ConfigsMenu]") {
        ParseConfigsMenusSettings(key, value);
    }
    else if (section == "[Radar]") {
        ParseRadarSettings(key, value);
    }
    else if (section == "[Notifications]") {
        if (key == "Enable") g_NotificationSettings.Enable = (value == "1");
        else if (key == "DisplayDuration") g_NotificationSettings.DisplayDuration = std::stof(value);
    }
}

void LoadAimSettingsFromFile() {
#if IsBitResell
    FILE* file;
    fopen_s(&file, "C:\\Default\\Fortnite.Settings", "r"); // Open file in text read mode
#else
    FILE* file;
    fopen_s(&file, "C:\\BitCheats\\Fortnite.Settings", "r"); // Open file in text read mode
#endif
    if (file) {
        char buffer[1024]; // Buffer for reading lines
        std::string section;

        while (fgets(buffer, sizeof(buffer), file)) { // Read line by line
            std::string line(buffer);

            // Remove any trailing newline or carriage return characters
            line.erase(std::remove(line.begin(), line.end(), '\n'), line.end());
            line.erase(std::remove(line.begin(), line.end(), '\r'), line.end());

            if (line.empty() || line[0] == ';') continue; // Skip empty or comment lines

            if (line[0] == '[' && line.back() == ']') {
                section = line; // Save the current section
            }
            else {
                std::string key, value;
                size_t equalPos = line.find('=');
                if (equalPos != std::string::npos) {
                    key = line.substr(0, equalPos);
                    value = line.substr(equalPos + 1);
                    ParseSettings(section, key, value); // Call existing parser
                }
            }
        }

        fclose(file); // Close the file
    }
}



void SaveAimbotSettings(std::ostringstream& file, const Aimbots& settings) {
    file << "Enable=" << (settings.Enable ? "1" : "0") << std::endl;
    file << "SaveTarget=" << (settings.SaveTarget ? "1" : "0") << std::endl;
    file << "AimLock=" << (settings.AimLock ? "1" : "0") << std::endl;
    file << "VisibilityCheck=" << (settings.VisibilityCheck ? "1" : "0") << std::endl;
    file << "HumanizedSmooth=" << (settings.HumanizedSmooth ? "1" : "0") << std::endl;
    file << "WeaponOnly=" << (settings.WeaponOnly ? "1" : "0") << std::endl;
    file << "IgnoreDowned=" << (settings.IgnoreDowned ? "1" : "0") << std::endl;
    file << "Predict=" << (settings.Predict ? "1" : "0") << std::endl;
    file << "PlayerAi=" << (settings.PlayerAi ? "1" : "0") << std::endl;
    file << "ByWeapon=" << (settings.ByWeapon ? "1" : "0") << std::endl;
    file << "DrawFov=" << (settings.DrawFov ? "1" : "0") << std::endl;
    file << "DrawFovFilled=" << (settings.DrawFovFilled ? "1" : "0") << std::endl;
    file << "DrawFovOutline=" << (settings.DrawFovOutline ? "1" : "0") << std::endl;
    file << "DrawFovRGB=" << (settings.DrawFovRGB ? "1" : "0") << std::endl;
    file << "DrawCrossHair=" << (settings.DrawCrossHair ? "1" : "0") << std::endl;
    file << "DrawCrossHairType=" << settings.DrawCrossHairType << std::endl;
    file << "DrawTarget=" << (settings.DrawTarget ? "1" : "0") << std::endl;
    file << "DrawTargetType=" << settings.DrawTargetType << std::endl;
    file << "DrawThickness=" << settings.DrawThickness << std::endl;
    file << "DrawSize=" << settings.DrawSize << std::endl;
    file << "HitBox=" << settings.HitBox << std::endl;
    file << "FOV=" << settings.FOV << std::endl;
    file << "Smooth=" << settings.Smooth << std::endl;
    file << "HumanizedSmoothPercent=" << settings.HumanizedSmoothPercent << std::endl;
    file << "MaxDistance=" << settings.MaxDistance << std::endl;
}


void SaveTriggerbotSettings(std::ostringstream& file, const Triggerbots& settings) {
    file << "Enable=" << (settings.Enable ? "1" : "0") << std::endl;
    file << "EnableAllWeapons=" << (settings.EnableAllWeapons ? "1" : "0") << std::endl;
    file << "EnableOnlyShutguns=" << (settings.EnableOnlyShutguns ? "1" : "0") << std::endl;
    file << "Delay=" << settings.Delay << std::endl;
    file << "MaxDistance=" << settings.MaxDistance << std::endl;
    file << "Hotkey=" << settings.Hotkey << std::endl;
}

bool SaveAimSettingsToFile() {
    try {
#if IsBitResell
        FILE* file;
        fopen_s(&file, "C:\\Default\\Fortnite.Settings", "w"); // Open file in text write mode
#else
        FILE* file;
        fopen_s(&file, "C:\\BitCheats\\Fortnite.Settings", "w"); // Open file in text write mode
#endif

        Aimbots& weaponAimSettings = aimbot[ConfigsMenu.selectedWeapon];

        if (file) {
            std::ostringstream oss;

            oss << "[Aim]" << std::endl;
            SaveAimbotSettings(oss, Aimbot);
            if (Aimbot.ByWeapon) {
                for (const auto& weapon : weaponSettings.weaponAimSettings) {
                    oss << "[AimNew" << weapon.first << "]" << std::endl;
                    SaveAimbotSettings(oss, weapon.second);
                }
            }
            oss << "[Trigger]" << std::endl;
            SaveTriggerbotSettings(oss, Triggerbot);
            // Save additional settings
            oss << "[Items]" << std::endl;
            oss << "ConsumableEnable=" << (Items.Consumable.Enable ? "1" : "0") << std::endl;
            oss << "ConsumableName=" << (Items.Consumable.Name ? "1" : "0") << std::endl;
            oss << "ConsumableDistance=" << (Items.Consumable.Distance ? "1" : "0") << std::endl;
            oss << "ConsumableIcons=" << (Items.Consumable.Icons ? "1" : "0") << std::endl;
            oss << "ConsumableIconSize=" << Items.Consumable.IconSize << std::endl;
            oss << "ConsumableMaxDistance=" << Items.Consumable.MaxDistance << std::endl;
            oss << "ConsumableFontSize=" << Items.Consumable.FontSize << std::endl;
            oss << "ConsumableBandages=" << (Items.Consumable.Bandages ? "1" : "0") << std::endl;
            oss << "ConsumableMedkit=" << (Items.Consumable.Medkit ? "1" : "0") << std::endl;
            oss << "ConsumableSmallShieldPotion=" << (Items.Consumable.SmallShieldPotion ? "1" : "0") << std::endl;
            oss << "ConsumableShieldPotion=" << (Items.Consumable.ShieldPotion ? "1" : "0") << std::endl;
            oss << "ConsumableFlowBerryFizz=" << (Items.Consumable.FlowBerryFizz ? "1" : "0") << std::endl;
            oss << "ConsumableChugSplash=" << (Items.Consumable.ChugSplash ? "1" : "0") << std::endl;
            oss << "ConsumableNitroSplash=" << (Items.Consumable.NitroSplash ? "1" : "0") << std::endl;
            oss << "ConsumableNukaCola=" << (Items.Consumable.NukaCola ? "1" : "0") << std::endl;

            // Weapons
            oss << "WeaponEnable=" << (Items.Weapon.Enable ? "1" : "0") << std::endl;
            oss << "WeaponName=" << (Items.Weapon.Name ? "1" : "0") << std::endl;
            oss << "WeaponDistance=" << (Items.Weapon.Distance ? "1" : "0") << std::endl;
            oss << "WeaponIcons=" << (Items.Weapon.Icons ? "1" : "0") << std::endl;
            oss << "WeaponIconSize=" << Items.Weapon.IconSize << std::endl;
            oss << "WeaponMaxDistance=" << Items.Weapon.MaxDistance << std::endl;
            oss << "WeaponFontSize=" << Items.Weapon.FontSize << std::endl;
            oss << "WeaponRangerPistol=" << (Items.Weapon.RangerPistol ? "1" : "0") << std::endl;
            oss << "WeaponHarbingerSMG=" << (Items.Weapon.HarbingerSMG ? "1" : "0") << std::endl;
            oss << "WeaponThunderBurstSMG=" << (Items.Weapon.ThunderBurstSMG ? "1" : "0") << std::endl;
            oss << "WeaponWarforgedAssaultRifle=" << (Items.Weapon.WarforgedAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponTacticalAssaultRifle=" << (Items.Weapon.TacticalAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponEnforcerAssaultRifle=" << (Items.Weapon.EnforcerAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponCombatAssaultRifle=" << (Items.Weapon.CombatAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponCombatShotgun=" << (Items.Weapon.CombatShotgun ? "1" : "0") << std::endl;
            oss << "WeaponGatekeeperShotgun=" << (Items.Weapon.GatekeeperShotgun ? "1" : "0") << std::endl;
            oss << "WeaponHammerPumpShotgun=" << (Items.Weapon.HammerPumpShotgun ? "1" : "0") << std::endl;
            oss << "WeaponFrenzyAutoShotgun=" << (Items.Weapon.FrenzyAutoShotgun ? "1" : "0") << std::endl;
            oss << "WeaponHandCannon=" << (Items.Weapon.HandCannon ? "1" : "0") << std::endl;
            oss << "WeaponHuntressDMR=" << (Items.Weapon.HuntressDMR ? "1" : "0") << std::endl;
            oss << "WeaponBoomBolt=" << (Items.Weapon.BoomBolt ? "1" : "0") << std::endl;
            oss << "WeaponNitroFists=" << (Items.Weapon.NitroFists ? "1" : "0") << std::endl;
            oss << "WeaponShockwavegrenade=" << (Items.Weapon.Shockwavegrenade ? "1" : "0") << std::endl;
            oss << "WeaponHeavyImpactSniperRifle=" << (Items.Weapon.HeavyImpactSniperRifle ? "1" : "0") << std::endl;

            // New Weapons
            oss << "WeaponMKSevenAssaultRifle=" << (Items.Weapon.MKSevenAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponNewPump_Shotgun=" << (Items.Weapon.NewPump_Shotgun ? "1" : "0") << std::endl;
            oss << "WeaponOGPump_Shotgun=" << (Items.Weapon.OGPump_Shotgun ? "1" : "0") << std::endl;
            oss << "WeaponBottomlessChugJug=" << (Items.Weapon.BottomlessChugJug ? "1" : "0") << std::endl;
            oss << "WeaponBurstAR=" << (Items.Weapon.BurstAR ? "1" : "0") << std::endl;
            oss << "WeaponDrumGun=" << (Items.Weapon.DrumGun ? "1" : "0") << std::endl;
            oss << "WeaponSkyesAR=" << (Items.Weapon.SkyesAR ? "1" : "0") << std::endl;
            oss << "WeaponGrappler=" << (Items.Weapon.Grappler ? "1" : "0") << std::endl;
            oss << "WeaponStingerSMG=" << (Items.Weapon.StingerSMG ? "1" : "0") << std::endl;
            oss << "WeaponHeistedBreacherShotgun=" << (Items.Weapon.HeistedBreacherShotgun ? "1" : "0") << std::endl;
            oss << "WeaponHeistedAccelerantShotgun=" << (Items.Weapon.HeistedAccelerantShotgun ? "1" : "0") << std::endl;
            oss << "WeaponHeistedExplosiveAR=" << (Items.Weapon.HeistedExplosiveAR ? "1" : "0") << std::endl;
            oss << "WeaponHeistedBlinkMagSMG=" << (Items.Weapon.HeistedBlinkMagSMG ? "1" : "0") << std::endl;
            oss << "WeaponHeistedRunGunSMG=" << (Items.Weapon.HeistedRunGunSMG ? "1" : "0") << std::endl;
            oss << "WeaponTacticalShotgun=" << (Items.Weapon.TacticalShotgun ? "1" : "0") << std::endl;
            oss << "WeaponLeverActionShotgun=" << (Items.Weapon.LeverActionShotgun ? "1" : "0") << std::endl;
            oss << "WeaponHeavyShotgun=" << (Items.Weapon.HeavyShotgun ? "1" : "0") << std::endl;
            oss << "WeaponRangerShotgun=" << (Items.Weapon.RangerShotgun ? "1" : "0") << std::endl;
            oss << "WeaponAssaultRifle=" << (Items.Weapon.AssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponScarAssaultRifle=" << (Items.Weapon.ScarAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponHammerAssaultRifle=" << (Items.Weapon.HammerAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponHeavyAssaultRifle=" << (Items.Weapon.HeavyAssaultRifle ? "1" : "0") << std::endl;
            oss << "WeaponInfantryRifle=" << (Items.Weapon.InfantryRifle ? "1" : "0") << std::endl;
            oss << "WeaponSubmachineGun=" << (Items.Weapon.SubmachineGun ? "1" : "0") << std::endl;
            oss << "WeaponTacticalSubmachineGun=" << (Items.Weapon.TacticalSubmachineGun ? "1" : "0") << std::endl;
            oss << "WeaponBoltActionSniperRifle=" << (Items.Weapon.BoltActionSniperRifle ? "1" : "0") << std::endl;
            oss << "WeaponHuntingRifle=" << (Items.Weapon.HuntingRifle ? "1" : "0") << std::endl;
            oss << "WeaponPistol=" << (Items.Weapon.Pistol ? "1" : "0") << std::endl;
            oss << "WeaponRevolver=" << (Items.Weapon.Revolver ? "1" : "0") << std::endl;
            oss << "WeaponRocketLauncher=" << (Items.Weapon.RocketLauncher ? "1" : "0") << std::endl;
            oss << "WeaponCrashPad=" << (Items.Weapon.CrashPad ? "1" : "0") << std::endl;

            // Ammos
            oss << "AmmoEnable=" << (Items.Ammo.Enable ? "1" : "0") << std::endl;
            oss << "AmmoName=" << (Items.Ammo.Name ? "1" : "0") << std::endl;
            oss << "AmmoDistance=" << (Items.Ammo.Distance ? "1" : "0") << std::endl;
            oss << "AmmoIcons=" << (Items.Ammo.Icons ? "1" : "0") << std::endl;
            oss << "AmmoIconSize=" << Items.Ammo.IconSize << std::endl;
            oss << "AmmoMaxDistance=" << Items.Ammo.MaxDistance << std::endl;
            oss << "AmmoFontSize=" << Items.Ammo.FontSize << std::endl;
            oss << "AmmoLight=" << (Items.Ammo.AmmoLight ? "1" : "0") << std::endl;
            oss << "AmmoMedium=" << (Items.Ammo.AmmoMedium ? "1" : "0") << std::endl;
            oss << "AmmoHeavy=" << (Items.Ammo.AmmoHeavy ? "1" : "0") << std::endl;
            oss << "AmmoShells=" << (Items.Ammo.AmmoShells ? "1" : "0") << std::endl;
            oss << "AmmoRockets=" << (Items.Ammo.AmmoRockets ? "1" : "0") << std::endl;

            // Others
            oss << "OtherEnable=" << (Items.Other.Enable ? "1" : "0") << std::endl;
            oss << "OtherName=" << (Items.Other.Name ? "1" : "0") << std::endl;
            oss << "OtherDistance=" << (Items.Other.Distance ? "1" : "0") << std::endl;
            oss << "OtherIcons=" << (Items.Other.Icons ? "1" : "0") << std::endl;
            oss << "OtherIconSize=" << Items.Other.IconSize << std::endl;
            oss << "OtherMaxDistance=" << Items.Other.MaxDistance << std::endl;
            oss << "OtherFontSize=" << Items.Other.FontSize << std::endl;
            oss << "OtherChest=" << (Items.Other.Chest ? "1" : "0") << std::endl;
            oss << "OtherVehicle=" << (Items.Other.Vehicle ? "1" : "0") << std::endl;
            oss << "OtherLlama=" << (Items.Other.Llama ? "1" : "0") << std::endl;
            oss << "OtherSupplyDrop=" << (Items.Other.SupplyDrop ? "1" : "0") << std::endl;

            oss << "[Players]" << std::endl;
            oss << "Enable=" << (Players.Enable ? "1" : "0") << std::endl;
            oss << "MaxDistance=" << Players.MaxDistance << std::endl;
            oss << "FontSize=" << Players.FontSize << std::endl;
            oss << "Box=" << (Players.Box ? "1" : "0") << std::endl;
            oss << "BoxStyle1=" << (Players.BoxStyle[0] ? "1" : "0") << std::endl;
            oss << "BoxStyle2=" << (Players.BoxStyle[1] ? "1" : "0") << std::endl;
            oss << "BoxType=" << Players.BoxType << std::endl;
            oss << "BoxThickness=" << Players.BoxThickness << std::endl;
            oss << "BoxRounding=" << Players.BoxRounding << std::endl;
            oss << "IgnoreDowned=" << (Players.IgnoreDowned ? "1" : "0") << std::endl;
            oss << "HeadCircle=" << (Players.HeadCircle ? "1" : "0") << std::endl;
            oss << "HeadCircleThickness=" << Players.HeadCircleThickness << std::endl;
            oss << "Lines=" << (Players.Lines ? "1" : "0") << std::endl;
            oss << "LinesPosition=" << Players.LinesPosition << std::endl;
            oss << "LinesThickness=" << Players.LinesThickness << std::endl;
            oss << "Distance=" << (Players.Distance ? "1" : "0") << std::endl;
            oss << "NickName=" << (Players.NickName ? "1" : "0") << std::endl;
            oss << "Platform=" << (Players.Platform ? "1" : "0") << std::endl;
            oss << "Kills=" << (Players.Kills ? "1" : "0") << std::endl;
            oss << "Level=" << (Players.Level ? "1" : "0") << std::endl;
            oss << "Rank=" << (Players.Rank ? "1" : "0") << std::endl;
            oss << "Weapon=" << (Players.Weapon ? "1" : "0") << std::endl;
            oss << "WeaponRarity=" << (Players.WeaponRarity ? "1" : "0") << std::endl;
            oss << "WeaponAmmo=" << (Players.WeaponAmmo ? "1" : "0") << std::endl;
            oss << "Skeleton=" << (Players.Skeleton ? "1" : "0") << std::endl;
            oss << "PlayerAi=" << (Players.PlayerAi ? "1" : "0") << std::endl;
            oss << "SkeletonThickness=" << Players.SkeletonThickness << std::endl;

            oss << "[Keys]" << std::endl;
            // Basic keys
            oss << "HoldTrigger=" << Keys.HoldTrigger << std::endl;
            oss << "HoldPrimary=" << Keys.HoldPrimary << std::endl;
            oss << "HoldSecondary=" << Keys.HoldSecondary << std::endl;
            oss << "SwitchToHead=" << Keys.SwitchToHead << std::endl;
            oss << "Toggle=" << Keys.Toggle << std::endl;
            oss << "TogglePlayers=" << Keys.TogglePlayers << std::endl;
            oss << "ToggleItems=" << Keys.ToggleItems << std::endl;
            oss << "ToggleRadar=" << Keys.ToggleRadar << std::endl;
            oss << "TogglePanicMode=" << Keys.TogglePanicMode << std::endl;
            oss << "Menu=" << Keys.Menu << std::endl;

            // Aimbot feature hotkeys
            oss << "AimbotEnable=" << Keys.AimbotEnable << std::endl;
            oss << "AimbotAimLock=" << Keys.AimbotAimLock << std::endl;
            oss << "AimbotPrediction=" << Keys.AimbotPrediction << std::endl;
            oss << "AimbotSaveTarget=" << Keys.AimbotSaveTarget << std::endl;
            oss << "AimbotVisibilityCheck=" << Keys.AimbotVisibilityCheck << std::endl;
            oss << "AimbotHumanizedSmooth=" << Keys.AimbotHumanizedSmooth << std::endl;
            oss << "AimbotIgnoreDowned=" << Keys.AimbotIgnoreDowned << std::endl;
            oss << "AimbotPlayerAi=" << Keys.AimbotPlayerAi << std::endl;
            oss << "AimbotWeaponOnly=" << Keys.AimbotWeaponOnly << std::endl;
            oss << "AimbotDrawFov=" << Keys.AimbotDrawFov << std::endl;
            oss << "AimbotDrawCrossHair=" << Keys.AimbotDrawCrossHair << std::endl;
            oss << "AimbotDrawTarget=" << Keys.AimbotDrawTarget << std::endl;

            // Player ESP feature hotkeys
            oss << "PlayerEspEnable=" << Keys.PlayerEspEnable << std::endl;
            oss << "PlayerEspBox=" << Keys.PlayerEspBox << std::endl;
            oss << "PlayerEspSkeleton=" << Keys.PlayerEspSkeleton << std::endl;
            oss << "PlayerEspHeadCircle=" << Keys.PlayerEspHeadCircle << std::endl;
            oss << "PlayerEspLines=" << Keys.PlayerEspLines << std::endl;
            oss << "PlayerEspDistance=" << Keys.PlayerEspDistance << std::endl;
            oss << "PlayerEspNickName=" << Keys.PlayerEspNickName << std::endl;

            // Item ESP feature hotkeys
            oss << "ItemEspEnable=" << Keys.ItemEspEnable << std::endl;
            oss << "ItemConsumableEnable=" << Keys.ItemConsumableEnable << std::endl;
            oss << "ItemWeaponEnable=" << Keys.ItemWeaponEnable << std::endl;
            oss << "ItemAmmoEnable=" << Keys.ItemAmmoEnable << std::endl;
            oss << "ItemOtherEnable=" << Keys.ItemOtherEnable << std::endl;

            // Radar feature hotkeys
            oss << "RadarEnable=" << Keys.RadarEnable << std::endl;
            oss << "RadarDistance=" << Keys.RadarDistance << std::endl;
            oss << "RadarVisibleColor=" << Keys.RadarVisibleColor << std::endl;
            oss << "RadarClosestColor=" << Keys.RadarClosestColor << std::endl;
            oss << "RadarAimingAtMeColor=" << Keys.RadarAimingAtMeColor << std::endl;

            // Hotkey modes (true = toggle, false = hold)
            std::cout << "Saving hotkey modes:" << std::endl;
            std::cout << "AimbotEnableMode: " << (Keys.AimbotEnableMode ? "Toggle (1)" : "Hold (0)") << std::endl;
            std::cout << "AimbotAimLockMode: " << (Keys.AimbotAimLockMode ? "Toggle (1)" : "Hold (0)") << std::endl;
            std::cout << "PlayerEspEnableMode: " << (Keys.PlayerEspEnableMode ? "Toggle (1)" : "Hold (0)") << std::endl;

            // Make sure we're using the latest values from Settings struct
            Keys.AimbotEnableMode = Keys.AimbotEnableMode;
            Keys.AimbotAimLockMode = Keys.AimbotAimLockMode;
            Keys.PlayerEspEnableMode = Keys.PlayerEspEnableMode;

            oss << "AimbotEnableMode=" << (Keys.AimbotEnableMode ? "1" : "0") << std::endl;
            oss << "AimbotAimLockMode=" << (Keys.AimbotAimLockMode ? "1" : "0") << std::endl;
            oss << "AimbotPredictionMode=" << (Keys.AimbotPredictionMode ? "1" : "0") << std::endl;
            oss << "AimbotSaveTargetMode=" << (Keys.AimbotSaveTargetMode ? "1" : "0") << std::endl;
            oss << "AimbotVisibilityCheckMode=" << (Keys.AimbotVisibilityCheckMode ? "1" : "0") << std::endl;
            oss << "AimbotHumanizedSmoothMode=" << (Keys.AimbotHumanizedSmoothMode ? "1" : "0") << std::endl;
            oss << "AimbotIgnoreDownedMode=" << (Keys.AimbotIgnoreDownedMode ? "1" : "0") << std::endl;
            oss << "AimbotPlayerAiMode=" << (Keys.AimbotPlayerAiMode ? "1" : "0") << std::endl;
            oss << "AimbotWeaponOnlyMode=" << (Keys.AimbotWeaponOnlyMode ? "1" : "0") << std::endl;
            oss << "AimbotDrawFovMode=" << (Keys.AimbotDrawFovMode ? "1" : "0") << std::endl;
            oss << "AimbotDrawCrossHairMode=" << (Keys.AimbotDrawCrossHairMode ? "1" : "0") << std::endl;
            oss << "AimbotDrawTargetMode=" << (Keys.AimbotDrawTargetMode ? "1" : "0") << std::endl;

            oss << "PlayerEspEnableMode=" << (Keys.PlayerEspEnableMode ? "1" : "0") << std::endl;
            oss << "PlayerEspBoxMode=" << (Keys.PlayerEspBoxMode ? "1" : "0") << std::endl;
            oss << "PlayerEspSkeletonMode=" << (Keys.PlayerEspSkeletonMode ? "1" : "0") << std::endl;
            oss << "PlayerEspHeadCircleMode=" << (Keys.PlayerEspHeadCircleMode ? "1" : "0") << std::endl;
            oss << "PlayerEspLinesMode=" << (Keys.PlayerEspLinesMode ? "1" : "0") << std::endl;
            oss << "PlayerEspDistanceMode=" << (Keys.PlayerEspDistanceMode ? "1" : "0") << std::endl;
            oss << "PlayerEspNickNameMode=" << (Keys.PlayerEspNickNameMode ? "1" : "0") << std::endl;

            oss << "ItemEspEnableMode=" << (Keys.ItemEspEnableMode ? "1" : "0") << std::endl;
            oss << "ItemConsumableEnableMode=" << (Keys.ItemConsumableEnableMode ? "1" : "0") << std::endl;
            oss << "ItemWeaponEnableMode=" << (Keys.ItemWeaponEnableMode ? "1" : "0") << std::endl;
            oss << "ItemAmmoEnableMode=" << (Keys.ItemAmmoEnableMode ? "1" : "0") << std::endl;
            oss << "ItemOtherEnableMode=" << (Keys.ItemOtherEnableMode ? "1" : "0") << std::endl;

            oss << "RadarEnableMode=" << (Keys.RadarEnableMode ? "1" : "0") << std::endl;
            oss << "RadarDistanceMode=" << (Keys.RadarDistanceMode ? "1" : "0") << std::endl;
            oss << "RadarVisibleColorMode=" << (Keys.RadarVisibleColorMode ? "1" : "0") << std::endl;
            oss << "RadarClosestColorMode=" << (Keys.RadarClosestColorMode ? "1" : "0") << std::endl;
            oss << "RadarAimingAtMeColorMode=" << (Keys.RadarAimingAtMeColorMode ? "1" : "0") << std::endl;

            oss << "[AimbotColors]" << std::endl;
            oss << "Fov=" << AimbotColors.Fov[0] << "," << AimbotColors.Fov[1] << "," << AimbotColors.Fov[2] << std::endl;
            oss << "CrossHair=" << AimbotColors.CrossHair[0] << "," << AimbotColors.CrossHair[1] << "," << AimbotColors.CrossHair[2] << std::endl;
            oss << "Target=" << AimbotColors.Target[0] << "," << AimbotColors.Target[1] << "," << AimbotColors.Target[2] << std::endl;

            oss << "[PlayersColors]" << std::endl;
            oss << "BoxVisible=" << PlayersColors.BoxVisible[0] << "," << PlayersColors.BoxVisible[1] << "," << PlayersColors.BoxVisible[2] << std::endl;
            oss << "BoxNonVisible=" << PlayersColors.BoxNonVisible[0] << "," << PlayersColors.BoxNonVisible[1] << "," << PlayersColors.BoxNonVisible[2] << std::endl;

            oss << "KnockedBoxVisible=" << PlayersColors.KnockedBoxVisible[0] << "," << PlayersColors.KnockedBoxVisible[1] << "," << PlayersColors.KnockedBoxVisible[2] << std::endl;
            oss << "KnockedBoxNonVisible=" << PlayersColors.KnockedBoxNonVisible[0] << "," << PlayersColors.KnockedBoxNonVisible[1] << "," << PlayersColors.KnockedBoxNonVisible[2] << std::endl;

            oss << "BoxFillVisible=" << PlayersColors.BoxFillVisible[0] << "," << PlayersColors.BoxFillVisible[1] << "," << PlayersColors.BoxFillVisible[2] << "," << PlayersColors.BoxFillVisible[3] << std::endl;
            oss << "BoxFillNonVisible=" << PlayersColors.BoxFillNonVisible[0] << "," << PlayersColors.BoxFillNonVisible[1] << "," << PlayersColors.BoxFillNonVisible[2] << "," << PlayersColors.BoxFillNonVisible[3] << std::endl;

            oss << "KnockedBoxFillVisible=" << PlayersColors.KnockedBoxFillVisible[0] << "," << PlayersColors.KnockedBoxFillVisible[1] << "," << PlayersColors.KnockedBoxFillVisible[2] << "," << PlayersColors.KnockedBoxFillVisible[3] << std::endl;
            oss << "KnockedBoxFillNonVisible=" << PlayersColors.KnockedBoxFillNonVisible[0] << "," << PlayersColors.KnockedBoxFillNonVisible[1] << "," << PlayersColors.KnockedBoxFillNonVisible[2] << "," << PlayersColors.KnockedBoxFillNonVisible[3] << std::endl;

            oss << "SkeletonVisible=" << PlayersColors.SkeletonVisible[0] << "," << PlayersColors.SkeletonVisible[1] << "," << PlayersColors.SkeletonVisible[2] << std::endl;
            oss << "SkeletonNonVisible=" << PlayersColors.SkeletonNonVisible[0] << "," << PlayersColors.SkeletonNonVisible[1] << "," << PlayersColors.SkeletonNonVisible[2] << std::endl;

            oss << "KnockedSkeletonVisible=" << PlayersColors.KnockedSkeletonVisible[0] << "," << PlayersColors.KnockedSkeletonVisible[1] << "," << PlayersColors.KnockedSkeletonVisible[2] << std::endl;
            oss << "KnockedSkeletonNonVisible=" << PlayersColors.KnockedSkeletonNonVisible[0] << "," << PlayersColors.KnockedSkeletonNonVisible[1] << "," << PlayersColors.KnockedSkeletonNonVisible[2] << std::endl;

            oss << "HeadVisible=" << PlayersColors.HeadVisible[0] << "," << PlayersColors.HeadVisible[1] << "," << PlayersColors.HeadVisible[2] << std::endl;
            oss << "HeadNonVisible=" << PlayersColors.HeadNonVisible[0] << "," << PlayersColors.HeadNonVisible[1] << "," << PlayersColors.HeadNonVisible[2] << std::endl;

            oss << "KnockedHeadVisible=" << PlayersColors.KnockedHeadVisible[0] << "," << PlayersColors.KnockedHeadVisible[1] << "," << PlayersColors.KnockedHeadVisible[2] << std::endl;
            oss << "KnockedHeadNonVisible=" << PlayersColors.KnockedHeadNonVisible[0] << "," << PlayersColors.KnockedHeadNonVisible[1] << "," << PlayersColors.KnockedHeadNonVisible[2] << std::endl;

            oss << "LineVisible=" << PlayersColors.LineVisible[0] << "," << PlayersColors.LineVisible[1] << "," << PlayersColors.LineVisible[2] << std::endl;
            oss << "LineNonVisible=" << PlayersColors.LineNonVisible[0] << "," << PlayersColors.LineNonVisible[1] << "," << PlayersColors.LineNonVisible[2] << std::endl;

            oss << "KnockedLineVisible=" << PlayersColors.KnockedLineVisible[0] << "," << PlayersColors.KnockedLineVisible[1] << "," << PlayersColors.KnockedLineVisible[2] << std::endl;
            oss << "KnockedLineNonVisible=" << PlayersColors.KnockedLineNonVisible[0] << "," << PlayersColors.KnockedLineNonVisible[1] << "," << PlayersColors.KnockedLineNonVisible[2] << std::endl;

            oss << "RadarVisible=" << PlayersColors.RadarVisible[0] << "," << PlayersColors.RadarVisible[1] << "," << PlayersColors.RadarVisible[2] << std::endl;
            oss << "RadarNonVisible=" << PlayersColors.RadarNonVisible[0] << "," << PlayersColors.RadarNonVisible[1] << "," << PlayersColors.RadarNonVisible[2] << std::endl;

            oss << "KnockedRadarVisible=" << PlayersColors.KnockedRadarVisible[0] << "," << PlayersColors.KnockedRadarVisible[1] << "," << PlayersColors.KnockedRadarVisible[2] << std::endl;
            oss << "KnockedRadarNonVisible=" << PlayersColors.KnockedRadarNonVisible[0] << "," << PlayersColors.KnockedRadarNonVisible[1] << "," << PlayersColors.KnockedRadarNonVisible[2] << std::endl;

            oss << "Distance=" << PlayersColors.Distance[0] << "," << PlayersColors.Distance[1] << "," << PlayersColors.Distance[2] << std::endl;
            oss << "NickName=" << PlayersColors.NickName[0] << "," << PlayersColors.NickName[1] << "," << PlayersColors.NickName[2] << std::endl;


            oss << "[Radar]" << std::endl;
            oss << "Enable=" << (Radar.Enable ? "1" : "0") << std::endl;
            oss << "Distance=" << (Radar.Distance ? "1" : "0") << std::endl;
            oss << "PositionX=" << Radar.PositionX << std::endl;
            oss << "PositionY=" << Radar.PositionY << std::endl;
            oss << "CirleSize=" << Radar.CirleSize << std::endl;
            oss << "RectangleSize=" << Radar.RectangleSize << std::endl;
            oss << "VisibleColor=" << (Radar.VisibleColor ? "1" : "0") << std::endl;
            oss << "ClosestColor=" << (Radar.ClosestColor ? "1" : "0") << std::endl;
            oss << "MaxDistance=" << Radar.MaxDistance << std::endl;
            oss << "DistanceFontSize=" << Radar.DistanceFontSize << std::endl;
            oss << "RadarType=" << Radar.RadarType << std::endl;

            oss << "[ConfigsMenu]" << std::endl;
            oss << "iConfig=" << ConfigsMenu.iConfig << std::endl;
            oss << "oldiConfig=" << ConfigsMenu.oldiConfig << std::endl;
            oss << "selectedWeapon=" << ConfigsMenu.selectedWeapon << std::endl;
            oss << "Language=" << ConfigsMenu.Language << std::endl;
            oss << "FeaturesDefinition=" << (ConfigsMenu.FeaturesDefinition ? "1" : "0") << std::endl;

            oss << "[Notifications]" << std::endl;
            oss << "Enable=" << (g_NotificationSettings.Enable ? "1" : "0") << std::endl;
            oss << "DisplayDuration=" << g_NotificationSettings.DisplayDuration << std::endl;

            // Write the settings to the file
            std::string output = oss.str();
            fprintf(file, "%s", output.c_str());
            fclose(file);

            // Add hotkey settings to the HotkeyManager
            // HotkeyManager::Initialize(); // Removed from here
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error saving configuration: " << e.what() << std::endl;
        return false;
    }
}

// Initialize the configuration system
void InitializeConfigSystem() {
    // Ensure config directory exists
    if (!std::filesystem::exists(g_ConfigSystem.configFolder)) {
        std::filesystem::create_directories(g_ConfigSystem.configFolder);
    }

    // Load existing configurations
    RefreshConfigurations();

    // Select default config if none is selected
    if (g_ConfigSystem.selectedConfigIndex == -1) {
        SelectDefaultConfiguration();
    }
}

// Refresh the list of available configurations
void RefreshConfigurations() {
    g_ConfigSystem.configs.clear();

    // Create default config if it doesn't exist
    std::string defaultConfigPath = g_ConfigSystem.configFolder + "Default.cfg";
    bool defaultExists = false;

    try {
        // Check if config directory exists, create if not
        if (!std::filesystem::exists(g_ConfigSystem.configFolder)) {
            std::filesystem::create_directories(g_ConfigSystem.configFolder);
        }

        // Iterate through config directory
        for (const auto& entry : std::filesystem::directory_iterator(g_ConfigSystem.configFolder)) {
            if (entry.is_regular_file() && entry.path().extension() == ".cfg") {
                std::string filename = entry.path().filename().string();
                std::string name = filename.substr(0, filename.size() - 4); // Remove .cfg

                // Check if this is the default config
                bool isDefault = (filename == "Default.cfg");
                if (isDefault) defaultExists = true;

                ConfigFile config;
                config.name = name;
                config.type = isDefault ? "Default" : "Custom";
                config.filename = filename;
                config.isSelected = false;
                config.isDefault = isDefault;
                config.isCloudConfig = false; // Local config by default

                // Convert filesystem time to system_clock time
                auto fsTime = entry.last_write_time();
                auto fsTimePoint = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    fsTime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
                config.lastModified = fsTimePoint;

                g_ConfigSystem.configs.push_back(config);
            }
        }

        // Create default config if it doesn't exist
        if (!defaultExists) {
            SaveConfiguration("Default.cfg");

            ConfigFile defaultConfig;
            defaultConfig.name = "Default";
            defaultConfig.type = "Default";
            defaultConfig.filename = "Default.cfg";
            defaultConfig.isSelected = true;
            defaultConfig.isDefault = true;
            defaultConfig.isCloudConfig = false;
            defaultConfig.lastModified = std::chrono::system_clock::now();

            g_ConfigSystem.configs.push_back(defaultConfig);
        }

        // Sort configs by last modified date (newest first)
        std::sort(g_ConfigSystem.configs.begin(), g_ConfigSystem.configs.end(),
            [](const ConfigFile& a, const ConfigFile& b) {
                if (a.isDefault) return true; // Default should always be first
                if (b.isDefault) return false;
                return a.lastModified > b.lastModified;
            });
    }
    catch (const std::exception& e) {
        // Handle exception
    }
}

// Save current settings to a configuration file
bool SaveConfiguration(const std::string& filename) {
    try {
        std::string filepath = g_ConfigSystem.configFolder + filename;

        // Ensure the directory exists
        if (!std::filesystem::exists(g_ConfigSystem.configFolder)) {
            std::filesystem::create_directories(g_ConfigSystem.configFolder);
        }

        // Use an independent file stream to ensure thread safety
        std::ofstream outFile(filepath);
        if (!outFile.is_open()) {
            return false;
        }

        std::ostringstream oss;

        // Create the config file with current settings
        oss << "[Aim]" << std::endl;
        SaveAimbotSettings(oss, Aimbot);
        if (Aimbot.ByWeapon) {
            for (const auto& weapon : weaponSettings.weaponAimSettings) {
                oss << "[AimNew" << weapon.first << "]" << std::endl;
                SaveAimbotSettings(oss, weapon.second);
            }
        }

        oss << "[Trigger]" << std::endl;
        SaveTriggerbotSettings(oss, Triggerbot);

        // Save items settings
        oss << "[Items]" << std::endl;
        oss << "ConsumableEnable=" << (Items.Consumable.Enable ? "1" : "0") << std::endl;
        oss << "ConsumableName=" << (Items.Consumable.Name ? "1" : "0") << std::endl;
        oss << "ConsumableDistance=" << (Items.Consumable.Distance ? "1" : "0") << std::endl;
        oss << "ConsumableIcons=" << (Items.Consumable.Icons ? "1" : "0") << std::endl;
        oss << "ConsumableIconSize=" << Items.Consumable.IconSize << std::endl;
        oss << "ConsumableMaxDistance=" << Items.Consumable.MaxDistance << std::endl;
        oss << "ConsumableFontSize=" << Items.Consumable.FontSize << std::endl;
        oss << "ConsumableBandages=" << (Items.Consumable.Bandages ? "1" : "0") << std::endl;
        oss << "ConsumableMedkit=" << (Items.Consumable.Medkit ? "1" : "0") << std::endl;
        oss << "ConsumableSmallShieldPotion=" << (Items.Consumable.SmallShieldPotion ? "1" : "0") << std::endl;
        oss << "ConsumableShieldPotion=" << (Items.Consumable.ShieldPotion ? "1" : "0") << std::endl;
        oss << "ConsumableFlowBerryFizz=" << (Items.Consumable.FlowBerryFizz ? "1" : "0") << std::endl;
        oss << "ConsumableChugSplash=" << (Items.Consumable.ChugSplash ? "1" : "0") << std::endl;
        oss << "ConsumableNitroSplash=" << (Items.Consumable.NitroSplash ? "1" : "0") << std::endl;
        oss << "ConsumableNukaCola=" << (Items.Consumable.NukaCola ? "1" : "0") << std::endl;

        // Weapons
        oss << "WeaponEnable=" << (Items.Weapon.Enable ? "1" : "0") << std::endl;
        oss << "WeaponName=" << (Items.Weapon.Name ? "1" : "0") << std::endl;
        oss << "WeaponDistance=" << (Items.Weapon.Distance ? "1" : "0") << std::endl;
        oss << "WeaponIcons=" << (Items.Weapon.Icons ? "1" : "0") << std::endl;
        oss << "WeaponIconSize=" << Items.Weapon.IconSize << std::endl;
        oss << "WeaponMaxDistance=" << Items.Weapon.MaxDistance << std::endl;
        oss << "WeaponFontSize=" << Items.Weapon.FontSize << std::endl;
        oss << "WeaponRangerPistol=" << (Items.Weapon.RangerPistol ? "1" : "0") << std::endl;
        oss << "WeaponHarbingerSMG=" << (Items.Weapon.HarbingerSMG ? "1" : "0") << std::endl;
        oss << "WeaponThunderBurstSMG=" << (Items.Weapon.ThunderBurstSMG ? "1" : "0") << std::endl;
        oss << "WeaponWarforgedAssaultRifle=" << (Items.Weapon.WarforgedAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponTacticalAssaultRifle=" << (Items.Weapon.TacticalAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponEnforcerAssaultRifle=" << (Items.Weapon.EnforcerAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponCombatAssaultRifle=" << (Items.Weapon.CombatAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponCombatShotgun=" << (Items.Weapon.CombatShotgun ? "1" : "0") << std::endl;
        oss << "WeaponGatekeeperShotgun=" << (Items.Weapon.GatekeeperShotgun ? "1" : "0") << std::endl;
        oss << "WeaponHammerPumpShotgun=" << (Items.Weapon.HammerPumpShotgun ? "1" : "0") << std::endl;
        oss << "WeaponFrenzyAutoShotgun=" << (Items.Weapon.FrenzyAutoShotgun ? "1" : "0") << std::endl;
        oss << "WeaponHandCannon=" << (Items.Weapon.HandCannon ? "1" : "0") << std::endl;
        oss << "WeaponHuntressDMR=" << (Items.Weapon.HuntressDMR ? "1" : "0") << std::endl;
        oss << "WeaponBoomBolt=" << (Items.Weapon.BoomBolt ? "1" : "0") << std::endl;
        oss << "WeaponNitroFists=" << (Items.Weapon.NitroFists ? "1" : "0") << std::endl;
        oss << "WeaponShockwavegrenade=" << (Items.Weapon.Shockwavegrenade ? "1" : "0") << std::endl;
        oss << "WeaponHeavyImpactSniperRifle=" << (Items.Weapon.HeavyImpactSniperRifle ? "1" : "0") << std::endl;

        // New Weapons
        oss << "WeaponMKSevenAssaultRifle=" << (Items.Weapon.MKSevenAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponNewPump_Shotgun=" << (Items.Weapon.NewPump_Shotgun ? "1" : "0") << std::endl;
        oss << "WeaponOGPump_Shotgun=" << (Items.Weapon.OGPump_Shotgun ? "1" : "0") << std::endl;
        oss << "WeaponBottomlessChugJug=" << (Items.Weapon.BottomlessChugJug ? "1" : "0") << std::endl;
        oss << "WeaponBurstAR=" << (Items.Weapon.BurstAR ? "1" : "0") << std::endl;
        oss << "WeaponDrumGun=" << (Items.Weapon.DrumGun ? "1" : "0") << std::endl;
        oss << "WeaponSkyesAR=" << (Items.Weapon.SkyesAR ? "1" : "0") << std::endl;
        oss << "WeaponGrappler=" << (Items.Weapon.Grappler ? "1" : "0") << std::endl;
        oss << "WeaponStingerSMG=" << (Items.Weapon.StingerSMG ? "1" : "0") << std::endl;
        oss << "WeaponHeistedBreacherShotgun=" << (Items.Weapon.HeistedBreacherShotgun ? "1" : "0") << std::endl;
        oss << "WeaponHeistedAccelerantShotgun=" << (Items.Weapon.HeistedAccelerantShotgun ? "1" : "0") << std::endl;
        oss << "WeaponHeistedExplosiveAR=" << (Items.Weapon.HeistedExplosiveAR ? "1" : "0") << std::endl;
        oss << "WeaponHeistedBlinkMagSMG=" << (Items.Weapon.HeistedBlinkMagSMG ? "1" : "0") << std::endl;
        oss << "WeaponHeistedRunGunSMG=" << (Items.Weapon.HeistedRunGunSMG ? "1" : "0") << std::endl;
        oss << "WeaponTacticalShotgun=" << (Items.Weapon.TacticalShotgun ? "1" : "0") << std::endl;
        oss << "WeaponLeverActionShotgun=" << (Items.Weapon.LeverActionShotgun ? "1" : "0") << std::endl;
        oss << "WeaponHeavyShotgun=" << (Items.Weapon.HeavyShotgun ? "1" : "0") << std::endl;
        oss << "WeaponRangerShotgun=" << (Items.Weapon.RangerShotgun ? "1" : "0") << std::endl;
        oss << "WeaponAssaultRifle=" << (Items.Weapon.AssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponScarAssaultRifle=" << (Items.Weapon.ScarAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponHammerAssaultRifle=" << (Items.Weapon.HammerAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponHeavyAssaultRifle=" << (Items.Weapon.HeavyAssaultRifle ? "1" : "0") << std::endl;
        oss << "WeaponInfantryRifle=" << (Items.Weapon.InfantryRifle ? "1" : "0") << std::endl;
        oss << "WeaponSubmachineGun=" << (Items.Weapon.SubmachineGun ? "1" : "0") << std::endl;
        oss << "WeaponTacticalSubmachineGun=" << (Items.Weapon.TacticalSubmachineGun ? "1" : "0") << std::endl;
        oss << "WeaponBoltActionSniperRifle=" << (Items.Weapon.BoltActionSniperRifle ? "1" : "0") << std::endl;
        oss << "WeaponHuntingRifle=" << (Items.Weapon.HuntingRifle ? "1" : "0") << std::endl;
        oss << "WeaponPistol=" << (Items.Weapon.Pistol ? "1" : "0") << std::endl;
        oss << "WeaponRevolver=" << (Items.Weapon.Revolver ? "1" : "0") << std::endl;
        oss << "WeaponRocketLauncher=" << (Items.Weapon.RocketLauncher ? "1" : "0") << std::endl;
        oss << "WeaponCrashPad=" << (Items.Weapon.CrashPad ? "1" : "0") << std::endl;

        // Ammos
        oss << "AmmoEnable=" << (Items.Ammo.Enable ? "1" : "0") << std::endl;
        oss << "AmmoName=" << (Items.Ammo.Name ? "1" : "0") << std::endl;
        oss << "AmmoDistance=" << (Items.Ammo.Distance ? "1" : "0") << std::endl;
        oss << "AmmoIcons=" << (Items.Ammo.Icons ? "1" : "0") << std::endl;
        oss << "AmmoIconSize=" << Items.Ammo.IconSize << std::endl;
        oss << "AmmoMaxDistance=" << Items.Ammo.MaxDistance << std::endl;
        oss << "AmmoFontSize=" << Items.Ammo.FontSize << std::endl;
        oss << "AmmoLight=" << (Items.Ammo.AmmoLight ? "1" : "0") << std::endl;
        oss << "AmmoMedium=" << (Items.Ammo.AmmoMedium ? "1" : "0") << std::endl;
        oss << "AmmoHeavy=" << (Items.Ammo.AmmoHeavy ? "1" : "0") << std::endl;
        oss << "AmmoShells=" << (Items.Ammo.AmmoShells ? "1" : "0") << std::endl;
        oss << "AmmoRockets=" << (Items.Ammo.AmmoRockets ? "1" : "0") << std::endl;

        // Others
        oss << "OtherEnable=" << (Items.Other.Enable ? "1" : "0") << std::endl;
        oss << "OtherName=" << (Items.Other.Name ? "1" : "0") << std::endl;
        oss << "OtherDistance=" << (Items.Other.Distance ? "1" : "0") << std::endl;
        oss << "OtherIcons=" << (Items.Other.Icons ? "1" : "0") << std::endl;
        oss << "OtherIconSize=" << Items.Other.IconSize << std::endl;
        oss << "OtherMaxDistance=" << Items.Other.MaxDistance << std::endl;
        oss << "OtherFontSize=" << Items.Other.FontSize << std::endl;
        oss << "OtherChest=" << (Items.Other.Chest ? "1" : "0") << std::endl;
        oss << "OtherVehicle=" << (Items.Other.Vehicle ? "1" : "0") << std::endl;
        oss << "OtherLlama=" << (Items.Other.Llama ? "1" : "0") << std::endl;
        oss << "OtherSupplyDrop=" << (Items.Other.SupplyDrop ? "1" : "0") << std::endl;

        oss << "[Players]" << std::endl;
        oss << "Enable=" << (Players.Enable ? "1" : "0") << std::endl;
        oss << "MaxDistance=" << Players.MaxDistance << std::endl;
        oss << "FontSize=" << Players.FontSize << std::endl;
        oss << "Box=" << (Players.Box ? "1" : "0") << std::endl;
        oss << "BoxStyle1=" << (Players.BoxStyle[0] ? "1" : "0") << std::endl;
        oss << "BoxStyle2=" << (Players.BoxStyle[1] ? "1" : "0") << std::endl;
        oss << "BoxType=" << Players.BoxType << std::endl;
        oss << "BoxThickness=" << Players.BoxThickness << std::endl;
        oss << "BoxRounding=" << Players.BoxRounding << std::endl;
        oss << "IgnoreDowned=" << (Players.IgnoreDowned ? "1" : "0") << std::endl;
        oss << "HeadCircle=" << (Players.HeadCircle ? "1" : "0") << std::endl;
        oss << "HeadCircleThickness=" << Players.HeadCircleThickness << std::endl;
        oss << "Lines=" << (Players.Lines ? "1" : "0") << std::endl;
        oss << "LinesPosition=" << Players.LinesPosition << std::endl;
        oss << "LinesThickness=" << Players.LinesThickness << std::endl;
        oss << "Distance=" << (Players.Distance ? "1" : "0") << std::endl;
        oss << "NickName=" << (Players.NickName ? "1" : "0") << std::endl;
        oss << "Platform=" << (Players.Platform ? "1" : "0") << std::endl;
        oss << "Kills=" << (Players.Kills ? "1" : "0") << std::endl;
        oss << "Level=" << (Players.Level ? "1" : "0") << std::endl;
        oss << "Rank=" << (Players.Rank ? "1" : "0") << std::endl;
        oss << "Weapon=" << (Players.Weapon ? "1" : "0") << std::endl;
        oss << "WeaponRarity=" << (Players.WeaponRarity ? "1" : "0") << std::endl;
        oss << "WeaponAmmo=" << (Players.WeaponAmmo ? "1" : "0") << std::endl;
        oss << "Skeleton=" << (Players.Skeleton ? "1" : "0") << std::endl;
        oss << "PlayerAi=" << (Players.PlayerAi ? "1" : "0") << std::endl;
        oss << "SkeletonThickness=" << Players.SkeletonThickness << std::endl;

        oss << "[Keys]" << std::endl;
        // Basic keys
        oss << "HoldTrigger=" << Keys.HoldTrigger << std::endl;
        oss << "HoldPrimary=" << Keys.HoldPrimary << std::endl;
        oss << "HoldSecondary=" << Keys.HoldSecondary << std::endl;
        oss << "SwitchToHead=" << Keys.SwitchToHead << std::endl;
        oss << "Toggle=" << Keys.Toggle << std::endl;
        oss << "TogglePlayers=" << Keys.TogglePlayers << std::endl;
        oss << "ToggleItems=" << Keys.ToggleItems << std::endl;
        oss << "ToggleRadar=" << Keys.ToggleRadar << std::endl;
        oss << "TogglePanicMode=" << Keys.TogglePanicMode << std::endl;
        oss << "Menu=" << Keys.Menu << std::endl;

        // Aimbot feature hotkeys
        oss << "AimbotEnable=" << Keys.AimbotEnable << std::endl;
        oss << "AimbotAimLock=" << Keys.AimbotAimLock << std::endl;
        oss << "AimbotPrediction=" << Keys.AimbotPrediction << std::endl;
        oss << "AimbotSaveTarget=" << Keys.AimbotSaveTarget << std::endl;
        oss << "AimbotVisibilityCheck=" << Keys.AimbotVisibilityCheck << std::endl;
        oss << "AimbotHumanizedSmooth=" << Keys.AimbotHumanizedSmooth << std::endl;
        oss << "AimbotIgnoreDowned=" << Keys.AimbotIgnoreDowned << std::endl;
        oss << "AimbotPlayerAi=" << Keys.AimbotPlayerAi << std::endl;
        oss << "AimbotWeaponOnly=" << Keys.AimbotWeaponOnly << std::endl;
        oss << "AimbotDrawFov=" << Keys.AimbotDrawFov << std::endl;
        oss << "AimbotDrawCrossHair=" << Keys.AimbotDrawCrossHair << std::endl;
        oss << "AimbotDrawTarget=" << Keys.AimbotDrawTarget << std::endl;

        // Player ESP feature hotkeys
        oss << "PlayerEspEnable=" << Keys.PlayerEspEnable << std::endl;
        oss << "PlayerEspBox=" << Keys.PlayerEspBox << std::endl;
        oss << "PlayerEspSkeleton=" << Keys.PlayerEspSkeleton << std::endl;
        oss << "PlayerEspHeadCircle=" << Keys.PlayerEspHeadCircle << std::endl;
        oss << "PlayerEspLines=" << Keys.PlayerEspLines << std::endl;
        oss << "PlayerEspDistance=" << Keys.PlayerEspDistance << std::endl;
        oss << "PlayerEspNickName=" << Keys.PlayerEspNickName << std::endl;

        // Item ESP feature hotkeys
        oss << "ItemEspEnable=" << Keys.ItemEspEnable << std::endl;
        oss << "ItemConsumableEnable=" << Keys.ItemConsumableEnable << std::endl;
        oss << "ItemWeaponEnable=" << Keys.ItemWeaponEnable << std::endl;
        oss << "ItemAmmoEnable=" << Keys.ItemAmmoEnable << std::endl;
        oss << "ItemOtherEnable=" << Keys.ItemOtherEnable << std::endl;

        // Radar feature hotkeys
        oss << "RadarEnable=" << Keys.RadarEnable << std::endl;
        oss << "RadarDistance=" << Keys.RadarDistance << std::endl;
        oss << "RadarVisibleColor=" << Keys.RadarVisibleColor << std::endl;
        oss << "RadarClosestColor=" << Keys.RadarClosestColor << std::endl;
        oss << "RadarAimingAtMeColor=" << Keys.RadarAimingAtMeColor << std::endl;

        // Hotkey modes (true = toggle, false = hold)
        std::cout << "Saving hotkey modes:" << std::endl;
        std::cout << "AimbotEnableMode: " << (Keys.AimbotEnableMode ? "Toggle (1)" : "Hold (0)") << std::endl;
        std::cout << "AimbotAimLockMode: " << (Keys.AimbotAimLockMode ? "Toggle (1)" : "Hold (0)") << std::endl;
        std::cout << "PlayerEspEnableMode: " << (Keys.PlayerEspEnableMode ? "Toggle (1)" : "Hold (0)") << std::endl;

        // Make sure we're using the latest values from Settings struct
        Keys.AimbotEnableMode = Keys.AimbotEnableMode;
        Keys.AimbotAimLockMode = Keys.AimbotAimLockMode;
        Keys.PlayerEspEnableMode = Keys.PlayerEspEnableMode;

        oss << "AimbotEnableMode=" << (Keys.AimbotEnableMode ? "1" : "0") << std::endl;
        oss << "AimbotAimLockMode=" << (Keys.AimbotAimLockMode ? "1" : "0") << std::endl;
        oss << "AimbotPredictionMode=" << (Keys.AimbotPredictionMode ? "1" : "0") << std::endl;
        oss << "AimbotSaveTargetMode=" << (Keys.AimbotSaveTargetMode ? "1" : "0") << std::endl;
        oss << "AimbotVisibilityCheckMode=" << (Keys.AimbotVisibilityCheckMode ? "1" : "0") << std::endl;
        oss << "AimbotHumanizedSmoothMode=" << (Keys.AimbotHumanizedSmoothMode ? "1" : "0") << std::endl;
        oss << "AimbotIgnoreDownedMode=" << (Keys.AimbotIgnoreDownedMode ? "1" : "0") << std::endl;
        oss << "AimbotPlayerAiMode=" << (Keys.AimbotPlayerAiMode ? "1" : "0") << std::endl;
        oss << "AimbotWeaponOnlyMode=" << (Keys.AimbotWeaponOnlyMode ? "1" : "0") << std::endl;
        oss << "AimbotDrawFovMode=" << (Keys.AimbotDrawFovMode ? "1" : "0") << std::endl;
        oss << "AimbotDrawCrossHairMode=" << (Keys.AimbotDrawCrossHairMode ? "1" : "0") << std::endl;
        oss << "AimbotDrawTargetMode=" << (Keys.AimbotDrawTargetMode ? "1" : "0") << std::endl;

        oss << "PlayerEspEnableMode=" << (Keys.PlayerEspEnableMode ? "1" : "0") << std::endl;
        oss << "PlayerEspBoxMode=" << (Keys.PlayerEspBoxMode ? "1" : "0") << std::endl;
        oss << "PlayerEspSkeletonMode=" << (Keys.PlayerEspSkeletonMode ? "1" : "0") << std::endl;
        oss << "PlayerEspHeadCircleMode=" << (Keys.PlayerEspHeadCircleMode ? "1" : "0") << std::endl;
        oss << "PlayerEspLinesMode=" << (Keys.PlayerEspLinesMode ? "1" : "0") << std::endl;
        oss << "PlayerEspDistanceMode=" << (Keys.PlayerEspDistanceMode ? "1" : "0") << std::endl;
        oss << "PlayerEspNickNameMode=" << (Keys.PlayerEspNickNameMode ? "1" : "0") << std::endl;

        oss << "ItemEspEnableMode=" << (Keys.ItemEspEnableMode ? "1" : "0") << std::endl;
        oss << "ItemConsumableEnableMode=" << (Keys.ItemConsumableEnableMode ? "1" : "0") << std::endl;
        oss << "ItemWeaponEnableMode=" << (Keys.ItemWeaponEnableMode ? "1" : "0") << std::endl;
        oss << "ItemAmmoEnableMode=" << (Keys.ItemAmmoEnableMode ? "1" : "0") << std::endl;
        oss << "ItemOtherEnableMode=" << (Keys.ItemOtherEnableMode ? "1" : "0") << std::endl;

        oss << "RadarEnableMode=" << (Keys.RadarEnableMode ? "1" : "0") << std::endl;
        oss << "RadarDistanceMode=" << (Keys.RadarDistanceMode ? "1" : "0") << std::endl;
        oss << "RadarVisibleColorMode=" << (Keys.RadarVisibleColorMode ? "1" : "0") << std::endl;
        oss << "RadarClosestColorMode=" << (Keys.RadarClosestColorMode ? "1" : "0") << std::endl;
        oss << "RadarAimingAtMeColorMode=" << (Keys.RadarAimingAtMeColorMode ? "1" : "0") << std::endl;

        oss << "[AimbotColors]" << std::endl;
        oss << "Fov=" << AimbotColors.Fov[0] << "," << AimbotColors.Fov[1] << "," << AimbotColors.Fov[2] << std::endl;
        oss << "CrossHair=" << AimbotColors.CrossHair[0] << "," << AimbotColors.CrossHair[1] << "," << AimbotColors.CrossHair[2] << std::endl;
        oss << "Target=" << AimbotColors.Target[0] << "," << AimbotColors.Target[1] << "," << AimbotColors.Target[2] << std::endl;

        oss << "[PlayersColors]" << std::endl;
        oss << "BoxVisible=" << PlayersColors.BoxVisible[0] << "," << PlayersColors.BoxVisible[1] << "," << PlayersColors.BoxVisible[2] << std::endl;
        oss << "BoxNonVisible=" << PlayersColors.BoxNonVisible[0] << "," << PlayersColors.BoxNonVisible[1] << "," << PlayersColors.BoxNonVisible[2] << std::endl;

        oss << "KnockedBoxVisible=" << PlayersColors.KnockedBoxVisible[0] << "," << PlayersColors.KnockedBoxVisible[1] << "," << PlayersColors.KnockedBoxVisible[2] << std::endl;
        oss << "KnockedBoxNonVisible=" << PlayersColors.KnockedBoxNonVisible[0] << "," << PlayersColors.KnockedBoxNonVisible[1] << "," << PlayersColors.KnockedBoxNonVisible[2] << std::endl;

        oss << "BoxFillVisible=" << PlayersColors.BoxFillVisible[0] << "," << PlayersColors.BoxFillVisible[1] << "," << PlayersColors.BoxFillVisible[2] << "," << PlayersColors.BoxFillVisible[3] << std::endl;
        oss << "BoxFillNonVisible=" << PlayersColors.BoxFillNonVisible[0] << "," << PlayersColors.BoxFillNonVisible[1] << "," << PlayersColors.BoxFillNonVisible[2] << "," << PlayersColors.BoxFillNonVisible[3] << std::endl;

        oss << "KnockedBoxFillVisible=" << PlayersColors.KnockedBoxFillVisible[0] << "," << PlayersColors.KnockedBoxFillVisible[1] << "," << PlayersColors.KnockedBoxFillVisible[2] << "," << PlayersColors.KnockedBoxFillVisible[3] << std::endl;
        oss << "KnockedBoxFillNonVisible=" << PlayersColors.KnockedBoxFillNonVisible[0] << "," << PlayersColors.KnockedBoxFillNonVisible[1] << "," << PlayersColors.KnockedBoxFillNonVisible[2] << "," << PlayersColors.KnockedBoxFillNonVisible[3] << std::endl;

        oss << "SkeletonVisible=" << PlayersColors.SkeletonVisible[0] << "," << PlayersColors.SkeletonVisible[1] << "," << PlayersColors.SkeletonVisible[2] << std::endl;
        oss << "SkeletonNonVisible=" << PlayersColors.SkeletonNonVisible[0] << "," << PlayersColors.SkeletonNonVisible[1] << "," << PlayersColors.SkeletonNonVisible[2] << std::endl;

        oss << "KnockedSkeletonVisible=" << PlayersColors.KnockedSkeletonVisible[0] << "," << PlayersColors.KnockedSkeletonVisible[1] << "," << PlayersColors.KnockedSkeletonVisible[2] << std::endl;
        oss << "KnockedSkeletonNonVisible=" << PlayersColors.KnockedSkeletonNonVisible[0] << "," << PlayersColors.KnockedSkeletonNonVisible[1] << "," << PlayersColors.KnockedSkeletonNonVisible[2] << std::endl;

        oss << "HeadVisible=" << PlayersColors.HeadVisible[0] << "," << PlayersColors.HeadVisible[1] << "," << PlayersColors.HeadVisible[2] << std::endl;
        oss << "HeadNonVisible=" << PlayersColors.HeadNonVisible[0] << "," << PlayersColors.HeadNonVisible[1] << "," << PlayersColors.HeadNonVisible[2] << std::endl;

        oss << "KnockedHeadVisible=" << PlayersColors.KnockedHeadVisible[0] << "," << PlayersColors.KnockedHeadVisible[1] << "," << PlayersColors.KnockedHeadVisible[2] << std::endl;
        oss << "KnockedHeadNonVisible=" << PlayersColors.KnockedHeadNonVisible[0] << "," << PlayersColors.KnockedHeadNonVisible[1] << "," << PlayersColors.KnockedHeadNonVisible[2] << std::endl;

        oss << "LineVisible=" << PlayersColors.LineVisible[0] << "," << PlayersColors.LineVisible[1] << "," << PlayersColors.LineVisible[2] << std::endl;
        oss << "LineNonVisible=" << PlayersColors.LineNonVisible[0] << "," << PlayersColors.LineNonVisible[1] << "," << PlayersColors.LineNonVisible[2] << std::endl;

        oss << "KnockedLineVisible=" << PlayersColors.KnockedLineVisible[0] << "," << PlayersColors.KnockedLineVisible[1] << "," << PlayersColors.KnockedLineVisible[2] << std::endl;
        oss << "KnockedLineNonVisible=" << PlayersColors.KnockedLineNonVisible[0] << "," << PlayersColors.KnockedLineNonVisible[1] << "," << PlayersColors.KnockedLineNonVisible[2] << std::endl;

        oss << "RadarVisible=" << PlayersColors.RadarVisible[0] << "," << PlayersColors.RadarVisible[1] << "," << PlayersColors.RadarVisible[2] << std::endl;
        oss << "RadarNonVisible=" << PlayersColors.RadarNonVisible[0] << "," << PlayersColors.RadarNonVisible[1] << "," << PlayersColors.RadarNonVisible[2] << std::endl;

        oss << "KnockedRadarVisible=" << PlayersColors.KnockedRadarVisible[0] << "," << PlayersColors.KnockedRadarVisible[1] << "," << PlayersColors.KnockedRadarVisible[2] << std::endl;
        oss << "KnockedRadarNonVisible=" << PlayersColors.KnockedRadarNonVisible[0] << "," << PlayersColors.KnockedRadarNonVisible[1] << "," << PlayersColors.KnockedRadarNonVisible[2] << std::endl;

        oss << "Distance=" << PlayersColors.Distance[0] << "," << PlayersColors.Distance[1] << "," << PlayersColors.Distance[2] << std::endl;
        oss << "NickName=" << PlayersColors.NickName[0] << "," << PlayersColors.NickName[1] << "," << PlayersColors.NickName[2] << std::endl;


        oss << "[Radar]" << std::endl;
        oss << "Enable=" << (Radar.Enable ? "1" : "0") << std::endl;
        oss << "Distance=" << (Radar.Distance ? "1" : "0") << std::endl;
        oss << "PositionX=" << Radar.PositionX << std::endl;
        oss << "PositionY=" << Radar.PositionY << std::endl;
        oss << "CirleSize=" << Radar.CirleSize << std::endl;
        oss << "RectangleSize=" << Radar.RectangleSize << std::endl;
        oss << "VisibleColor=" << (Radar.VisibleColor ? "1" : "0") << std::endl;
        oss << "ClosestColor=" << (Radar.ClosestColor ? "1" : "0") << std::endl;
        oss << "MaxDistance=" << Radar.MaxDistance << std::endl;
        oss << "DistanceFontSize=" << Radar.DistanceFontSize << std::endl;
        oss << "RadarType=" << Radar.RadarType << std::endl;

        oss << "[ConfigsMenu]" << std::endl;
        oss << "iConfig=" << ConfigsMenu.iConfig << std::endl;
        oss << "oldiConfig=" << ConfigsMenu.oldiConfig << std::endl;
        oss << "selectedWeapon=" << ConfigsMenu.selectedWeapon << std::endl;
        oss << "Language=" << ConfigsMenu.Language << std::endl;
        oss << "FeaturesDefinition=" << (ConfigsMenu.FeaturesDefinition ? "1" : "0") << std::endl;

        oss << "[Notifications]" << std::endl;
        oss << "Enable=" << (g_NotificationSettings.Enable ? "1" : "0") << std::endl;
        oss << "DisplayDuration=" << g_NotificationSettings.DisplayDuration << std::endl;

        std::string output = oss.str();
        outFile.write(output.c_str(), output.size());
        outFile.close();
        // HotkeyManager::Initialize(); // Removed from SaveConfiguration as well, if it was here.
                                     // It's more robust to have it after LoadConfiguration.
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error saving configuration: " << e.what() << std::endl;
        return false;
    }
}

// Forward declaration of LoadHotkeySettings
void LoadHotkeySettings(const std::string& key, const std::string& value);

// Load settings from a configuration file
bool LoadConfiguration(const std::string& filename) {
    try {
        std::string filepath = g_ConfigSystem.configFolder + filename;

        // Check if file exists
        if (!std::filesystem::exists(filepath)) {
            return false;
        }

        std::ifstream inFile(filepath);
        if (!inFile.is_open()) {
            return false;
        }

        std::string section;
        std::string line;

        while (std::getline(inFile, line)) {
            // Remove any trailing newline or carriage return characters
            line.erase(std::remove(line.begin(), line.end(), '\n'), line.end());
            line.erase(std::remove(line.begin(), line.end(), '\r'), line.end());

            if (line.empty() || line[0] == ';') continue; // Skip empty or comment lines

            if (line[0] == '[' && line.back() == ']') {
                section = line; // Save the current section
            }
            else {
                std::string key, value;
                size_t equalPos = line.find('=');
                if (equalPos != std::string::npos) {
                    key = line.substr(0, equalPos);
                    value = line.substr(equalPos + 1);

                    // Use "[Keys]" and ParseKeyssSettings as indicated by logs
                    if (section == "[Keys]") { 
                        ParseKeyssSettings(key, value);
                    } else {
                        ParseSettings(section, key, value); // Call existing parser
                    }
                }
            }
        }

        inFile.close();

        // Sync settings immediately
        SettingsHelper::SyncSettings();
        
        // Re-initialize HotkeyManager with loaded settings
        HotkeySystem::Initialize();

        // Update last selection time
        for (auto& config : g_ConfigSystem.configs) {
            if (config.filename == filename) {
                config.lastModified = std::chrono::system_clock::now();
                break;
            }
        }

        // Add notification that config was loaded
        g_NotificationManager.AddChange("Configurations", "Loaded Config", false, true);

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading configuration: " << e.what() << std::endl;
        return false;
    }
}

// Delete a configuration file
bool DeleteConfiguration(const std::string& filename) {
    try {
        // Prevent deletion of default config
        if (filename == "Default.cfg") {
            return false;
        }

        std::string filepath = g_ConfigSystem.configFolder + filename;

        // Check if file exists
        if (!std::filesystem::exists(filepath)) {
            return false;
        }

        // Delete the file
        std::filesystem::remove(filepath);

        // Refresh configurations
        RefreshConfigurations();

        // If the deleted config was selected, select the default
        bool needsNewSelection = true;
        for (int i = 0; i < g_ConfigSystem.configs.size(); i++) {
            if (g_ConfigSystem.configs[i].isSelected) {
                needsNewSelection = false;
                break;
            }
        }

        if (needsNewSelection) {
            SelectDefaultConfiguration();
        }

        return true;
    }
    catch (const std::exception& e) {
        // Handle exception
        return false;
    }
}

// Create a new configuration with the current settings
bool CreateConfiguration(const std::string& name, const std::string& type) {
    try {
        // Sanitize filename to be safe for filesystem
        std::string safeName = name;
        std::replace_if(safeName.begin(), safeName.end(),
            [](char c) { return c == '<' || c == '>' || c == ':' || c == '"' || c == '/' || c == '\\' || c == '|' || c == '?' || c == '*'; },
            '_');

        std::string filename = safeName + ".cfg";

        // Save the new configuration
        if (!SaveConfiguration(filename)) { // SaveConfiguration will call SaveAimSettingsToFile internally if default
            return false;
        }

        // Create config entry
        ConfigFile newConfig;
        newConfig.name = safeName;
        newConfig.type = type;
        newConfig.filename = filename;
        newConfig.isSelected = true;
        newConfig.isDefault = false;
        newConfig.isCloudConfig = false;
        newConfig.lastModified = std::chrono::system_clock::now();

        // Deselect other configs
        for (auto& config : g_ConfigSystem.configs) {
            config.isSelected = false;
        }

        // Add to list and select it
        g_ConfigSystem.configs.push_back(newConfig);
        g_ConfigSystem.selectedConfigIndex = g_ConfigSystem.configs.size() - 1;

        // Sort configurations
        std::sort(g_ConfigSystem.configs.begin(), g_ConfigSystem.configs.end(),
            [](const ConfigFile& a, const ConfigFile& b) {
                if (a.isDefault) return true;
                if (b.isDefault) return false;
                return a.lastModified > b.lastModified;
            });

        // Update selected index after sorting
        for (int i = 0; i < g_ConfigSystem.configs.size(); i++) {
            if (g_ConfigSystem.configs[i].filename == filename) {
                g_ConfigSystem.selectedConfigIndex = i;
                break;
            }
        }

        return true;
    }
    catch (const std::exception& e) {
        // Handle exception
        return false;
    }
}

// Share a configuration to the cloud
bool ShareConfiguration(const std::string& name, const std::string& type) {
    // In a real implementation, this would upload the current config to a cloud service
    // For now, just create a local copy with "Shared_" prefix
    try {
        std::string sharedName = "Shared_" + name;
        return CreateConfiguration(sharedName, type);
    }
    catch (const std::exception& e) {
        // Handle exception
        return false;
    }
}

// Select a configuration by index
void SelectConfiguration(int index) {
    // Validate index
    if (index < 0 || index >= g_ConfigSystem.configs.size()) {
        return;
    }

    // Deselect all configs
    for (auto& config : g_ConfigSystem.configs) {
        config.isSelected = false;
    }

    // Select the specified config
    g_ConfigSystem.configs[index].isSelected = true;
    g_ConfigSystem.selectedConfigIndex = index;

    // Load the configuration
    LoadConfiguration(g_ConfigSystem.configs[index].filename);
}

// Select the default configuration
void SelectDefaultConfiguration() {
    for (int i = 0; i < g_ConfigSystem.configs.size(); i++) {
        if (g_ConfigSystem.configs[i].isDefault) {
            SelectConfiguration(i);
            return;
        }
    }

    // If no default config exists, select the first one
    if (!g_ConfigSystem.configs.empty()) {
        SelectConfiguration(0);
    }
}

// Save hotkey settings
void SaveHotkeySettings(std::ostringstream& file) {
    file << "[Hotkeys]" << std::endl;

    // Save Aimbot hotkeys
    file << "AimbotEnable=" << Keys.AimbotEnable << std::endl;
    file << "AimbotEnableMode=" << (Keys.AimbotEnableMode ? "1" : "0") << std::endl;
    file << "AimbotAimLock=" << Keys.AimbotAimLock << std::endl;
    file << "AimbotAimLockMode=" << (Keys.AimbotAimLockMode ? "1" : "0") << std::endl;
    file << "AimbotPrediction=" << Keys.AimbotPrediction << std::endl;
    file << "AimbotPredictionMode=" << (Keys.AimbotPredictionMode ? "1" : "0") << std::endl;
    file << "AimbotSaveTarget=" << Keys.AimbotSaveTarget << std::endl;
    file << "AimbotSaveTargetMode=" << (Keys.AimbotSaveTargetMode ? "1" : "0") << std::endl;
    file << "AimbotVisibilityCheck=" << Keys.AimbotVisibilityCheck << std::endl;
    file << "AimbotVisibilityCheckMode=" << (Keys.AimbotVisibilityCheckMode ? "1" : "0") << std::endl;
    file << "AimbotHumanizedSmooth=" << Keys.AimbotHumanizedSmooth << std::endl;
    file << "AimbotHumanizedSmoothMode=" << (Keys.AimbotHumanizedSmoothMode ? "1" : "0") << std::endl;
    file << "AimbotIgnoreDowned=" << Keys.AimbotIgnoreDowned << std::endl;
    file << "AimbotIgnoreDownedMode=" << (Keys.AimbotIgnoreDownedMode ? "1" : "0") << std::endl;
    file << "AimbotPlayerAi=" << Keys.AimbotPlayerAi << std::endl;
    file << "AimbotPlayerAiMode=" << (Keys.AimbotPlayerAiMode ? "1" : "0") << std::endl;
    file << "AimbotWeaponOnly=" << Keys.AimbotWeaponOnly << std::endl;
    file << "AimbotWeaponOnlyMode=" << (Keys.AimbotWeaponOnlyMode ? "1" : "0") << std::endl;
    file << "AimbotDrawFov=" << Keys.AimbotDrawFov << std::endl;
    file << "AimbotDrawFovMode=" << (Keys.AimbotDrawFovMode ? "1" : "0") << std::endl;
    file << "AimbotDrawCrossHair=" << Keys.AimbotDrawCrossHair << std::endl;
    file << "AimbotDrawCrossHairMode=" << (Keys.AimbotDrawCrossHairMode ? "1" : "0") << std::endl;
    file << "AimbotDrawTarget=" << Keys.AimbotDrawTarget << std::endl;
    file << "AimbotDrawTargetMode=" << (Keys.AimbotDrawTargetMode ? "1" : "0") << std::endl;

    // Save Player ESP hotkeys
    file << "PlayerEspEnable=" << Keys.PlayerEspEnable << std::endl;
    file << "PlayerEspEnableMode=" << (Keys.PlayerEspEnableMode ? "1" : "0") << std::endl;
    file << "PlayerEspBox=" << Keys.PlayerEspBox << std::endl;
    file << "PlayerEspBoxMode=" << (Keys.PlayerEspBoxMode ? "1" : "0") << std::endl;
    file << "PlayerEspSkeleton=" << Keys.PlayerEspSkeleton << std::endl;
    file << "PlayerEspSkeletonMode=" << (Keys.PlayerEspSkeletonMode ? "1" : "0") << std::endl;
    file << "PlayerEspHeadCircle=" << Keys.PlayerEspHeadCircle << std::endl;
    file << "PlayerEspHeadCircleMode=" << (Keys.PlayerEspHeadCircleMode ? "1" : "0") << std::endl;
    file << "PlayerEspLines=" << Keys.PlayerEspLines << std::endl;
    file << "PlayerEspLinesMode=" << (Keys.PlayerEspLinesMode ? "1" : "0") << std::endl;
    file << "PlayerEspDistance=" << Keys.PlayerEspDistance << std::endl;
    file << "PlayerEspDistanceMode=" << (Keys.PlayerEspDistanceMode ? "1" : "0") << std::endl;
    file << "PlayerEspNickName=" << Keys.PlayerEspNickName << std::endl;
    file << "PlayerEspNickNameMode=" << (Keys.PlayerEspNickNameMode ? "1" : "0") << std::endl;

    // Save Item ESP hotkeys
    file << "ItemEspEnable=" << Keys.ItemEspEnable << std::endl;
    file << "ItemEspEnableMode=" << (Keys.ItemEspEnableMode ? "1" : "0") << std::endl;
    file << "ItemConsumableEnable=" << Keys.ItemConsumableEnable << std::endl;
    file << "ItemConsumableEnableMode=" << (Keys.ItemConsumableEnableMode ? "1" : "0") << std::endl;
    file << "ItemWeaponEnable=" << Keys.ItemWeaponEnable << std::endl;
    file << "ItemWeaponEnableMode=" << (Keys.ItemWeaponEnableMode ? "1" : "0") << std::endl;
    file << "ItemAmmoEnable=" << Keys.ItemAmmoEnable << std::endl;
    file << "ItemAmmoEnableMode=" << (Keys.ItemAmmoEnableMode ? "1" : "0") << std::endl;
    file << "ItemOtherEnable=" << Keys.ItemOtherEnable << std::endl;
    file << "ItemOtherEnableMode=" << (Keys.ItemOtherEnableMode ? "1" : "0") << std::endl;

    // Save Radar hotkeys
    file << "RadarEnable=" << Keys.RadarEnable << std::endl;
    file << "RadarEnableMode=" << (Keys.RadarEnableMode ? "1" : "0") << std::endl;
    file << "RadarDistance=" << Keys.RadarDistance << std::endl;
    file << "RadarDistanceMode=" << (Keys.RadarDistanceMode ? "1" : "0") << std::endl;
    file << "RadarVisibleColor=" << Keys.RadarVisibleColor << std::endl;
    file << "RadarVisibleColorMode=" << (Keys.RadarVisibleColorMode ? "1" : "0") << std::endl;
    file << "RadarClosestColor=" << Keys.RadarClosestColor << std::endl;
    file << "RadarClosestColorMode=" << (Keys.RadarClosestColorMode ? "1" : "0") << std::endl;
    file << "RadarAimingAtMeColor=" << Keys.RadarAimingAtMeColor << std::endl;
    file << "RadarAimingAtMeColorMode=" << (Keys.RadarAimingAtMeColorMode ? "1" : "0") << std::endl;
}

// Load hotkey settings
void LoadHotkeySettings(const std::string& key, const std::string& value) {
    try {
        // Check if this is a mode setting
        if (key.length() > 4 && key.substr(key.length() - 4) == "Mode") {
            // This is a mode setting
            std::string featureName = key.substr(0, key.length() - 4);

            // Update the Keys struct directly
            if (featureName == "AimbotEnable") {
                Keys.AimbotEnableMode = (value == "1");
            } else if (featureName == "AimbotAimLock") {
                Keys.AimbotAimLockMode = (value == "1");
            } else if (featureName == "AimbotPrediction") {
                Keys.AimbotPredictionMode = (value == "1");
            } else if (featureName == "AimbotSaveTarget") {
                Keys.AimbotSaveTargetMode = (value == "1");
            } else if (featureName == "AimbotVisibilityCheck") {
                Keys.AimbotVisibilityCheckMode = (value == "1");
            } else if (featureName == "AimbotHumanizedSmooth") {
                Keys.AimbotHumanizedSmoothMode = (value == "1");
            } else if (featureName == "AimbotIgnoreDowned") {
                Keys.AimbotIgnoreDownedMode = (value == "1");
            } else if (featureName == "AimbotPlayerAi") {
                Keys.AimbotPlayerAiMode = (value == "1");
            } else if (featureName == "AimbotWeaponOnly") {
                Keys.AimbotWeaponOnlyMode = (value == "1");
            } else if (featureName == "AimbotDrawFov") {
                Keys.AimbotDrawFovMode = (value == "1");
            } else if (featureName == "AimbotDrawCrossHair") {
                Keys.AimbotDrawCrossHairMode = (value == "1");
            } else if (featureName == "AimbotDrawTarget") {
                Keys.AimbotDrawTargetMode = (value == "1");
            } else if (featureName == "PlayerEspEnable") {
                Keys.PlayerEspEnableMode = (value == "1");
            } else if (featureName == "PlayerEspBox") {
                Keys.PlayerEspBoxMode = (value == "1");
            } else if (featureName == "PlayerEspSkeleton") {
                Keys.PlayerEspSkeletonMode = (value == "1");
            } else if (featureName == "PlayerEspHeadCircle") {
                Keys.PlayerEspHeadCircleMode = (value == "1");
            } else if (featureName == "PlayerEspLines") {
                Keys.PlayerEspLinesMode = (value == "1");
            } else if (featureName == "PlayerEspDistance") {
                Keys.PlayerEspDistanceMode = (value == "1");
            } else if (featureName == "PlayerEspNickName") {
                Keys.PlayerEspNickNameMode = (value == "1");
            } else if (featureName == "ItemEspEnable") {
                Keys.ItemEspEnableMode = (value == "1");
            } else if (featureName == "ItemConsumableEnable") {
                Keys.ItemConsumableEnableMode = (value == "1");
            } else if (featureName == "ItemWeaponEnable") {
                Keys.ItemWeaponEnableMode = (value == "1");
            } else if (featureName == "ItemAmmoEnable") {
                Keys.ItemAmmoEnableMode = (value == "1");
            } else if (featureName == "ItemOtherEnable") {
                Keys.ItemOtherEnableMode = (value == "1");
            } else if (featureName == "RadarEnable") {
                Keys.RadarEnableMode = (value == "1");
            } else if (featureName == "RadarDistance") {
                Keys.RadarDistanceMode = (value == "1");
            } else if (featureName == "RadarVisibleColor") {
                Keys.RadarVisibleColorMode = (value == "1");
            } else if (featureName == "RadarClosestColor") {
                Keys.RadarClosestColorMode = (value == "1");
            } else if (featureName == "RadarAimingAtMeColor") {
                Keys.RadarAimingAtMeColorMode = (value == "1");
            }
        } else {
            // This is a key code setting
            if (key == "AimbotEnable") {
                Keys.AimbotEnable = std::stoi(value);
            } else if (key == "AimbotAimLock") {
                Keys.AimbotAimLock = std::stoi(value);
            } else if (key == "AimbotPrediction") {
                Keys.AimbotPrediction = std::stoi(value);
            } else if (key == "AimbotSaveTarget") {
                Keys.AimbotSaveTarget = std::stoi(value);
            } else if (key == "AimbotVisibilityCheck") {
                Keys.AimbotVisibilityCheck = std::stoi(value);
            } else if (key == "AimbotHumanizedSmooth") {
                Keys.AimbotHumanizedSmooth = std::stoi(value);
            } else if (key == "AimbotIgnoreDowned") {
                Keys.AimbotIgnoreDowned = std::stoi(value);
            } else if (key == "AimbotPlayerAi") {
                Keys.AimbotPlayerAi = std::stoi(value);
            } else if (key == "AimbotWeaponOnly") {
                Keys.AimbotWeaponOnly = std::stoi(value);
            } else if (key == "AimbotDrawFov") {
                Keys.AimbotDrawFov = std::stoi(value);
            } else if (key == "AimbotDrawCrossHair") {
                Keys.AimbotDrawCrossHair = std::stoi(value);
            } else if (key == "AimbotDrawTarget") {
                Keys.AimbotDrawTarget = std::stoi(value);
            } else if (key == "PlayerEspEnable") {
                Keys.PlayerEspEnable = std::stoi(value);
            } else if (key == "PlayerEspBox") {
                Keys.PlayerEspBox = std::stoi(value);
            } else if (key == "PlayerEspSkeleton") {
                Keys.PlayerEspSkeleton = std::stoi(value);
            } else if (key == "PlayerEspHeadCircle") {
                Keys.PlayerEspHeadCircle = std::stoi(value);
            } else if (key == "PlayerEspLines") {
                Keys.PlayerEspLines = std::stoi(value);
            } else if (key == "PlayerEspDistance") {
                Keys.PlayerEspDistance = std::stoi(value);
            } else if (key == "PlayerEspNickName") {
                Keys.PlayerEspNickName = std::stoi(value);
            } else if (key == "ItemEspEnable") {
                Keys.ItemEspEnable = std::stoi(value);
            } else if (key == "ItemConsumableEnable") {
                Keys.ItemConsumableEnable = std::stoi(value);
            } else if (key == "ItemWeaponEnable") {
                Keys.ItemWeaponEnable = std::stoi(value);
            } else if (key == "ItemAmmoEnable") {
                Keys.ItemAmmoEnable = std::stoi(value);
            } else if (key == "ItemOtherEnable") {
                Keys.ItemOtherEnable = std::stoi(value);
            } else if (key == "RadarEnable") {
                Keys.RadarEnable = std::stoi(value);
            } else if (key == "RadarDistance") {
                Keys.RadarDistance = std::stoi(value);
            } else if (key == "RadarVisibleColor") {
                Keys.RadarVisibleColor = std::stoi(value);
            } else if (key == "RadarClosestColor") {
                Keys.RadarClosestColor = std::stoi(value);
            } else if (key == "RadarAimingAtMeColor") {
                Keys.RadarAimingAtMeColor = std::stoi(value);
            }
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading hotkey setting: " << key << " = " << value << " - " << e.what() << std::endl;
    }
}
