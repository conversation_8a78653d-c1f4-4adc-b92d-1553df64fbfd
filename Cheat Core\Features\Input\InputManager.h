#pragma once
#include <Windows.h>
#include <map>
#include <string>
#include <vector>
#include <functional>
#include <iostream>

// Key state structure to track the state of each key
struct KeyState {
    bool isDown = false;         // Is the key currently pressed
    bool wasDown = false;        // Was the key pressed in the previous frame
    bool justPressed = false;    // Was the key just pressed this frame
    bool justReleased = false;   // Was the key just released this frame

    // Reset the state for a new frame
    void Update(bool currentlyDown) {
        // First store the current state as the previous state
        wasDown = isDown;

        // Then update the current state
        isDown = currentlyDown;

        // Calculate justPressed and justReleased based on the state change
        if (currentlyDown && !wasDown) {
            // Key was just pressed this frame
            justPressed = true;
        } else if (!currentlyDown && wasDown) {
            // Key was just released this frame
            justReleased = true;
            // When a key is released, we should also clear the justPressed flag
            justPressed = false;
        } else {
            // Key state didn't change this frame, clear the flags
            // Only clear justPressed after one frame to ensure it's detected
            if (justPressed) {
                // Keep justPressed true for one more frame to ensure detection
                justPressed = false;
            }

            // Only clear justReleased after one frame to ensure it's detected
            if (justReleased) {
                // Keep justReleased true for one more frame to ensure detection
                justReleased = false;
            }
        }
    }
};

// Input manager class to handle all input-related functionality
class InputManager {
private:
    // Map of key codes to their states
    static std::map<int, KeyState> keyStates;

    // Debug mode flag
    static bool debugMode;

    // Flag to indicate if the input manager is initialized
    static bool initialized;

    // List of keys to monitor
    static std::vector<int> monitoredKeys;

public:
    // Initialize the input manager
    static void Initialize(bool debug = false);

    // Update the state of all monitored keys
    static void Update();

    // Add a key to the monitored keys list
    static void MonitorKey(int keyCode);

    // Remove a key from the monitored keys list
    static void UnmonitorKey(int keyCode);

    // Check if a key is currently down
    static bool IsKeyDown(int keyCode);

    // Check if a key was just pressed this frame
    static bool IsKeyJustPressed(int keyCode);

    // Check if a key was just released this frame
    static bool IsKeyJustReleased(int keyCode);

    // Get the name of a key from its virtual key code
    static std::string GetKeyName(int keyCode);

    // Convert a virtual key code to a string
    static std::string VirtualKeyToString(int keyCode);

    // Check if a key is valid (can be used as a hotkey)
    static bool IsValidKey(int keyCode);

    // Get the current state of a key
    static KeyState GetKeyState(int keyCode);

    // Print debug information about a key
    static void DebugKey(int keyCode);

    // Print debug information about all monitored keys
    static void DebugAllKeys();

    // Get the list of monitored keys
    static const std::vector<int>& GetMonitoredKeys() {
        return monitoredKeys;
    }
};
