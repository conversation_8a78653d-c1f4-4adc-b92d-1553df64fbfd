#pragma once
#include <Windows.h>
#include <map>
#include <string>
#include <vector>

// Structure for defining hotkeys
struct HotkeyDefinition {
    std::string name;
    std::string category;
    bool* statePtr;
    int* keyCodePtr;
    bool* toggleModePtr;
};
#include <functional>
#include <iostream>
#include "InputManager.h"
#include "../../GameClass/GameSettings.h"

// Structure to represent a feature that can be toggled with a hotkey
struct HotkeyBinding {
    std::string name;           // Name of the feature
    std::string category;       // Category of the feature
    bool* statePtr;             // Pointer to the feature's state
    int keyCode;                // Virtual key code for the hotkey
    bool toggleMode;            // True for toggle mode, false for hold mode

    HotkeyBinding() : name(""), category(""), statePtr(nullptr), keyCode(0), toggleMode(true) {}

    HotkeyBinding(const std::string& n, const std::string& c, bool* s, int k, bool t)
        : name(n), category(c), statePtr(s), keyCode(k), toggleMode(t) {}
};

// Hotkey system class to manage all hotkey bindings
class HotkeySystem {
private:
    // Map of feature names to their hotkey bindings
    static std::map<std::string, HotkeyBinding> bindings;

    // Debug mode flag (kept for compatibility)
    static bool debugMode;

    // Flag to indicate if the hotkey system is initialized
    static bool initialized;

    // Flag to indicate if the hotkey system is enabled
    static bool enabled;

    // Flag to indicate if the menu is open (to disable hotkeys)
    static bool menuOpen;

public:
    // Initialize the hotkey system
    static void Initialize(bool debug = false);

    // Initialize the hotkey system with force reload option
    static void Initialize(bool debug, bool forceReload);

    // Register a feature with the hotkey system
    static void RegisterHotkey(const std::string& name, const std::string& category, bool* state, int keyCode, bool toggleMode);

    // Register all hotkeys from the Keys struct
    static void RegisterAllHotkeys();

    // Update the hotkey system (call every frame)
    static void Update();

    // Process all registered hotkeys
    static void ProcessHotkeys();

    // Set the menu open state
    static void SetMenuOpen(bool open);

    // Enable or disable the hotkey system
    static void SetEnabled(bool enable);

    // Check if the hotkey system is enabled
    static bool IsEnabled() { return enabled; }

    // Get a specific hotkey binding by name
    static HotkeyBinding* GetHotkey(const std::string& name);

    // Get all registered hotkey bindings
    static const std::map<std::string, HotkeyBinding>& GetAllHotkeys();

    // Update a hotkey binding
    static void UpdateHotkey(const std::string& name, int keyCode, bool toggleMode);

    // Reset a hotkey binding
    static void ResetHotkey(const std::string& name);

    // Reset all hotkey bindings
    static void ResetAllHotkeys();

    // Helper function to get the mode from the Keys struct
    static bool GetModeFromKeysStruct(const std::string& name);

    // Helper function to get the mode from the Settings struct
    static bool GetModeFromSettingsStruct(const std::string& name);

    // Sync hotkey bindings with the Keys struct
    static void SyncWithKeysStruct();

    // Verify mode consistency across all data structures
    static void VerifyModeConsistency();

    // Helper function to update a specific mode field in the Keys struct
    static void UpdateKeysModeField(const std::string& name, bool toggleMode);

    // These functions have been removed as they were only used for debugging

    // Helper function to update feature state in the global state
    static void UpdateFeatureState(const std::string& name, bool newState);

    // Helper function to save settings
    static void SaveSettings();
};
