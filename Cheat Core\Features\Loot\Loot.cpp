#include "Loot.h"
#include <unordered_set>
#include <map>
#include "../../GameClass/GameFunctions.h"


ImColor GetPickupColor(EFortRarity Rarity) {
	static const std::unordered_map<EFortRarity, ImColor> rarityColorMap = {
		{EFortRarity::Common, ImColor(181, 181, 181)},
		{EFortRarity::Uncommon, ImColor(40.0f / 255.0f, 180.0f / 255.0f, 5.0f / 255.0f, 1.0f)},
		{EFortRarity::Rare, ImColor(0.0f / 255.0f, 200.0f / 255.0f, 240.0f / 250.0f, 1.0f)},
		{EFortRarity::Epic, ImColor(200.0f / 255.0f, 100.0f / 255.0f, 1.0f, 1.0f)},
		{EFortRarity::Legendary, ImColor(250.0f / 255.0f, 150.0f / 255.0f, 40.0f / 255.0f, 1.0f)},
		{EFortRarity::Mythic, ImColor(230.0f / 255.0f, 200.0f / 255.0f, 95.0f / 255.0f, 1.0f)}
		// Uncomment if needed: {EFortRarity::Transcendent, ImColor(0.0f / 255.0f, 245.0f / 255.0f, 205.0f / 255.0f, 1.0f)}
	};

	auto it = rarityColorMap.find(Rarity);
	if (it != rarityColorMap.end()) {
		return it->second;
	}

	return ImColor(0, 0, 0);  // Default color if rarity not found
}


bool IsConsumableItem(const std::string& itemName) {
	static const std::unordered_set<std::string> consumableItems = {
	   "Bandage", "Shield Potion", "Med Kit", "FlowBerry Fizz",
	   "Chug Splash", "Nitro Splash", "Nuka Cola", "Small Shield Potion"
	};
	return consumableItems.find(itemName) != consumableItems.end();
}

bool IsWeaponItem(const std::string& itemName) {
	static const std::unordered_set<std::string> weaponItems = {
		"Harbinger", "Thunder Burst SMG", "Combat Assault Rifle", "Enforcer AR",
		"Tactical Assault Rifle", "Warforged Assault Rifle", "Hammer Pump Shotgun",
		"Gatekeeper Shotgun", "Combat Shotgun", "Frenzy Auto Shotgun", "Ranger Pistol",
		"DMR", "Boom Bolt", "Hand Cannon", "Nitro Fists", "Shockwave Grenade",
		"Heavy Impact Sniper Rifle", "MK-Seven Assault Rifle", "Pump Shotgun",
		"OG Pump Shotgun", "Tactical Shotgun", "Lever Action Shotgun", "Heavy Shotgun",
		"Ranger Shotgun", "Scar Assault Rifle","OG Assault Rifle", "Hammer Assault Rifle",
		"Heavy Assault Rifle", "Infantry Rifle", "Submachine Gun", "Tactical Submachine Gun",
		"Stinger SMG", "Bolt-Action Sniper Rifle", "Hunting Rifle", "Pistol",
		"Revolver", "Rocket Launcher", "Grappler", "Crash Pad",
		"Dual Pistols", "Rapid Fire SMG", "Suppressed SMG", "Suppressed Assault Rifle",
		"Heavy Sniper Rifle", "Semi-Automatic Sniper Rifle", "Grenade Launcher",
		"Remote Explosives", "Regular Grenades", "Stink Bomb", "Bandage Bazooka",
		"Boogie Bomb", "Oni Shotgun", "Twinfire Auto Shotgun", "Sentinel Pump Shotgun",
		"Surgefire SMG", "Veiled Precision SMG", "Fury Assault Rifle",
		"Holo Twister Assault Rifle", "Clinger"
	};
	return weaponItems.find(itemName) != weaponItems.end();
}


// Define a map for exact item renaming rules based on item name and rarity
std::unordered_map<std::string, std::unordered_map<EFortRarity, std::string>> exactItemRenamingMap = {
	{"Pump Shotgun", {
		{EFortRarity::Epic, "Pump Shotgun"},
		{EFortRarity::Legendary, "Pump Shotgun"},
		{EFortRarity::Common, "OG Pump Shotgun"},
		{EFortRarity::Uncommon, "OG Pump Shotgun"},
		{EFortRarity::Rare, "OG Pump Shotgun"}
	}},
	{"Assault Rifle", {
		{EFortRarity::Epic, "Scar Assault Rifle"},
		{EFortRarity::Legendary, "Scar Assault Rifle"},
		{EFortRarity::Common, "OG Assault Rifle"},
		{EFortRarity::Uncommon, "OG Assault Rifle"},
		{EFortRarity::Rare, "OG Assault Rifle"}
	}}
};

std::string GetExactRenamedItem(const std::string& itemName, EFortRarity rarity) {
	// Check for exact name and rarity match
	if (exactItemRenamingMap.count(itemName) > 0) {
		auto rarityMap = exactItemRenamingMap.at(itemName);
		if (rarityMap.count(rarity) > 0) {
			std::string renamedItem = rarityMap[rarity];
			return renamedItem;
		}
	}
	return itemName;  // Return original name if no renaming rule is found
}

ID3D11ShaderResourceView* GetTextureForItem(const std::string& itemName, EFortRarity itemRarity) {
	static const std::unordered_map<std::string, ID3D11ShaderResourceView*> textureMap = {
		{"Med Kit", medkitTexture},
		{"Bandage", BandagesTexture},
		{"FlowBerry Fizz", FlowBerry_FizzTexture},
		{"Chug Splash", ChugSplashTexture},
		{"Nitro Splash", Nitro_SplashTexture},
		{"Nuka", Nitro_SplashTexture},
		{"Small Shield Potion", Small_ShieldTexture},
		{"Small Shield", Small_ShieldTexture},
		{"Crash Pad", CrashPadTexture},
		{"Shield Potion", sobigshieldTexture},
		{"Big Shield", sobigshieldTexture},
		{"Crash", CrashPadTexture},
		{"Harbinger", Harbinger_SMGTexture},
		{"Thunder Burst SMG", Thunder_Burst_SMGTexture},
		{"Combat Assault Rifle", Combat_Assault_RifleTexture},
		{"Enforcer AR", Enforcer_ARTexture},
		{"Tactical Assault Rifle", Tactical_Assault_RifleTexture},
		{"Warforged Assault Rifle", Warforged_Assault_RifleTexture},
		{"Hammer Pump Shotgun", Hammer_Pump_ShotgunTexture},
		{"Gatekeeper Shotgun", Gatekeeper_ShotgunTexture},
		{"Combat Shotgun", Combat_ShotgunTexture},
		{"Frenzy Auto Shotgun", Frenzy_Auto_ShotgunTexture},
		{"Ranger Pistol", Ranger_PistolTexture},
		{"DMR", Huntress_DMRTexture},
		{"Boom Bolt", Boom_BoltTexture},
		{"Hand Cannon", Hand_CannonTexture},
		{"Nitro Fists", Nitro_FistsTexture},
		{"Shockwave Grenade", Shockwave_grenadeTexture},
		{"Shockwave", Shockwave_grenadeTexture},
		{"Heavy Impact Sniper Rifle", HeavyImpactSniperRifleTexture},
		{"Light Bullets", Light_BulletsTexture},
		{"Medium Bullets", Medium_BulletsTexture},
		{"Heavy Bullets", Heavy_BulletsTexture},
		{"Shells", ShellsTexture},
		{"Rockets", RocketsTexture},
		{"Chest", chestTexture},
		{"MK-Seven Assault Rifle", MKSevenAssaultRifleTexture},
		{"Chug Jug", ChugJugTexture},
		{"Drum Gun", MidasDrumGunTexture},
		{"Skye's AR", SkyesARTexture},
		{"Grappler", GrapplerTexture},
		{"Stinger SMG", StingerSMGTexture},
		{"Burst Assault Rifle", BurstARTexture},
		{"Heisted Breacher Shotgun", HeistedBreacherShotgunTexture},
		{"Heisted Accelerant Shotgun", HeistedAccelerantShotgunTexture},
		{"Heisted Explosive AR", HeistedExplosiveARTexture},
		{"Heisted Blink Mag SMG", HeistedBlinkMagSMGTexture},
		{"Heisted Run 'N' Gun SMG", HeistedRunGunSMGTexture},
		{"Tactical Shotgun", TacticalShotgunTexture},
		{"Lever Action Shotgun", LeverActionShotgunTexture},
		{"Heavy Shotgun", HeavyShotgunTexture},
		{"Ranger Shotgun", RangerShotgunTexture},
		{"Hammer Assault Rifle", HammerAssaultRifleTexture},
		{"Heavy Assault Rifle", HeavyAssaultRifleTexture},
		{"Infantry Rifle", InfantryRifleTexture},
		{"Submachine Gun", SubmachineGunTexture},
		{"Tactical Submachine Gun", TacticalSubmachineGunTexture},
		{"Bolt-Action Sniper Rifle", BoltActionSniperRifleTexture},
		{"Hunting Rifle", HuntingRifleTexture},
		{"Pistol", PistolTexture},
		{"Revolver", RevolverTexture},
		{"Rocket Launcher", RocketLauncherTexture},
		{"Monarch Pistol", Monarch_PistolTexture},
		{"Dual Micro SMGs", Dual_Micro_SMGsTexture},
		{"Striker Burst Rifle", Striker_Burst_RifleTexture},
		{"Hyper SMG", Hyper_SMGTexture},
		{"Striker AR", Striker_ARTexture},
		{"Sovereign Shotgun", Sovereign_ShotgunTexture},
		{"Firefly Jar", Firefly_JarTexture},
		{"Dual Pistols", DualPistolsTexture},
		{"Rapid Fire SMG", RapidFireSMGTexture},
		{"Suppressed SMG", SuppressedSMGTexture},
		{"Suppressed Assault Rifle", SuppressedAssaultRifleTexture},
		{"Heavy Sniper Rifle", HeavySniperRifleTexture},
		{"Semi-Automatic Sniper Rifle", SemiAutomaticSniperRifleTexture},
		{"Grenade Launcher", GrenadeLauncherTexture},
		{"Remote Explosives", RemoteExplosivesTexture},
		{"Regular Grenades", RegularGrenadesTexture},
		{"Stink Bomb", StinkBombTexture},
		{"Bandage Bazooka", BandageBazookaTexture},
		{"Boogie Bomb", BoogieBombTexture},

		{"Holo Twister Assault Rifle", HoloTwisterAssaultRifleTexture},
		{"Fury Assault Rifle", FuryAssaultRifleTexture},
		{"Veiled Precision SMG", VeiledPrecisionSMGTexture},
		{"Surgefire SMG", SurgefireSMGTexture},
		{"Sentinel Pump Shotgun", SentinelPumpTexture},
		{"Twinfire Auto Shotgun", TwinfireAutoShotgunTexture},
		{"Oni Shotgun", OniShotgunTexture},

		{"Clinger", ClingersTexture}
	};

	// Check rarity-based item overrides
	if ((itemName == "OG Pump Shotgun" && (itemRarity == EFortRarity::Common || itemRarity == EFortRarity::Uncommon || itemRarity == EFortRarity::Rare))) {
		return OGPump_ShotgunTexture;
	}
	else if (itemName == "Pump Shotgun" && (itemRarity == EFortRarity::Epic || itemRarity == EFortRarity::Legendary)) {
		return NewPump_ShotgunTexture;
	}
	else if (itemName == "OG Assault Rifle" && (itemRarity == EFortRarity::Common || itemRarity == EFortRarity::Uncommon || itemRarity == EFortRarity::Rare)) {
		return AssaultRifleTexture;
	}
	else if (itemName == "Scar Assault Rifle" && (itemRarity == EFortRarity::Epic || itemRarity == EFortRarity::Legendary)) {
		return ScarAssaultRifleTexture;
	}

	// Check if the item name contains any key in the map
	for (const auto& [key, texture] : textureMap) {
		if (itemName.find(key) != std::string::npos) {
			return texture;
		}
	}
	// Return nullptr if no texture was found
	return nullptr;
}

bool IsAmmoItem(const std::string& itemName) {
	static const std::unordered_set<std::string> ammoItems = {
		"Light Bullets", "Medium Bullets", "Heavy Bullets", "Shells", "Rockets"
	};
	return ammoItems.find(itemName) != ammoItems.end();
}

bool IsChestItem(const std::string& itemName) {
	return itemName.find("Chest") != std::string::npos;
}

int itemCount = 0; // Initialize an item counter

void AddPickup(std::vector<Pickups>& pickupList, uintptr_t actor, const std::string& itemName, const Vector3& itemPosition, ImColor itemColor, const std::string& category, EFortRarity itemRarity) {
	if (category == "Weapon" && !Items.Weapon.Rarity[static_cast<int>(itemRarity)]) return;  // Skip adding to the list if the weapon's rarity is not selected
	float itemDistance = vCamera.Location.Distance(itemPosition) / 100.0f;
	if ((category == "Consumable" && itemDistance > Items.Consumable.MaxDistance) ||
		(category == "Weapon" && itemDistance > Items.Weapon.MaxDistance) ||
		(category == "Ammo" && itemDistance > Items.Ammo.MaxDistance) ||
		(category == "Other" && itemDistance > Items.Other.MaxDistance)) {
		return; // Skip adding items beyond the max distance
	}

	Pickups pickup;
	pickup.Actor = actor;
	pickup.Rarity = itemRarity;  // Set the rarity

	std::string displayName = itemName;

	if (category == "Consumable") {
		pickup.Consumable = displayName;
	}
	else if (category == "Weapon") {
		displayName = GetExactRenamedItem(itemName, itemRarity);
		pickup.Weapons = displayName;
	}
	else if (category == "Ammo") {
		pickup.Ammo = displayName;
	}
	else if (category == "Other") {
		//std::cout << "[LOG] Other: " << displayName << std::endl;
		pickup.Other = displayName;
	}

	pickup.ItemPosition = itemPosition;
	pickup.Color = itemColor;
	pickupList.push_back(pickup);
	//pickupList.emplace_back(std::move(pickup));

}

struct CachedLoot {
	std::vector<Pickups> items;
	std::chrono::steady_clock::time_point lastUpdate;
	uint32_t lastMatchId = 0;  // Track match changes
	std::mutex mutex;
};

static CachedLoot gLootCache;
static const int LOOT_UPDATE_INTERVAL_MS = 500;

void ResetLootCache() {
	std::lock_guard<std::mutex> lock(gLootCache.mutex);
	gLootCache.items.clear();
	gLootCache.lastMatchId = 0;
}


bool IsValidActor(uintptr_t actor) {
	if (!actor || actor == (uintptr_t)-1)
		return false;

	// Basic validation check
	try {
		// Try reading a small amount of memory to verify the address is valid
		uint8_t testByte = UseDriver::read<uint8_t>(actor);
		return true;
	}
	catch (...) {
		return false;
	}
}

void LootSystem::ProcessLoot() {
	static std::vector<Pickups> tempList;
	auto currentTime = std::chrono::steady_clock::now();


	tempList.clear();
	tempList.reserve(100);

	if (!(Items.Consumable.Enable || Items.Weapon.Enable || Items.Ammo.Enable || Items.Other.Enable)) return;
	TArray<uintptr_t> Levels = UseDriver::read<TArray<uintptr_t>>(Results::Uworld + Offsets::Levels);
	for (int a = 0; a < Levels.Size(); a++) {
		uintptr_t levelAddress = Levels[a];
		if (!levelAddress) continue;
		TArray<uintptr_t> AActors = UseDriver::read<TArray<uintptr_t>>(Levels[a] + Offsets::ActorsArray);

		for (int i = 0; i < AActors.Size(); i++) {
			uintptr_t CurrentActor = AActors[i];
			if (!CurrentActor) continue;

			std::string ItemName;
			uint64_t PrimaryPickupItemEntry = UseDriver::read<uint64_t>(CurrentActor + 0x370 + 0x10);
			if (!PrimaryPickupItemEntry) continue;

			EFortPickupSpawnSource SpawnSourceOverride = UseDriver::read<EFortPickupSpawnSource>(CurrentActor + 0xc98);
			EFortRarity Rarity = UseDriver::read<EFortRarity>(PrimaryPickupItemEntry + Offsets::Rarity);

			ImColor ItemRarity = GetPickupColor(Rarity);

			uint64_t Enemyftext_ptr = UseDriver::read<uint64_t>(PrimaryPickupItemEntry + 0x40);

			if (Enemyftext_ptr) {
				uint64_t Enemyftext_data = UseDriver::read<uint64_t>(Enemyftext_ptr + 0x20);
				int Enemyftext_length = UseDriver::read<int>(Enemyftext_ptr + 0x28);
				if (Enemyftext_length > 0 && Enemyftext_length < 50) {
					wchar_t* Enemyftext_buf = new wchar_t[Enemyftext_length];
					UseDriver::ReadString(Enemyftext_data, Enemyftext_buf, Enemyftext_length * sizeof(wchar_t));
					std::wstring Enemywstr_buf(Enemyftext_buf);
					ItemName = std::string(Enemywstr_buf.begin(), Enemywstr_buf.end());
					//std::cout << "[LOG] ItemName : " << ItemName << std::endl;
					delete[] Enemyftext_buf;
				}
			}
			Vector3 ItemPosition = LootLocation(CurrentActor);

			// Consumable check
			if (Items.Consumable.Enable && IsConsumableItem(ItemName)) {
				bool addConsumable = true;
				for (const auto& [name, enabled] : consumableMap) {
					if (!*enabled && ItemName.find(name) != std::string::npos) {
						addConsumable = false;
						break;
					}
				}
				if (addConsumable) {
					AddPickup(tempList, CurrentActor, ItemName, ItemPosition, ItemRarity, "Consumable", Rarity);
				}
			}

			// Weapon check
			if (Items.Weapon.Enable) {
				ItemName = GetExactRenamedItem(ItemName, Rarity);
				if (IsWeaponItem(ItemName)) {
					bool addWeapon = true;
					for (const auto& [name, enabled] : weaponMap) {
						if (!*enabled && ItemName == name) {  // Ensure exact match without `find`
							addWeapon = false;
							break;
						}
					}
					if (addWeapon) {
						AddPickup(tempList, CurrentActor, ItemName, ItemPosition, ItemRarity, "Weapon", Rarity);
					}
				}
			}

			// Ammo check
			if (Items.Ammo.Enable && IsAmmoItem(ItemName)) {
				bool addAmmo = true;
				for (const auto& [name, enabled] : ammoMap) {
					if (!*enabled && ItemName.find(name) != std::string::npos) {
						addAmmo = false;
						break;
					}
				}
				if (addAmmo) {
					AddPickup(tempList, CurrentActor, ItemName, ItemPosition, ItemRarity, "Ammo", Rarity);
				}
			}
			// ===================== Chest Check =====================
			if (Items.Other.Chest) {
				if (SpawnSourceOverride == EFortPickupSpawnSource::Chest) {
					AddPickup(tempList, CurrentActor, "Chest", ItemPosition, ImColor(255, 215, 0), "Other", Rarity);
				}
			}

			// ===================== SupplyDrop Check =====================
			if (Items.Other.SupplyDrop) {
				if (SpawnSourceOverride == EFortPickupSpawnSource::SupplyDrop) {
					AddPickup(tempList, CurrentActor, "Supply Drop", ItemPosition, ImColor(0, 191, 255), "Other", Rarity);
				}
			}

			// ===================== Vehicle Check =====================
			/*if (Items.Other.Vehicle) {
				EFortPickupSpawnSource PickupSpawnSource = UseDriver::read<EFortPickupSpawnSource>(CurrentActor + 0x2c1);
				if (PickupSpawnSource == EFortPickupSpawnSource::Vehicle) {
					AddPickup(PickupTempList, CurrentActor, "Vehicle", ItemPosition, ImColor(128, 128, 128), "Other", Rarity);
				}
			}*/

			// ===================== Llama Check =====================
			/*if (Items.Other.Llama) {
				if (SpawnSourceOverride == EFortPickupSpawnSource::LootDrop) {
					AddPickup(PickupTempList, CurrentActor, "Llama", ItemPosition, ImColor(255, 105, 180), "Other", Rarity);
				}
			}*/
		}

	}

	// Update cache atomically
	{
		std::lock_guard<std::mutex> lock(gLootCache.mutex);
		gLootCache.items.swap(tempList);
		tempList.clear();
		gLootCache.lastUpdate = currentTime;
	}
}
void RenderItem(const std::string& itemName, const Vector3& itemPosition, EFortRarity itemRarity, float fontSize, bool showName, bool showDistance, float iconSize, bool showIcon, ImColor outlineColor, ImColor textColor, bool isWeapon, Vector2 ScreenLocation) {
	if (isWeapon && !Items.Weapon.Rarity[static_cast<int>(itemRarity)]) return;  // Skip rendering if the weapon's rarity is not selected

	if (!isItemOnScreen(ScreenLocation)) return;

	float itemDistance = vCamera.Location.Distance(itemPosition) / 100;
	std::string distanceText = "[" + std::to_string(static_cast<int>(itemDistance)) + "m]";

	std::string itemText;
	if (showName) {
		itemText += itemName;
	}
	if (showDistance) {
		if (!itemText.empty()) itemText += " ";
		itemText += distanceText;
	}

	if (!itemText.empty()) {
		ImFont* font = ImGui::GetFont();
		ImVec2 textSize = font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, itemText.c_str());
		ImVec2 textPosition = ImVec2(ScreenLocation.x - textSize.x / 2, ScreenLocation.y + 3 + 1);

		ImVec2 offsets[] = { {-0.5, -0.5}, {-0.5, 0}, {-0.5, 0.5}, {0, -0.5}, {0, 0.5}, {0.5, -0.5}, {0.5, 0}, {0.5, 0.5} };
		for (auto& offset : offsets) {
			ImVec2 outlinePos = ImVec2(textPosition.x + offset.x, textPosition.y + offset.y);
			ImGui::GetBackgroundDrawList()->AddText(font, fontSize, outlinePos, outlineColor, itemText.c_str());
		}
		ImGui::GetBackgroundDrawList()->AddText(font, fontSize, textPosition, textColor, itemText.c_str());
	}

	if (showIcon) {
		ID3D11ShaderResourceView* currentTexture = GetTextureForItem(itemName, itemRarity);
		if (currentTexture) {
			ImVec2 iconPosition = (showName || showDistance) ? ImVec2(ScreenLocation.x, ScreenLocation.y - 20) : ImVec2(ScreenLocation.x, ScreenLocation.y);
			DrawItemIconWithCircle(iconPosition, currentTexture, outlineColor, outlineColor, IM_COL32(0, 0, 0, 155), iconSize);
		}
	}
}


void LootSystem::DrawItems() {
	if (!(Items.Ammo.Enable || Items.Consumable.Enable || Items.Weapon.Enable || Items.Other.Enable)) return;
	std::vector<Pickups> currentItems;
	{
		std::lock_guard<std::mutex> lock(gLootCache.mutex);
		currentItems = gLootCache.items;
	}

	for (auto& Pickup : currentItems) {
		if (!IsValidActor(Pickup.Actor)) continue;

		Vector2 ScreenLocation = GameFunctions::ProjectWorldToScreen(Pickup.ItemPosition);
		if (!GameFunctions::isOnScreen(ScreenLocation)) continue;

		ImColor outlineColor = Pickup.Color;
		ImColor textColor = ImColor(255, 255, 255);

		// Render Consumable Items
		if (Items.Consumable.Enable && !Pickup.Consumable.empty()) {
			RenderItem(Pickup.Consumable, Pickup.ItemPosition, Pickup.Rarity, Items.Consumable.FontSize, Items.Consumable.Name, Items.Consumable.Distance, Items.Consumable.IconSize, Items.Consumable.Icons, outlineColor, textColor, false, ScreenLocation);
		}

		// Render Weapon Items
		if (Items.Weapon.Enable && !Pickup.Weapons.empty()) {
			RenderItem(Pickup.Weapons, Pickup.ItemPosition, Pickup.Rarity, Items.Weapon.FontSize, Items.Weapon.Name, Items.Weapon.Distance, Items.Weapon.IconSize, Items.Weapon.Icons, outlineColor, textColor, true, ScreenLocation);
		}

		// Render Ammo Items
		if (Items.Ammo.Enable && !Pickup.Ammo.empty()) {
			RenderItem(Pickup.Ammo, Pickup.ItemPosition, Pickup.Rarity, Items.Ammo.FontSize, Items.Ammo.Name, Items.Ammo.Distance, Items.Ammo.IconSize, Items.Ammo.Icons, outlineColor, textColor, false, ScreenLocation);
		}

		// Render Other Items
		if (Items.Other.Enable && !Pickup.Other.empty()) {
			RenderItem(Pickup.Other, Pickup.ItemPosition, Pickup.Rarity, Items.Other.FontSize, Items.Other.Name, Items.Other.Distance, Items.Other.IconSize, Items.Other.Icons, outlineColor, textColor, false, ScreenLocation);
		}
	}
}