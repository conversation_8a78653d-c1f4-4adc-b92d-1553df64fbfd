#pragma once
#include <string>
#include <unordered_set>
#include <shared_mutex>
#include <set>
#include <unordered_map>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../Menu UI/ImGui/imgui.h"
#include <d3d11.h>
#include "../../GameClass/GameSettings.h"
#include "../../Framwork/Vectors.h"
#include "../../Kernel Driver/Driver/Driver.h"
#include "../../GameClass/Offsets.h"
#include "icons.h"


class LootSystem {
public:
	static void ProcessLoot();
	static void DrawItems();
};

enum class EFortRarity : uint8_t
{
	Common = 0,
	Uncommon = 1,
	Rare = 2,
	Epic = 3,
	Legendary = 4,
	Mythic = 5
};

typedef struct Pickups
{
	uintptr_t Actor;
	std::string Name;
	std::string Consumable;
	std::string Weapons;
	std::string Ammo;
	std::string Other;
	std::string isVehicle;
	std::string isLlama;
	std::string isChest;
	std::string isSupplyDrop;
	std::string isPickup;
	std::string isAmmoBox;
	std::string distance;
	Vector3 ItemPosition;
	ImColor Color;
	ID3D11ShaderResourceView* ItemsIcon;
	EFortRarity Rarity;  // Add this line

} Pickups;
enum class EFortPickupSpawnSource : uint8_t {
	Unset = 0,
	PlayerElimination = 1,
	Chest = 2,
	SupplyDrop = 3,
	AmmoBox = 4,
	Drone = 5,
	ItemSpawner = 6,
	BotElimination = 7,
	NPCElimination = 8,
	LootDrop = 9,
	TossedByPlayer = 10,
	NPC = 11,
	NPCGift = 12,
	CraftingBench = 13,
	VendingMachine = 14,
	QuestReward = 15

};
// Define mapping for consumable, weapon, and ammo checks
inline std::unordered_map<std::string, bool*> consumableMap = {
	{"Bandage", &Items.Consumable.Bandages},
	{"Med Kit", &Items.Consumable.Medkit},
	{"Shield Potion", &Items.Consumable.ShieldPotion},
	{"FlowBerry Fizz", &Items.Consumable.FlowBerryFizz},
	{"Chug Splash", &Items.Consumable.ChugSplash},
	{"Nitro Splash", &Items.Consumable.NitroSplash},
	{"Nuka Cola", &Items.Consumable.NukaCola},
	{"Small Shield Potion", &Items.Consumable.SmallShieldPotion},
	{"Bandage Bazooka", &Items.Consumable.NukaCola} // Assuming intentional duplicate for Bazooka
};
inline std::unordered_map<std::string, bool*> chestMap = {
	{"Chest", &Items.Other.Chest},
};

inline std::unordered_map<std::string, bool*> weaponMap = {
	{"Harbinger", &Items.Weapon.HarbingerSMG},
	{"Thunder Burst SMG", &Items.Weapon.ThunderBurstSMG},
	{"Combat Assault Rifle", &Items.Weapon.CombatAssaultRifle},
	{"Enforcer AR", &Items.Weapon.EnforcerAssaultRifle},
	{"Tactical Assault Rifle", &Items.Weapon.TacticalAssaultRifle},
	{"Warforged Assault Rifle", &Items.Weapon.WarforgedAssaultRifle},
	{"Hammer Pump Shotgun", &Items.Weapon.HammerPumpShotgun},
	{"Gatekeeper Shotgun", &Items.Weapon.GatekeeperShotgun},
	{"Combat Shotgun", &Items.Weapon.CombatShotgun},
	{"Frenzy Auto Shotgun", &Items.Weapon.FrenzyAutoShotgun},
	{"Ranger Pistol", &Items.Weapon.RangerPistol},
	{"DMR", &Items.Weapon.HuntressDMR},
	{"Boom Bolt", &Items.Weapon.BoomBolt},
	{"Hand Cannon", &Items.Weapon.HandCannon},
	{"Nitro Fists", &Items.Weapon.NitroFists},
	{"Shockwave Grenade", &Items.Weapon.Shockwavegrenade},
	{"Heavy Impact Sniper Rifle", &Items.Weapon.HeavyImpactSniperRifle},
	{"MK-Seven Assault Rifle", &Items.Weapon.MKSevenAssaultRifle},
	{"Pump Shotgun", &Items.Weapon.NewPump_Shotgun},
	{"OG Pump Shotgun", &Items.Weapon.OGPump_Shotgun},
	{"Ocean's Bottomless Chug Jug", &Items.Weapon.BottomlessChugJug},
	{"Midas' Drum Gun", &Items.Weapon.DrumGun},
	{"Skye's AR", &Items.Weapon.SkyesAR},
	{"Grappler", &Items.Weapon.Grappler},
	{"Gunnar's Stinger SMG", &Items.Weapon.StingerSMG},
	{"Slone's Burst Assault Rifle", &Items.Weapon.BurstAR},
	{"Heisted Breacher Shotgun", &Items.Weapon.HeistedBreacherShotgun},
	{"Heisted Accelerant Shotgun", &Items.Weapon.HeistedAccelerantShotgun},
	{"Heisted Explosive AR", &Items.Weapon.HeistedExplosiveAR},
	{"Heisted Blink Mag SMG", &Items.Weapon.HeistedBlinkMagSMG},
	{"Heisted Run 'N' Gun SMG", &Items.Weapon.HeistedRunGunSMG},
	{"Tactical Shotgun", &Items.Weapon.TacticalShotgun},
	{"Lever Action Shotgun", &Items.Weapon.LeverActionShotgun},
	{"Heavy Shotgun", &Items.Weapon.HeavyShotgun},
	{"Ranger Shotgun", &Items.Weapon.RangerShotgun},
	{"OG Assault Rifle", &Items.Weapon.AssaultRifle},
	{"Scar Assault Rifle", &Items.Weapon.ScarAssaultRifle},
	{"Hammer Assault Rifle", &Items.Weapon.HammerAssaultRifle},
	{"Heavy Assault Rifle", &Items.Weapon.HeavyAssaultRifle},
	{"Infantry Rifle", &Items.Weapon.InfantryRifle},
	{"Submachine Gun", &Items.Weapon.SubmachineGun},
	{"Tactical Submachine Gun", &Items.Weapon.TacticalSubmachineGun},
	{"Bolt-Action Sniper Rifle", &Items.Weapon.BoltActionSniperRifle},
	{"Hunting Rifle", &Items.Weapon.HuntingRifle},
	{"Pistol", &Items.Weapon.Pistol},
	{"Revolver", &Items.Weapon.Revolver},
	{"Rocket Launcher", &Items.Weapon.RocketLauncher},
	{"Crash Pad", &Items.Weapon.CrashPad},
	{"Monarch Pistol", &Items.Weapon.MonarchPistol},
	{"Dual Micro SMGs", &Items.Weapon.DualMicroSMGs},
	{"Striker Burst Rifle", &Items.Weapon.StrikerBurstRifle},
	{"Hyper SMG", &Items.Weapon.HyperSMG},
	{"Striker AR", &Items.Weapon.StrikerAR},
	{"Sovereign Shotgun", &Items.Weapon.SovereignShotgun},
	{"Firefly Jar", &Items.Weapon.FireflyJar},
	{"Dual Pistols", &Items.Weapon.DualPistols},
	{"Rapid Fire SMG", &Items.Weapon.RapidFireSMG},
	{"Suppressed SMG", &Items.Weapon.SuppressedSMG},
	{"Suppressed Assault Rifle", &Items.Weapon.SuppressedAssaultRifle},
	{"Heavy Sniper Rifle", &Items.Weapon.HeavySniperRifle},
	{"Semi-Automatic Sniper Rifle", &Items.Weapon.SemiAutomaticSniperRifle},
	{"Grenade Launcher", &Items.Weapon.GrenadeLauncher},
	{"Remote Explosives", &Items.Weapon.RemoteExplosives},
	{"Regular Grenades", &Items.Weapon.RegularGrenades},
	{"Stink Bomb", &Items.Weapon.StinkBomb},
	{"Bandage Bazooka", &Items.Weapon.BandageBazooka},
	{"Boogie Bomb", &Items.Weapon.BoogieBomb},
	{"Clinger", &Items.Weapon.Clinger}
};

inline std::unordered_map<std::string, bool*> ammoMap = {
	{"Light Bullets", &Items.Ammo.AmmoLight},
	{"Medium Bullets", &Items.Ammo.AmmoMedium},
	{"Heavy Bullets", &Items.Ammo.AmmoHeavy},
	{"Shells", &Items.Ammo.AmmoShells},
	{"Rockets", &Items.Ammo.AmmoRockets}
};
inline bool isItemOnScreen(const Vector2& pos) {
	return pos.x >= 0 && pos.x <= OverlayWidth && pos.y >= 0 && pos.y <= OverlayHeight;
}
inline Vector3 LootLocation(uintptr_t actor) {
	uintptr_t rootComponent = UseDriver::read<uintptr_t>(actor + Offsets::RootComponent);
	if (!rootComponent) {
		return Vector3{ 0, 0, 0 };
	}
	Vector3 location = UseDriver::read<Vector3>(rootComponent + Offsets::Loot_RelativeLocation);
	return location;
}


ImColor GetPickupColor(EFortRarity Rarity);
bool IsConsumableItem(const std::string& itemName);
bool IsWeaponItem(const std::string& itemName);
ID3D11ShaderResourceView* GetTextureForItem(const std::string& itemName, EFortRarity itemRarity);
bool IsAmmoItem(const std::string& itemName);
bool IsChestItem(const std::string& itemName);
void AddPickup(std::vector<Pickups>& pickupList, uintptr_t actor, const std::string& itemName, const Vector3& itemPosition, ImColor itemColor, const std::string& category, EFortRarity itemRarity);
void ReadPickups(std::vector<Pickups>& PickupTempList);
std::string GetExactRenamedItem(const std::string& itemName, EFortRarity rarity);
void RenderItem(const std::string& itemName, const Vector3& itemPosition, EFortRarity itemRarity, float fontSize, bool showName, bool showDistance, float iconSize, bool showIcon, ImColor outlineColor, ImColor textColor, bool isWeapon, Vector2 ScreenLocation);
