#pragma once
#include <Windows.h>
#include "../Framwork/Vectors.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../Menu UI/ImGui/imgui.h"
#include "../../Menu UI/ImGui/imgui_impl_win32.h"
#include "../../Menu UI/ImGui/imgui_impl_dx11.h"
//-----------------------------------------------------------------------------------
// Global Variables
//-----------------------------------------------------------------------------------
inline Camera vCamera;
inline bool ShowMenu = true;
inline HWND GameWindow = NULL;
inline HWND Window = NULL;
inline int OverlayWidth = 0;
inline int OverlayHeight = 0;
inline RECT GameRec;
inline int GameX = 0;
inline int GameY = 0;
inline uintptr_t oBaseAddress = 0;
inline bool InLobby;


//-----------------------------------------------------------------------------------
// Game offsets namespace
//-----------------------------------------------------------------------------------

namespace Offsets
{
    inline DWORD64 UWorld = 0x176C0298;
    inline DWORD OwningGameInstance = 0x238;
    inline DWORD RootComponent = 0x1b0;
    inline DWORD PlayerState = 0x2C8;
    inline DWORD Mesh = 0x328;
    inline DWORD LocalPlayers = 0x38;
    inline DWORD PlayerController = 0x30;
    inline DWORD AcknowledgedPawn = 0x350;

    inline DWORD ActorID = 0x18;
    inline DWORD PersistentLevel = 0x38;
    inline DWORD TeamIndex = 0x1251;
    inline DWORD Controller = 0x2d8;
    inline DWORD bIsABot = 0x2B2;
    inline DWORD bIsKnocked = 0x729;
    inline DWORD isDBNO = 0x98a;
    inline DWORD bIsAnAthenaGameParticipant = 0x198f;
    inline DWORD bIsScriptedBot = 0x2014;

    inline DWORD Levels = 0x1D8;
    inline DWORD ActorsArray = 0xF8;
    inline DWORD ActorCount = ActorsArray + 0x8;

    inline DWORD ComponentToWorld = 0x1E0;
    inline DWORD Loot_RelativeLocation = 0x138;

    inline DWORD CameraLocation = 0x160;
    inline DWORD CameraRotation = 0x170;
    inline DWORD BoneArray = 0x5E8;

    inline DWORD ComponentVelocity = 0x180;
    inline DWORD Velocity = 0xb8;

    inline DWORD GameState = 0x1C0;
    inline DWORD PlayerArray = 0x2C0;
    inline DWORD PawnPrivate = 0x320;
    inline DWORD HabaneroComponent = 0xa48;
    inline DWORD Rank = 0xd0;

    constexpr inline uintptr_t SpectatorArray = 0x1ae0;

    inline DWORD TargetedFortPawn = 0x18a0;
    inline DWORD PlayerCameraManager = 0x360;

    inline DWORD CustomTimeDilation = 0x68;

    inline DWORD CurrentWeapon = 0xAA8;
    inline DWORD WeaponData = 0x618;
    inline uintptr_t ItemName = 0x40;
    inline uintptr_t FData = 0x20;
    inline uintptr_t FLength = 0x28;

    inline DWORD Rarity = 0xA2;

    inline DWORD ServerWorldTimeSecondsDelta = 0x180;
    inline DWORD LastSubmitTimeOnScreen = 0x2E8;
    inline DWORD LastRenderTime = 0x32C;

    inline DWORD ViewYawMin = 0x25f4;
    inline DWORD ViewYawMax = 0x25f8;
    inline DWORD ViewPitchMin = 0x25EC;
    inline DWORD ViewPitchMax = 0x25f0;
    inline DWORD AimPitchMin = 0x1bb0;
    inline DWORD AimPitchMax = 0x1bb4;


    inline DWORD Health = 0x30;
    inline DWORD HealthSet = 0x13e8;
    inline DWORD SeasonLevelUIDisplay = 0x126c;
    inline DWORD KillScore = 0x1268;

}


// Results from memory operations
namespace Results {
    inline DWORD_PTR Uworld;
    inline DWORD_PTR PlayerController;
    inline DWORD_PTR PlayerCameraManager;
    inline DWORD_PTR LocalPlayer;
    inline DWORD_PTR AcknowlegedPawn;
    inline DWORD_PTR GameState;
} 