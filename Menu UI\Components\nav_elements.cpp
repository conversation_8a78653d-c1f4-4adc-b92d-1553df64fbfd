﻿#include "nav_elements.h"
#include "windows.h"
#include <unordered_map>
#include <algorithm> // For std::min
#include "../../Cheat Core/Features/Input/InputManager.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../ImGui/imgui.h"
#include "../ImGui/imgui_internal.h"
#include "../../Utils.h"
#include "../Fonts/font_defines.h"
#include "../Fonts/custom_icons.h"
#include "../../Cheat Core/Settings/Settings.h"
#include "../../Cheat Core/Features/Input/HotkeySystem.h"

// Helper function to get key name from key code
const char* GetKeyName(int key) {
    static char buffer[256];

    if (key == 0) {
        return "(none)";
    }

    // Get key name using Windows API
    UINT scanCode = MapVirtualKey(key, MAPVK_VK_TO_VSC);

    // Handle special keys
    switch (key) {
        case VK_LEFT: case VK_UP: case VK_RIGHT: case VK_DOWN:
        case VK_PRIOR: case VK_NEXT:
        case VK_END: case VK_HOME:
        case VK_INSERT: case VK_DELETE:
        case VK_DIVIDE:
        case VK_NUMLOCK:
            scanCode |= 0x100; // set extended bit
            break;
    }

    if (GetKeyNameTextA((scanCode << 16), buffer, sizeof(buffer)) == 0) {
        // If GetKeyNameText fails, use a simple lookup for common keys
        switch (key) {
            case VK_LBUTTON: return "Left Mouse";
            case VK_RBUTTON: return "Right Mouse";
            case VK_MBUTTON: return "Middle Mouse";
            case VK_XBUTTON1: return "Mouse 4";
            case VK_XBUTTON2: return "Mouse 5";
            case VK_BACK: return "Backspace";
            case VK_TAB: return "Tab";
            case VK_RETURN: return "Enter";
            case VK_SHIFT: return "Shift";
            case VK_CONTROL: return "Ctrl";
            case VK_MENU: return "Alt";
            case VK_PAUSE: return "Pause";
            case VK_CAPITAL: return "Caps Lock";
            case VK_ESCAPE: return "Escape";
            case VK_SPACE: return "Space";
            case VK_PRIOR: return "Page Up";
            case VK_NEXT: return "Page Down";
            case VK_END: return "End";
            case VK_HOME: return "Home";
            case VK_LEFT: return "Left";
            case VK_UP: return "Up";
            case VK_RIGHT: return "Right";
            case VK_DOWN: return "Down";
            case VK_SNAPSHOT: return "Print Screen";
            case VK_INSERT: return "Insert";
            case VK_DELETE: return "Delete";
            default:
                sprintf_s(buffer, "Key %d", key);
                return buffer;
        }
    }

    return buffer;
}

// Additional icon definitions needed for CheckboxComponent
#define ICON_CIRCLE_FILL "\uea80"
#define ICON_SETTINGS_LINE "\uf1de"

// Logging macro (if not already defined)
#ifndef LOG_DEBUG
#define DEBUG_LOG 0
#if DEBUG_LOG
#define LOG_DEBUG(msg, ...) printf("[DEBUG_NAV] " msg "\n", ##__VA_ARGS__)
#else
#define LOG_DEBUG(msg, ...)
#endif
#endif

using namespace ImGui;

void nav_elements::Theme()
{
    ImGuiStyle* style = &ImGui::GetStyle();
    ImVec4* colors = style->Colors;
    style->Colors[ImGuiCol_ChildBg].w = 5.3f;
    colors[ImGuiCol_Text] = ImVec4(255 / 255.f, 255 / 255.f, 255 / 255.f, 255 / 255.f);
    colors[ImGuiCol_TextDisabled] = ImVec4(0.50f, 0.50f, 0.50f, 1.00f);
    colors[ImGuiCol_WindowBg] = ImVec4(23 / 255.f, 17 / 255.f, 13 / 255.f, 40 / 255.f); // Swapped 240
    colors[ImGuiCol_ChildBg] = ImVec4(11 / 255.f, 11 / 255.f, 11 / 255.f, 120 / 255.f);
    colors[ImGuiCol_PopupBg] = ImVec4(23 / 255.f, 17 / 255.f, 13 / 255.f, 200 / 255.f); // Swapped
    colors[ImGuiCol_Border] = ImVec4(81 / 255.f, 65 / 255.f, 55 / 255.f, 255 / 255.f); // Swapped
    colors[ImGuiCol_BorderShadow] = ImVec4(0.90f, 0.40f, 0.40f, 0.00f); // Swapped
    colors[ImGuiCol_FrameBg] = ImVec4(41 / 255.f, 34 / 255.f, 33 / 255.f, 180 / 255.f); // Swapped
    colors[ImGuiCol_FrameBgHovered] = ImVec4(37 / 255.f, 31 / 255.f, 30 / 255.f, 200 / 255.f); // Swapped
    colors[ImGuiCol_FrameBgActive] = ImVec4(37 / 255.f, 31 / 255.f, 30 / 255.f, 200 / 255.f); // Swapped
    colors[ImGuiCol_TitleBg] = ImVec4(0.04f, 0.04f, 0.04f, 1.00f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.48f, 0.29f, 0.16f, 1.00f); // Swapped
    colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.00f, 0.00f, 0.00f, 0.51f);
    colors[ImGuiCol_MenuBarBg] = ImVec4(0.14f, 0.14f, 0.14f, 1.00f);
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.06f, 0.06f, 0.06f, 0.00f);
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(48 / 255.f, 39 / 255.f, 35 / 255.f, 0.00f); // Swapped
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(48 / 255.f, 39 / 255.f, 35 / 255.f, 0.00f); // Swapped
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(48 / 255.f, 39 / 255.f, 35 / 255.f, 0.00f); // Swapped
    colors[ImGuiCol_CheckMark] = ImVec4(0.20f, 0.20f, 0.20f, 1.00f);
    colors[ImGuiCol_SliderGrab] = ImVec4(0.88f, 0.52f, 0.24f, 1.00f); // Swapped
    colors[ImGuiCol_SliderGrabActive] = ImVec4(0.98f, 0.59f, 0.26f, 1.00f); // Swapped
    colors[ImGuiCol_Button] = ImVec4(44 / 255.f, 34 / 255.f, 27 / 255.f, 50 / 255.f); // Swapped
    colors[ImGuiCol_ButtonHovered] = ImVec4(44 / 255.f, 34 / 255.f, 27 / 255.f, 50 / 255.f); // Swapped
    colors[ImGuiCol_ButtonActive] = ImVec4(44 / 255.f, 34 / 255.f, 27 / 255.f, 50 / 255.f); // Swapped
    colors[ImGuiCol_Header] = ImVec4(0.10f, 0.10f, 0.10f, 0.70f);
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.45f, 0.45f, 0.45f, 0.10f);
    colors[ImGuiCol_HeaderActive] = ImVec4(0.45f, 0.45f, 0.45f, 0.10f);
    colors[ImGuiCol_Separator] = colors[ImGuiCol_Border];
    colors[ImGuiCol_SeparatorHovered] = ImVec4(0.75f, 0.40f, 0.10f, 0.78f); // Swapped
    colors[ImGuiCol_SeparatorActive] = ImVec4(0.75f, 0.40f, 0.10f, 1.00f); // Swapped
    colors[ImGuiCol_ResizeGrip] = ImVec4(0.98f, 0.59f, 0.26f, 0.25f); // Swapped
    colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.98f, 0.59f, 0.26f, 0.67f); // Swapped
    colors[ImGuiCol_ResizeGripActive] = ImVec4(0.98f, 0.59f, 0.26f, 0.95f); // Swapped
    colors[ImGuiCol_Tab] = ImLerp(colors[ImGuiCol_Header], colors[ImGuiCol_TitleBgActive], 0.80f);
    colors[ImGuiCol_TabHovered] = colors[ImGuiCol_HeaderHovered];
    colors[ImGuiCol_TabActive] = ImLerp(colors[ImGuiCol_HeaderActive], colors[ImGuiCol_TitleBgActive], 0.60f);
    colors[ImGuiCol_TabUnfocused] = ImLerp(colors[ImGuiCol_Tab], colors[ImGuiCol_TitleBg], 0.80f);
    colors[ImGuiCol_TabUnfocusedActive] = ImLerp(colors[ImGuiCol_TabActive], colors[ImGuiCol_TitleBg], 0.40f);
    colors[ImGuiCol_PlotLines] = ImVec4(0.61f, 0.61f, 0.61f, 1.00f);
    colors[ImGuiCol_PlotLinesHovered] = ImVec4(0.35f, 0.43f, 1.00f, 1.00f); // Swapped
    colors[ImGuiCol_PlotHistogram] = ImVec4(0.00f, 0.70f, 0.90f, 1.00f); // Swapped
    colors[ImGuiCol_PlotHistogramHovered] = ImVec4(0.00f, 0.60f, 1.00f, 1.00f); // Swapped
    colors[ImGuiCol_TextSelectedBg] = ImVec4(124 / 255.f, 54 / 255.f, 90 / 255.f, 80 / 255.f); // Swapped
    colors[ImGuiCol_DragDropTarget] = ImVec4(0.00f, 1.00f, 1.00f, 0.90f); // Swapped
    colors[ImGuiCol_NavHighlight] = ImVec4(0.98f, 0.59f, 0.26f, 1.00f); // Swapped
    colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
    colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.20f);
    colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.35f);
    style->WindowBorderSize = 1;
    style->WindowRounding = 12.f;
    style->FrameRounding = 4.f;
    style->FramePadding = ImVec2(10, 10);
    style->WindowPadding = ImVec2(0, 0);
    style->ItemSpacing = ImVec2(10, 10);
    style->ChildBorderSize = 0;
    style->ChildRounding = 12.f;
    style->ScrollbarRounding = 15.f;
    style->ScrollbarSize = 13.f;
    style->PopupRounding = 4.f;
    style->PopupBorderSize = 1;
    style->GrabMinSize = 0;
}

struct tab_states {
    ImVec4 text_col[2], icon_col;
    ImVec4 frame_col;
    ImVec4 line_col;
    bool is_want = true;
};

struct tab_elements {
    float element_opacity; // Opacity for the background element
    float rect_opacity;    // Opacity for the rectangle
    float text_opacity;    // Opacity for the text
    ImVec4 icon_color;     // Color for the icon (with alpha for opacity)
    float line_opacity;    // Opacity for the left line
    float shadow_opacity;  // Opacity for the shadow (only shown when selected)
};
std::map<ImGuiID, tab_elements> anim_2;

bool nav_elements::Tab(const char* label, const char* icon, int* v, int number) {
    // Check if the current tab is active
    bool is_active = (*v == number);

    // Call the elements::tab function with the icon and label
    if (TabVS2(icon, label, is_active)) {
        // If the tab is pressed, update the selected tab
        *v = number;
        return true;
    }

    return false;
}
bool nav_elements::TabVS2(const char* icon, const char* name, bool boolean)
{
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(name);
    ImGui::PushFont(iconsBig);
    const ImVec2 icon_size = ImGui::CalcTextSize(icon);
    ImGui::PopFont();

    const ImVec2 label_size = ImGui::CalcTextSize(name); // Size of the label
    ImVec2 pos = window->DC.CursorPos;

    const ImRect rect(pos, ImVec2(pos.x + 181, pos.y + 42));
    ImGui::ItemSize(ImVec4(rect.Min.x, rect.Min.y, rect.Max.x, rect.Max.y + 5), style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || boolean) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand); // Set cursor to hand
    }

    auto it_anim = anim_2.find(id);
    if (it_anim == anim_2.end()) {
        anim_2.insert({ id, { 0.0f, 0.0f, 0.0f, ImVec4(1.0f, 1.0f, 1.0f, 0.7f), 0.0f } }); // Initialize icon color and line opacity
        it_anim = anim_2.find(id);
    }

    ImVec2 size({ window->Size.x, 24 });
    if (pressed)
        content_anim = 0.f;

    // Animate line opacity
    it_anim->second.line_opacity = ImLerp(it_anim->second.line_opacity, (boolean || hovered) ? 1.0f : 0.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));


    // Animate element opacity
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity, (boolean ? 0.04f : hovered ? 0.01f : 0.0f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.rect_opacity = ImLerp(it_anim->second.rect_opacity, (boolean ? 1.0f : 0.0f), 0.15f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.text_opacity = ImLerp(it_anim->second.text_opacity, (boolean ? 1.0f : hovered ? 0.5f : 0.3f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Animate icon color
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        boolean || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f), // Darker shadow when active or hovered
        GetAnimSpeed()
    );

    // Draw the background rectangle
    window->DrawList->AddRectFilled(rect.Min, rect.Max, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Draw the border
    ImVec4 border_color = boolean || hovered ? ImVec4(ImColor(255, 255, 255, 10)) : ImVec4(ImColor(255, 255, 255, 10)); // Grey when disabled, main color when hovered/clicked
    window->DrawList->AddRect(rect.Min, rect.Max, ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);


    const ImVec2 label_pos = ImVec2(rect.GetCenter().x - label_size.x / 2, rect.GetCenter().y - label_size.y / 2 - 2); // Middle for label

    float rect_height = rect.GetHeight(); // Should be 42
    float rect_center_y = rect.Min.y + (rect_height * 0.5f);

    // Padding on the X-axis for the icon:
    const float icon_padding = 5.0f;

    // Calculate the position for the icon so it's centered vertically:
    ImVec2 icon_pos = ImVec2(
        rect.Min.x + icon_padding,
        rect_center_y - (icon_size.y * 0.5f) // center the icon text
    );

    // Calculate the center of the icon for AddShadowCircle:
    ImVec2 icon_center = icon_pos + (icon_size * 0.5f);


    // Draw the icon shadow
    window->DrawList->AddShadowCircle(
        icon_center, // Center of the icon
        9.f, // Shadow radius
        ImGui::GetColorU32(it_anim->second.icon_color), // Animated shadow color
        40, // Shadow spread
        ImVec2(0, 0), // Shadow offset
        0, // Shadow flags (0 for default)
        360 // Shadow rounding
    );

    ImGui::PushFont(iconsBig); // Use the desired font for the icon
    window->DrawList->AddText(
        icon_pos,
        ImGui::GetColorU32(it_anim->second.icon_color), // Use the animated icon color
        icon
    );
    ImGui::PopFont();

    // Draw the label
    window->DrawList->AddText(
        label_pos, // Adjust label position as needed
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.text_opacity), // Use the desired text color
        name
    );

    // Define the rectangle for the line (inside the button on the left)
    const float line_width = 1.0f; // Width of the glowing line
    ImVec2 line_start = { rect.Min.x + 1, rect.Min.y + 10 }; // Inside the button on the left
    ImVec2 line_end = { rect.Min.x + 1 + line_width, rect.Max.y - 10 }; // Full height of the button

    if (boolean || hovered) {
        window->DrawList->AddShadowRect(
            line_start, // Start position of the line
            line_end,   // End position of the line
            gui.main, // Shadow color
            25.f,       // Shadow size (larger shadow)
            ImVec2(0, 0), // Shadow offset
            0,          // Shadow flags (0 for default)
            60.f       // Shadow rounding
        );
    }

    // Add the glowing line with animated opacity
    ImColor glow_color = ImColor(accent_color[2], accent_color[1], accent_color[0], it_anim->second.line_opacity); // Glow color with opacity
    window->DrawList->AddRectFilled(
        line_start, // Start position of the line
        line_end,   // End position of the line
        glow_color, // Glow color with opacity
        360.f,      // Rounding radius (360.f for fully rounded ends)
        ImDrawFlags_RoundCornersTop | ImDrawFlags_RoundCornersBottom // Round top and bottom corners
    );


    // Define the rectangle for the line (inside the button on the left)
    const float line_widths = 1.0f; // Width of the glowing line
    ImVec2 line_starts = { rect.Min.x + 1, rect.Min.y + 10 }; // Inside the button on the left
    ImVec2 line_ends = { rect.Min.x + 1 + line_widths, rect.Max.y - 10 }; // Full height of the button

    // Add shadow to the line (only when hovered or selected)
    if (boolean || hovered) {
        window->DrawList->AddShadowRect(
            line_starts, // Start position of the line
            line_ends,   // End position of the line
            ImGui::ColorConvertFloat4ToU32(glow_color), // Shadow color
            25.f,       // Shadow size (larger shadow)
            ImVec2(0, 0), // Shadow offset
            0,          // Shadow flags (0 for default)
            60.f       // Shadow rounding
        );
    }

    // Add the glowing line with animated opacity
    window->DrawList->AddRectFilled(
        line_starts, // Start position of the line
        line_ends,   // End position of the line
        glow_color, // Glow color with opacity
        360.f,      // Rounding radius (360.f for fully rounded ends)
        ImDrawFlags_RoundCornersTop | ImDrawFlags_RoundCornersBottom // Round top and bottom corners
    );

    return pressed;
}////////////

struct check_state {
    ImVec4 check_color, check_rect_color, check_color_circle, check_color_shadow, icon_color, rect_color, rect_shadow_color, background_color, background_rect_color;
    float circle_alpha;
    float element_opacity; // Opacity for the background element
    ImVec2 circle_offset;
    ImVec2 picker_offset{ 0, 200.f };

};

bool nav_elements::Checkbox(const char* label, bool* v, const char* description) {
    // --- Early return check ---
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    // --- Setup and ID generation ---
    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);
    std::string label_str = label;

    // --- Layout calculations ---
    const ImVec2 pos = window->DC.CursorPos;
    const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;
    const float component_height = 60.f;

    const ImRect total_bb(pos, pos + ImVec2(content_width, component_height));
    ItemSize(total_bb, 0.f);

    if (!ItemAdd(total_bb, id))
        return false;

    // --- Checkbox dimensions and positioning ---
    ImVec2 check_size = ImVec2(50, 26);
    const float check_spacing = (total_bb.GetSize().y - check_size.y) / 2;
    const ImRect check_bb(
        total_bb.Max - ImVec2(check_spacing, check_spacing) - check_size,
        total_bb.Max - ImVec2(check_spacing, check_spacing)
    );

    // --- Input handling ---
    bool hovered, held;
    bool pressed = ButtonBehavior(total_bb, id, &hovered, &held);

    if (IsItemClicked()) {
        *v = !(*v);
        MarkItemEdited(id);
    }

    // --- Text positioning ---
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    const ImVec2 label_size = CalcTextSize(label, NULL, true);
    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
    );

    // --- Animation state management ---
    static std::map<ImGuiID, check_state> anim;
    auto it_anim = anim.emplace(id, check_state()).first;

    // Update animation colors
    const float anim_speed = GetAnimSpeed();
    const bool is_active = *v;

    it_anim->second.check_color = ImLerp(
        it_anim->second.check_color,
        is_active ? gui.checkboxstroke : ImColor(34, 27, 27, 255),
        anim_speed / 3
    );

    it_anim->second.check_color_circle = ImLerp(
        it_anim->second.check_color_circle,
        is_active ? gui.main : ImColor(64, 57, 57, 255),
        anim_speed / 3
    );

    it_anim->second.rect_color = ImLerp(
        it_anim->second.rect_color,
        is_active || hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f),
        anim_speed
    );

    it_anim->second.rect_shadow_color = ImLerp(
        it_anim->second.rect_shadow_color,
        is_active || hovered ? gui.main : func.GetColorWithAlpha(ImColor(0, 0, 0, 255), 0.3f),
        anim_speed
    );

    it_anim->second.check_rect_color = ImLerp(
        it_anim->second.check_rect_color,
        is_active ? gui.checkboxstrokeactive : gui.checkboxstroke,
        anim_speed / 3
    );


    it_anim->second.circle_alpha = ImLerp(
        it_anim->second.circle_alpha,
        is_active ? 0.9f : 0.6f,
        anim_speed
    );

    ImColor main = ImLerpCol(gui.main, gui.main, ImGui::GetIO().DeltaTime * 10.f);
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        is_active || hovered ? func.GetDarkColor(main) : main,
        anim_speed
    );

    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity, (is_active ? 0.08f : hovered ? 0.04f : 0.2f), 0.14f * (1.0f - ImGui::GetIO().DeltaTime));

    it_anim->second.background_color = ImLerp(
        it_anim->second.background_color,
        is_active || hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
        anim_speed
    );
    it_anim->second.background_rect_color = ImLerp(
        it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f),
        anim_speed
    );
    //it_anim->second.background_rect_color = ImLerp(it_anim->second.background_rect_color, (is_active ? func.GetColorWithAlpha(gui.main_for_border, 0.3f) : hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f)), anim_speed);


    // Handle circle animation
    if (is_active) {
        EasingAnimationV2(
            "circle_offset" + label_str,
            &it_anim->second.circle_offset,
            ImVec2(24, 0),
            0.25f,
            imanim::EasingCurve::Type::InOutCubic,
            -1
        );
    }
    else {
        EasingAnimationV2(
            "circle_offset" + label_str,
            &it_anim->second.circle_offset,
            ImVec2(0, 0),
            0.25f,
            imanim::EasingCurve::Type::InOutCubic,
            -1
        );
    }

    // --- Drawing/Rendering ---
    // Background
    //RenderBackgroundElemets(total_bb.Min, total_bb.Max, window->DrawList);

    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max, GetColorU32(it_anim->second.background_color), 3.0f);
    window->DrawList->AddRect(total_bb.Min, total_bb.Max, GetColorU32(it_anim->second.background_rect_color), 3.0f);


    // Left background rectangle
    GetWindowDrawList()->AddRectFilled(
        total_bb.Min,
        total_bb.Min + ImVec2(total_bb.GetSize().y, total_bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color),
        style.FrameRounding,
        ImDrawFlags_RoundCornersLeft
    );

    window->DrawList->AddShadowRect(
        total_bb.Min, // Start position of the line
        total_bb.Min + ImVec2(total_bb.GetSize().y - 5, total_bb.GetSize().y),   // End position with small inset
        GetColorU32(it_anim->second.rect_shadow_color), // Shadow color
        20.f,       // Slightly smaller shadow size
        ImVec2(0, 0), // Negative X offset to pull shadow away from red line
        0,          // Shadow flags (0 for default)
        ImDrawFlags_RoundCornersLeft       // Shadow rounding
    );
    // Checkbox background
    GetWindowDrawList()->AddRectFilled(
        check_bb.Min,
        check_bb.Max,
        GetColorU32(it_anim->second.check_color),
        30.f
    );

    // Checkbox border
    GetWindowDrawList()->AddRect(
        check_bb.Min,
        check_bb.Max,
        GetColorU32(it_anim->second.check_rect_color),
        30.f
    );

    // Checkbox shadow
    window->DrawList->AddShadowCircle(
        check_bb.Min + check_bb.Max,
        8.f,
        GetColorU32(it_anim->second.check_color),
        15.f,
        ImVec2(0, 0),
        0,
        360
    );

    // Circle shadow
    window->DrawList->AddShadowCircle(
        check_bb.Min + ImVec2(13 + it_anim->second.circle_offset.x, 13),
        6.f,
        GetColorU32(it_anim->second.check_color_circle),
        70.f,
        ImVec2(0, 0),
        0,
        360
    );

    // Icon circle shadow
    window->DrawList->AddShadowCircle(
        total_bb.Min + ImVec2(total_bb.GetSize().y / 2, total_bb.GetSize().y / 2),
        9.f,
        GetColorU32(it_anim->second.icon_color),
        40,
        ImVec2(0, 0),
        0,
        360
    );

    // Slider circle
    window->DrawList->AddCircleFilled(
        check_bb.Min + ImVec2(13 + it_anim->second.circle_offset.x, 13),
        8.f,
        GetColorU32(it_anim->second.check_color_circle),
        360
    );


    // --- Text rendering ---
    // Main label
    GetWindowDrawList()->AddText(
        label_pos,
        ImColor(1.f, 1.f, 1.0f, 1.0f),
        label
    );

    // Description text
    const char* display_description = getStringBeforeCaret(description) != nullptr ?
        getStringBeforeCaret(description) :
        description;

    GetWindowDrawList()->AddText(
        label_pos + ImVec2(0.f, label_size.y),
        ImColor(1.f, 1.f, 1.f, 0.7f),
        display_description
    );

    // Icon (if present)
    if (findSymbolAfterCaret(description) != nullptr) {
        PushFont(iconsBig);
        GetWindowDrawList()->AddText(func.CalcTextPos(total_bb.Min,
            total_bb.Min + ImVec2(total_bb.GetSize().y, total_bb.GetSize().y),
            findSymbolAfterCaret(description),
            iconsBig),
            GetColorU32(it_anim->second.icon_color),
            findSymbolAfterCaret(description));
        PopFont();
    }

    return pressed;
}

struct slider_state {
    ImVec4 background, circle, label_col, description_col, rect_color, icon_color, background_color, background_rect_color;

    float position, slow;
};
bool nav_elements::SliderScalar(const char* label,
    ImGuiDataType data_type,
    void* p_data,
    const void* p_min,
    const void* p_max,
    const char* format,
    ImGuiSliderFlags flags,
    const char* description) {
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);
    const float w = GetContentRegionMax().x - style.WindowPadding.x;

    static std::map<ImGuiID, slider_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, slider_state() });
        it_anim = anim.find(id);
    }

    const ImVec2 pos = window->DC.CursorPos;

    const ImRect total_bb(window->DC.CursorPos, window->DC.CursorPos + ImVec2(w, 60.f));

    std::string label_str = label;

    //PushFont(fonts["regular"][3]);
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    //PopFont();

    const ImVec2 label_size = CalcTextSize(label, NULL, true);

    ImVec2 check_size = ImVec2(60, 26);
    const float check_spacing = (total_bb.GetSize().y - check_size.y) / 2;

    const ImRect slide_bb(total_bb.Max - ImVec2(check_spacing, check_spacing) - check_size,
        total_bb.Max - ImVec2(check_spacing, check_spacing));

    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2);

    bool hovered_slider = IsMouseHoveringRect(slide_bb.Min, slide_bb.Max);

    const bool temp_input_allowed = (flags & ImGuiSliderFlags_NoInput) == 0;
    ItemSize(ImRect(total_bb.Min, total_bb.Max));

    if (!ItemAdd(total_bb, id, &total_bb, temp_input_allowed ? ImGuiItemFlags_Inputable : 0)) return false;

    if (format == NULL) format = DataTypeGetInfo(data_type)->PrintFmt;

    bool hovered = ItemHoverable(total_bb, id, g.LastItemData.InFlags), held, pressed = ButtonBehavior(total_bb,
        id,
        &hovered,
        &held,
        NULL);

    ImRect grab_bb;

    bool data_changed = false;  // ������� ���������� ��� ������������ ���������
    bool value_changed = false;
    const float anim_speed = GetAnimSpeed();

    it_anim->second.background_color = ImLerp(
        it_anim->second.background_color,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
        anim_speed
    );
    it_anim->second.background_rect_color = ImLerp(
        it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f),
        anim_speed
    );

    //RenderBackgroundElemets(total_bb.Min, total_bb.Max, window->DrawList);
    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max, GetColorU32(it_anim->second.background_color), 3.0f);
    window->DrawList->AddRect(total_bb.Min, total_bb.Max, GetColorU32(it_anim->second.background_rect_color), 3.0f);

    GetWindowDrawList()->AddRectFilled(total_bb.Min,
        total_bb.Min + ImVec2(total_bb.GetSize().y, +total_bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color),
        style.FrameRounding,
        ImDrawFlags_RoundCornersLeft);

    window->DrawList->AddRectFilled(slide_bb.Min, slide_bb.Max, gui.second, 30);

    value_changed = DragBehavior(id, data_type, p_data, 1, p_min, p_max, format, flags);
    if (value_changed) {
        MarkItemEdited(id);
        data_changed = true;  // ���������� � true, ���� ������ ����������
    }

    int* f_data = static_cast<int*>(p_data);
    const int* f_min = static_cast<const int*>(p_min);
    const int* f_max = static_cast<const int*>(p_max);

    const int num_bars = 9;
    float bar_width = 4;
    float bar_spacing = (slide_bb.GetWidth() - num_bars * bar_width) / (num_bars - 1);
    float offset = fmodf(30.0 * (*f_data - *f_min) / static_cast<float>(*f_max - *f_min) * (bar_spacing + bar_width),
        bar_spacing + bar_width);

    for (int i = 0; i < num_bars; ++i) {
        float x = slide_bb.Min.x + i * (bar_width + bar_spacing) - offset;
        float dist_to_mid = std::abs(x - slide_bb.GetCenter().x);
        float height = ImLerp(14.0f, 0.0f, dist_to_mid / (slide_bb.GetWidth() / 1.2f));

        if (x + bar_width < slide_bb.Min.x || x > slide_bb.Max.x) continue;

        ImVec2 min = ImVec2(x, slide_bb.GetCenter().y - height / 2);
        ImVec2 max = ImVec2(x + bar_width, slide_bb.GetCenter().y + height / 2);

        window->DrawList->AddRectFilled(min,
            max,
            func.GetColorWithAlpha(gui.main,
                ImLerp(1.0f,
                    0.0f,
                    dist_to_mid / (slide_bb.GetWidth() / 2.3f))),
            30);
    }

    if (value_changed) MarkItemEdited(id);

    char value_buf[64];
    const char* value_buf_end =
        value_buf + DataTypeFormatString(value_buf, IM_ARRAYSIZE(value_buf), data_type, p_data, format);

    //PushFont(fonts["semibold"][2]);
    const ImRect value_bb(slide_bb.Min - ImVec2(30 + CalcTextSize(value_buf, value_buf_end).x, 0),
        slide_bb.Min - ImVec2(10, -slide_bb.GetSize().y));

    GetWindowDrawList()->AddRectFilled(value_bb.Min, value_bb.Max, func.GetColorWithAlpha(gui.main, 0.3f), 4.f);
    GetWindowDrawList()->AddText(value_bb.GetCenter() - CalcTextSize(value_buf, value_buf_end) / 2,
        gui.main,
        value_buf,
        value_buf_end);

    window->DrawList->AddShadowCircle(total_bb.Min + ImVec2(total_bb.GetSize().y / 2, total_bb.GetSize().y / 2),
        8.f,
        GetColorU32(it_anim->second.icon_color),
        60,
        ImVec2(0, 0),
        0,
        360);

    //PopFont();

    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color,
        hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f),
        GetAnimSpeed());
    it_anim->second.slow = ImLerp(it_anim->second.slow, grab_bb.Min.x - (total_bb.Min.x), g.IO.DeltaTime * 25.f);

    GetWindowDrawList()->AddText(label_pos, gui.text[0], label);

    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color,
        hovered ? func.GetDarkColor(gui.main) : gui.main,
        GetAnimSpeed());

    //PushFont(fonts["regular"][3]);
    GetWindowDrawList()->AddText(label_pos + ImVec2(0.f, label_size.y),
        gui.text[1],
        getStringBeforeCaret(description) != nullptr ? getStringBeforeCaret(description)
        : description);
    //PopFont();

    //if (findSymbolAfterCaret(description) != nullptr) {
    //    //PushFont(fonts["semibold"][6]);
    //    GetWindowDrawList()->AddText(func.CalcTextPos(total_bb.Min,
    //        total_bb.Min + ImVec2(total_bb.GetSize().y, total_bb.GetSize().y),
    //        findSymbolAfterCaret(description),
    //        fonts["semibold"][6]),
    //        GetColorU32(it_anim->second.icon_color),
    //        findSymbolAfterCaret(description));
    //    //PopFont();
    //}
    if (findSymbolAfterCaret(description) != nullptr) {
        PushFont(iconsBig);
        GetWindowDrawList()->AddText(func.CalcTextPos(total_bb.Min,
            total_bb.Min + ImVec2(total_bb.GetSize().y, total_bb.GetSize().y),
            findSymbolAfterCaret(description)),
            GetColorU32(it_anim->second.icon_color),
            findSymbolAfterCaret(description));
        PopFont();
    }

    return value_changed;
}

bool nav_elements::SliderFloat(const char* label,
    float* v,
    float v_min,
    float v_max,
    const char* description,
    const char* format,
    ImGuiSliderFlags flags) {
    return nav_elements::SliderScalar(label, ImGuiDataType_Float, v, &v_min, &v_max, format, flags, description);
}

bool nav_elements::SliderInt(const char* label,
    int* v,
    int v_min,
    int v_max,
    const char* description,
    const char* format,
    ImGuiSliderFlags flags) {
    return nav_elements::SliderScalar(label, ImGuiDataType_S32, v, &v_min, &v_max, format, flags, description);
}



const char* keys[] =
{
    "-",
    "Mouse 1",
    "Mouse 2",
    "CN",
    "Mouse 3",
    "Mouse 4",
    "Mouse 5",
    "-",
    "Back",
    "Tab",
    "-",
    "-",
    "CLR",
    "Enter",
    "-",
    "-",
    "Shift",
    "CTL",
    "Menu",
    "Pause",
    "Caps Lock",
    "KAN",
    "-",
    "JUN",
    "FIN",
    "KAN",
    "-",
    "Escape",
    "CON",
    "NCO",
    "ACC",
    "MAD",
    "Space",
    "PGU",
    "PGD",
    "End",
    "Home",
    "Left",
    "Up",
    "Right",
    "Down",
    "SEL",
    "PRI",
    "EXE",
    "PRI",
    "INS",
    "Delete",
    "HEL",
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "WIN",
    "WIN",
    "APP",
    "-",
    "SLE",
    "Numpad 0",
    "Numpad 1",
    "Numpad 2",
    "Numpad 3",
    "Numpad 4",
    "Numpad 5",
    "Numpad 6",
    "Numpad 7",
    "Numpad 8",
    "Numpad 9",
    "MUL",
    "ADD",
    "SEP",
    "MIN",
    "Delete",
    "DIV",
    "F1",
    "F2",
    "F3",
    "F4",
    "F5",
    "F6",
    "F7",
    "F8",
    "F9",
    "F10",
    "F11",
    "F12",
    "F13",
    "F14",
    "F15",
    "F16",
    "F17",
    "F18",
    "F19",
    "F20",
    "F21",
    "F22",
    "F23",
    "F24",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "NUM",
    "SCR",
    "EQU",
    "MAS",
    "TOY",
    "OYA",
    "OYA",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "-",
    "Shift",
    "Shift",
    "Ctrl",
    "Ctrl",
    "Alt",
    "Alt"
};

struct key_state {
    ImVec4 background, text;
    bool active = false;
    bool want_close;
    bool hovered = false;
    float alpha = 0.f;

    ImVec4 check_color, icon_color, rect_color, background_color;
};

struct keyset_state {
    ImVec4 background, text;
    bool active = false;
    bool hovered = false;
    float alpha = 0.f;
};

bool nav_elements::Keyset(const char* label, int* key) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    ImGuiIO& io = g.IO;
    const ImGuiStyle& style = g.Style;

    const ImGuiID id = window->GetID(label);
    const float width = (GetContentRegionMax().x - style.WindowPadding.x);

    const ImRect rect(window->DC.CursorPos, window->DC.CursorPos + ImVec2(width, 32));

    ItemSize(ImRect(rect.Min, rect.Max));
    if (!ImGui::ItemAdd(rect, id)) return false;

    char buf_display[64] = "None";

    bool value_changed = false;
    int k = *key;

    // Get the key name using our improved InputManager::GetKeyName function
    if (*key != 0 && g.ActiveId != id) {
        strcpy_s(buf_display, InputManager::GetKeyName(*key).c_str());
    }
    else if (g.ActiveId == id) {
        strcpy_s(buf_display, "Press Any Key...");
    }

    const ImVec2 label_size = CalcTextSize(buf_display, NULL, true);
    std::string label_str = label;

    bool hovered = ItemHoverable(rect, id, NULL);

    static std::map<ImGuiID, keyset_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, keyset_state() });
        it_anim = anim.find(id);
    }

    it_anim->second.text = ImLerp(it_anim->second.text,
        g.ActiveId == id ? c::text::text_active : hovered ? c::text::text_hov : c::text::text,
        ImGui::GetIO().DeltaTime * 6.f);

    it_anim->second.background = ImLerp(it_anim->second.background,
        g.ActiveId == id ? gui.main : gui.third,
        ImGui::GetIO().DeltaTime * 6.f);

    window->DrawList->AddRectFilled(rect.Min,
        rect.Max,
        GetColorU32(it_anim->second.background),
        c::elements::rounding);

    PushStyleColor(ImGuiCol_Text, func.ImColorToImVec4(gui.text[0]));
    RenderTextClipped(rect.Min, rect.Max, buf_display, NULL, &label_size, ImVec2(0.5f, 0.5f));
    PopStyleColor();

    if (hovered && io.MouseClicked[0]) {
        if (g.ActiveId != id) {
            // Clear any existing key states
            memset(io.MouseDown, 0, sizeof(io.MouseDown));
            memset(io.KeysDown, 0, sizeof(io.KeysDown));
            *key = 0;
        }
        ImGui::SetActiveID(id, window);
        ImGui::FocusWindow(window);
    }
    else if (io.MouseClicked[0]) {
        if (g.ActiveId == id)
            ImGui::ClearActiveID();
    }

    if (g.ActiveId == id) {
        // Handle mouse buttons
        for (auto i = 0; i < 5; i++) {
            if (io.MouseDown[i]) {
                switch (i) {
                case 0: k = VK_LBUTTON; break;
                case 1: k = VK_RBUTTON; break;
                case 2: k = VK_MBUTTON; break;
                case 3: k = VK_XBUTTON1; break;
                case 4: k = VK_XBUTTON2; break;
                }

                value_changed = true;
                ImGui::ClearActiveID();

                // Debug output
                std::cout << "Keyset: Selected mouse button " << i << " (VK: " << k << ")" << std::endl;
            }
        }

        // Handle keyboard keys using GetAsyncKeyState for better detection
        if (!value_changed) {
            // Check all possible virtual key codes
            for (int i = 0x01; i <= 0xFE; i++) {
                // Skip mouse buttons as they're handled above
                if (i == VK_LBUTTON || i == VK_RBUTTON || i == VK_MBUTTON ||
                    i == VK_XBUTTON1 || i == VK_XBUTTON2)
                    continue;

                // Check if key is pressed using GetAsyncKeyState
                // 0x8000 bit indicates key is currently down
                // 0x0001 bit indicates key was pressed after last check
                if ((GetAsyncKeyState(i) & 0x8001) != 0) {
                    k = i;
                    value_changed = true;
                    ImGui::ClearActiveID();

                    // Debug output with more detailed key information
                    std::cout << "Keyset: Selected key " << InputManager::GetKeyName(i) << " (VK: " << k << ")" << std::endl;
                    break;
                }
            }
        }

        // If Escape is pressed, set key to 0 (None)
        if (io.KeysDown[VK_ESCAPE]) {
            k = 0;
            value_changed = true;
            ImGui::ClearActiveID();

            // Debug output
            std::cout << "Keyset: Reset key to None" << std::endl;
        }

        // Update the key value
        if (value_changed) {
            *key = k;

            // Mark the item as edited to ensure changes are saved
            MarkItemEdited(id);

            // Print the selected key for debugging
            std::cout << "Keyset: Selected key value: " << k << " (" << InputManager::GetKeyName(k) << ")" << std::endl;

            // Get the feature name from the label
            std::string labelStr = label;

            // Print the raw label for debugging
            std::cout << "Raw label: '" << labelStr << "'" << std::endl;

            // Remove the "_keyset" suffix if present
            size_t keysetPos = labelStr.find("_keyset");
            if (keysetPos != std::string::npos) {
                labelStr = labelStr.substr(0, keysetPos);
                std::cout << "Removed _keyset suffix: '" << labelStr << "'" << std::endl;
            }

            // Clean up the label to get the feature name
            // Remove any trailing spaces
            while (!labelStr.empty() && labelStr.back() == ' ') {
                labelStr.pop_back();
            }

            // Remove any trailing numbers or special characters
            while (!labelStr.empty() && !isalpha(labelStr.back())) {
                labelStr.pop_back();
            }

            // Convert to the format used in HotkeySystem
            std::string featureName = "";

            // Map common UI labels to their corresponding feature names in HotkeySystem
            if (labelStr.find("Enable Aimbot") != std::string::npos ||
                labelStr.find("Aimbot") != std::string::npos ||
                labelStr == "Enable") {
                featureName = "AimbotEnable";
            } else if (labelStr.find("Aim Lock") != std::string::npos) {
                featureName = "AimbotAimLock";
            } else if (labelStr.find("Prediction") != std::string::npos) {
                featureName = "AimbotPrediction";
            } else if (labelStr.find("Save Target") != std::string::npos) {
                featureName = "AimbotSaveTarget";
            } else if (labelStr.find("Visibility Check") != std::string::npos) {
                featureName = "AimbotVisibilityCheck";
            } else if (labelStr.find("Humanized Smooth") != std::string::npos) {
                featureName = "AimbotHumanizedSmooth";
            } else if (labelStr.find("Ignore Downed") != std::string::npos) {
                featureName = "AimbotIgnoreDowned";
            } else if (labelStr.find("Player AI") != std::string::npos) {
                featureName = "AimbotPlayerAi";
            } else if (labelStr.find("Weapon Only") != std::string::npos) {
                featureName = "AimbotWeaponOnly";
            } else if (labelStr.find("Draw FOV") != std::string::npos) {
                featureName = "AimbotDrawFov";
            } else if (labelStr.find("Draw Crosshair") != std::string::npos) {
                featureName = "AimbotDrawCrossHair";
            } else if (labelStr.find("Draw Target") != std::string::npos) {
                featureName = "AimbotDrawTarget";
            } else if (labelStr.find("ESP") != std::string::npos ||
                       labelStr.find("Players") != std::string::npos) {
                featureName = "PlayerEspEnable";
            } else if (labelStr.find("Box") != std::string::npos) {
                featureName = "PlayerEspBox";
            } else if (labelStr.find("Skeleton") != std::string::npos) {
                featureName = "PlayerEspSkeleton";
            } else if (labelStr.find("Head Circle") != std::string::npos) {
                featureName = "PlayerEspHeadCircle";
            } else if (labelStr.find("Lines") != std::string::npos) {
                featureName = "PlayerEspLines";
            } else if (labelStr.find("Distance") != std::string::npos) {
                featureName = "PlayerEspDistance";
            } else if (labelStr.find("Nickname") != std::string::npos) {
                featureName = "PlayerEspNickName";
            } else if (labelStr.find("Platform") != std::string::npos) {
                featureName = "PlayerEspPlatform";
            } else if (labelStr.find("Kills") != std::string::npos) {
                featureName = "PlayerEspKills";
            } else if (labelStr.find("Level") != std::string::npos) {
                featureName = "PlayerEspLevel";
            } else if (labelStr.find("Rank") != std::string::npos) {
                featureName = "PlayerEspRank";
            } else if (labelStr.find("Items") != std::string::npos) {
                featureName = "ItemEspEnable";
            } else if (labelStr.find("Consumables") != std::string::npos) {
                featureName = "ItemConsumableEnable";
            } else if (labelStr.find("Weapons") != std::string::npos) {
                featureName = "ItemWeaponEnable";
            } else if (labelStr.find("Ammo") != std::string::npos) {
                featureName = "ItemAmmoEnable";
            } else if (labelStr.find("Other Items") != std::string::npos) {
                featureName = "ItemOtherEnable";
            } else if (labelStr.find("Radar") != std::string::npos) {
                featureName = "RadarEnable";
            }

            std::cout << "Original label: '" << label << "', Cleaned label: '" << labelStr << "', Feature name: '" << featureName << "'" << std::endl;

            // Try to update the hotkey in the HotkeySystem
            if (!featureName.empty()) {
                HotkeyBinding* binding = HotkeySystem::GetHotkey(featureName);
                if (binding != nullptr) {
                    // Get the current toggle mode
                    bool toggleMode = binding->toggleMode;
                    // Update the hotkey with the new key code but keep the same toggle mode
                    HotkeySystem::UpdateHotkey(featureName, k, toggleMode);
                    std::cout << "Updated hotkey for " << featureName << " to " << k << " (" << InputManager::GetKeyName(k) << ")" << std::endl;

                    // Verify the mode was updated correctly
                    bool keysStructMode = HotkeySystem::GetModeFromKeysStruct(featureName);
                    bool settingsStructMode = HotkeySystem::GetModeFromSettingsStruct(featureName);

                    std::cout << "After update - Binding mode: " << (binding->toggleMode ? "Toggle" : "Hold") << std::endl;
                    std::cout << "After update - Keys struct mode: " << (keysStructMode ? "Toggle" : "Hold") << std::endl;
                    std::cout << "After update - Settings struct mode: " << (settingsStructMode ? "Toggle" : "Hold") << std::endl;

                    // If there's a mismatch, force another update
                    if (binding->toggleMode != toggleMode || keysStructMode != toggleMode || settingsStructMode != toggleMode) {
                        std::cout << "MODE MISMATCH DETECTED! Forcing another update..." << std::endl;

                        // Force update the Keys struct field directly
                        HotkeySystem::UpdateKeysModeField(featureName, toggleMode);

                        // Update the binding
                        binding->toggleMode = toggleMode;

                        // Update Settings struct
                        Settings.Keys.Keys = Keys;

                        // Force save settings
                        SettingsHelper::SaveSettings();

                        // Verify mode consistency
                        HotkeySystem::VerifyModeConsistency();

                        std::cout << "Forced direct update complete. Mode is now " << (toggleMode ? "Toggle" : "Hold") << std::endl;
                    }

                    // Also update the Keys struct directly
                    if (featureName == "AimbotEnable") {
                        Keys.AimbotEnable = k;
                        std::cout << "Directly set Keys.AimbotEnable to " << k << std::endl;

                        // Make sure the key is monitored
                        InputManager::MonitorKey(k);

                        // Force update the feature state
                        if (toggleMode) {
                            // Toggle mode - keep current state
                        } else {
                            // Hold mode - set to false initially unless key is down
                            if (!InputManager::IsKeyDown(k)) {
                                Aimbot.Enable = false;
                                std::cout << "Forced update Aimbot.Enable to OFF after key change" << std::endl;
                            }
                        }
                    } else if (featureName == "AimbotAimLock") {
                        Keys.AimbotAimLock = k;
                        std::cout << "Directly set Keys.AimbotAimLock to " << k << std::endl;

                        // Make sure the key is monitored
                        InputManager::MonitorKey(k);

                        // Force update the feature state
                        if (toggleMode) {
                            // Toggle mode - keep current state
                        } else {
                            // Hold mode - set to false initially unless key is down
                            if (!InputManager::IsKeyDown(k)) {
                                Aimbot.AimLock = false;
                                std::cout << "Forced update Aimbot.AimLock to OFF after key change" << std::endl;
                            }
                        }
                    } else if (featureName == "PlayerEspEnable") {
                        Keys.PlayerEspEnable = k;
                        std::cout << "Directly set Keys.PlayerEspEnable to " << k << std::endl;

                        // Make sure the key is monitored
                        InputManager::MonitorKey(k);

                        // Force update the feature state
                        if (toggleMode) {
                            // Toggle mode - keep current state
                        } else {
                            // Hold mode - set to false initially unless key is down
                            if (!InputManager::IsKeyDown(k)) {
                                Players.Enable = false;
                                std::cout << "Forced update Players.Enable to OFF after key change" << std::endl;
                            }
                        }
                    }

                    // Force sync with Keys struct
                    HotkeySystem::SyncWithKeysStruct();

                    // Reinitialize the HotkeySystem to ensure all bindings are updated
                    HotkeySystem::Initialize(true);

                    // Save settings to ensure the changes are persisted
                    if (SettingsHelper::SaveSettings()) {
                        std::cout << "Settings saved successfully after key selection" << std::endl;
                    } else {
                        std::cout << "Failed to save settings after key selection" << std::endl;
                    }
                } else {
                    std::cout << "No binding found for feature: " << featureName << std::endl;
                }
            } else {
                std::cout << "Could not map label to a feature name: " << labelStr << std::endl;
            }

            // Force save settings to ensure the key change is persisted
            try {
                if (SettingsHelper::SaveSettings()) {
                    std::cout << "Settings saved successfully after key selection" << std::endl;
                } else {
                    std::cout << "Failed to save settings after key selection" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "Exception while saving settings: " << e.what() << std::endl;
            }
        }
    }

    return value_changed;
}

bool nav_elements::Keybind(const char* label, const char* description, int* key, bool* mode) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    ImGuiIO& io = g.IO;
    const ImGuiStyle& style = g.Style;

    const ImGuiID id = window->GetID(label);
    const float width = (GetContentRegionMax().x - style.WindowPadding.x);

    const ImVec2 pos = window->DC.CursorPos;

    const float w = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;

    const ImRect total_bb(pos, pos + ImVec2(!window->DC.IsSameLine ? w : 60.f, 60.f));

    const ImRect rect(total_bb.Min, total_bb.Max);

    ItemSize(ImRect(rect.Min, rect.Max));
    if (!ImGui::ItemAdd(rect, id)) return false;

    char buf_display[64] = "None";

    //PushFont(fonts["regular"][3]);
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    //PopFont();

    const ImVec2 label_size = CalcTextSize(label, NULL, true);

    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2);

    bool value_changed = false;
    int k = *key;


    if (*key != 0 && g.ActiveId != id) {
        strcpy_s(buf_display, InputManager::GetKeyName(*key).c_str());
    }

    ImRect clickable(ImVec2(rect.Max.x - 30 - CalcTextSize(buf_display).x, rect.Min.y), rect.Max);
    bool hovered, held, pressed = ButtonBehavior(total_bb, id, &hovered, &held);

    static std::map<ImGuiID, key_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, key_state() });
        it_anim = anim.find(id);
    }

    it_anim->second.text = ImLerp(it_anim->second.text,
        hovered ? gui.main : gui.text[1],
        ImGui::GetIO().DeltaTime * 6.f);


    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color, held || hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f), GetAnimSpeed());

    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color, held || hovered ? func.GetDarkColor(gui.main) : gui.main, GetAnimSpeed());

    //RenderBackgroundElemets(total_bb.Min, total_bb.Max, window->DrawList);
    const float anim_speed = GetAnimSpeed();

    it_anim->second.background_color = ImLerp(
        it_anim->second.background_color,
        held || hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
        anim_speed
    );

    //RenderBackgroundElemets(total_bb.Min, total_bb.Max, window->DrawList);
    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max, GetColorU32(it_anim->second.background_color), 3.0f);

    if (style.GrabMinSize != 70.f) {
        GetWindowDrawList()->AddRectFilled(total_bb.Min,
            total_bb.Min + ImVec2(total_bb.GetSize().y, +total_bb.GetSize().y),
            GetColorU32(it_anim->second.rect_color),
            style.FrameRounding,
            ImDrawFlags_RoundCornersLeft);


        window->DrawList->AddShadowCircle(total_bb.Min + ImVec2(total_bb.GetSize().y / 2, total_bb.GetSize().y / 2),
            9.f,
            GetColorU32(it_anim->second.icon_color),
            40,
            ImVec2(0, 0),
            0,
            360);

        GetWindowDrawList()->AddText(label_pos, gui.text[0], label);

        //PushFont(fonts["regular"][3]);
        GetWindowDrawList()->AddText(label_pos + ImVec2(0.f, label_size.y),
            gui.text[1],
            getStringBeforeCaret(description) != nullptr ? getStringBeforeCaret(description)
            : description);
        //PopFont();


       /* if (findSymbolAfterCaret(description) != nullptr) {
            PushFont(fonts["semibold"][6]);
            GetWindowDrawList()->AddText(func.CalcTextPos(total_bb.Min,
                total_bb.Min + ImVec2(total_bb.GetSize().y, total_bb.GetSize().y),
                findSymbolAfterCaret(description),
                fonts["semibold"][6]),
                GetColorU32(it_anim->second.icon_color),
                findSymbolAfterCaret(description));
            PopFont();
        }*/
    }


    std::string label_str = label;
    std::string popup_str = label_str + "_##popup";

    if (pressed)
    {
        OpenPopup(popup_str.c_str());
    }

    PushStyleColor(ImGuiCol_WindowBg, func.ImColorToImVec4(gui.title));
    PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.title));
    PushStyleColor(ImGuiCol_Border, func.ImColorToImVec4(gui.stroke));

    PushStyleVar(ImGuiStyleVar_WindowRounding, style.FrameRounding);
    PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
    PushStyleVar(ImGuiStyleVar_WindowBorderSize, 1.f);

    ImGuiWindowFlags window_flags =
        ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoFocusOnAppearing;

    PushStyleVar(ImGuiStyleVar_Alpha, it_anim->second.alpha);

    if (BeginPopup(popup_str.c_str(), window_flags))
    {
        it_anim->second.alpha = ImLerp(it_anim->second.alpha, it_anim->second.want_close ? 0.f : 1.f, GetAnimSpeed() / 2);

        gui.keybind_window = ImGui::GetCurrentWindow();
        ImGui::PushClipRect(ImGui::GetWindowPos() - ImVec2(15, 0), ImGui::GetWindowPos() + ImGui::GetWindowSize() + ImVec2(15, 0), false);

        //blur::add_blur(ImGui::GetWindowDrawList(), ImGui::GetWindowPos(), ImGui::GetWindowPos() + ImGui::GetWindowSize(), it_anim->second.alpha);
        ImGui::GetWindowDrawList()->AddRectFilled(ImGui::GetWindowPos(), ImGui::GetWindowPos() + ImGui::GetWindowSize(), func.GetColorWithAlpha(gui.background, it_anim->second.alpha - 0.2f), style.FrameRounding);
        ImGui::GetWindowDrawList()->AddRect(ImGui::GetWindowPos(), ImGui::GetWindowPos() + ImGui::GetWindowSize(), func.GetColorWithAlpha(gui.stroke, it_anim->second.alpha / 15.f), style.FrameRounding);

        PopClipRect();


        ImGui::Text(label);

        Keyset(label, key);

        nav_elements::ToggleButton("Hold", "Toggle", mode, ImVec2(150, 30));

        PushFont(iconsBig);

        if (nav_elements::Button(ICON_DELETE_2_LINE "Reset", ImVec2(150, 30), ImColor(255, 88, 88))) {
            *key = 0;

            // Get the feature name from the label
            std::string labelStr = label;

            // Print the raw label for debugging
            std::cout << "Reset - Raw label: '" << labelStr << "'" << std::endl;

            // Remove the "_keyset" suffix if present
            size_t keysetPos = labelStr.find("_keyset");
            if (keysetPos != std::string::npos) {
                labelStr = labelStr.substr(0, keysetPos);
                std::cout << "Reset - Removed _keyset suffix: '" << labelStr << "'" << std::endl;
            }

            // Clean up the label to get the feature name
            // Remove any trailing spaces
            while (!labelStr.empty() && labelStr.back() == ' ') {
                labelStr.pop_back();
            }

            // Remove any trailing numbers or special characters
            while (!labelStr.empty() && !isalpha(labelStr.back())) {
                labelStr.pop_back();
            }

            // Convert to the format used in HotkeySystem
            std::string featureName = "";

            // Map common UI labels to their corresponding feature names in HotkeySystem
            if (labelStr.find("Enable Aimbot") != std::string::npos ||
                labelStr.find("Aimbot") != std::string::npos ||
                labelStr == "Enable") {
                featureName = "AimbotEnable";
            } else if (labelStr.find("Aim Lock") != std::string::npos) {
                featureName = "AimbotAimLock";
            } else if (labelStr.find("Prediction") != std::string::npos) {
                featureName = "AimbotPrediction";
            } else if (labelStr.find("Save Target") != std::string::npos) {
                featureName = "AimbotSaveTarget";
            } else if (labelStr.find("Visibility Check") != std::string::npos) {
                featureName = "AimbotVisibilityCheck";
            } else if (labelStr.find("Humanized Smooth") != std::string::npos) {
                featureName = "AimbotHumanizedSmooth";
            } else if (labelStr.find("Ignore Downed") != std::string::npos) {
                featureName = "AimbotIgnoreDowned";
            } else if (labelStr.find("Player AI") != std::string::npos) {
                featureName = "AimbotPlayerAi";
            } else if (labelStr.find("Weapon Only") != std::string::npos) {
                featureName = "AimbotWeaponOnly";
            } else if (labelStr.find("Draw FOV") != std::string::npos) {
                featureName = "AimbotDrawFov";
            } else if (labelStr.find("Draw Crosshair") != std::string::npos) {
                featureName = "AimbotDrawCrossHair";
            } else if (labelStr.find("Draw Target") != std::string::npos) {
                featureName = "AimbotDrawTarget";
            } else if (labelStr.find("ESP") != std::string::npos ||
                       labelStr.find("Players") != std::string::npos) {
                featureName = "PlayerEspEnable";
            } else if (labelStr.find("Box") != std::string::npos) {
                featureName = "PlayerEspBox";
            } else if (labelStr.find("Skeleton") != std::string::npos) {
                featureName = "PlayerEspSkeleton";
            } else if (labelStr.find("Head Circle") != std::string::npos) {
                featureName = "PlayerEspHeadCircle";
            } else if (labelStr.find("Lines") != std::string::npos) {
                featureName = "PlayerEspLines";
            } else if (labelStr.find("Distance") != std::string::npos) {
                featureName = "PlayerEspDistance";
            } else if (labelStr.find("Nickname") != std::string::npos) {
                featureName = "PlayerEspNickName";
            } else if (labelStr.find("Platform") != std::string::npos) {
                featureName = "PlayerEspPlatform";
            } else if (labelStr.find("Kills") != std::string::npos) {
                featureName = "PlayerEspKills";
            } else if (labelStr.find("Level") != std::string::npos) {
                featureName = "PlayerEspLevel";
            } else if (labelStr.find("Rank") != std::string::npos) {
                featureName = "PlayerEspRank";
            } else if (labelStr.find("Items") != std::string::npos) {
                featureName = "ItemEspEnable";
            } else if (labelStr.find("Consumables") != std::string::npos) {
                featureName = "ItemConsumableEnable";
            } else if (labelStr.find("Weapons") != std::string::npos) {
                featureName = "ItemWeaponEnable";
            } else if (labelStr.find("Ammo") != std::string::npos) {
                featureName = "ItemAmmoEnable";
            } else if (labelStr.find("Other Items") != std::string::npos) {
                featureName = "ItemOtherEnable";
            } else if (labelStr.find("Radar") != std::string::npos) {
                featureName = "RadarEnable";
            }

            std::cout << "Reset hotkey - Original label: '" << label << "', Cleaned label: '" << labelStr << "', Feature name: '" << featureName << "'" << std::endl;

            // Try to update the hotkey in the HotkeySystem
            if (!featureName.empty()) {
                HotkeyBinding* binding = HotkeySystem::GetHotkey(featureName);
                if (binding != nullptr) {
                    // Get the current toggle mode
                    bool toggleMode = binding->toggleMode;
                    // Update the hotkey with the new key code but keep the same toggle mode
                    HotkeySystem::UpdateHotkey(featureName, 0, toggleMode);
                    std::cout << "Reset hotkey for " << featureName << " to 0 (None)" << std::endl;

                    // Verify the mode was updated correctly
                    bool keysStructMode = HotkeySystem::GetModeFromKeysStruct(featureName);
                    bool settingsStructMode = HotkeySystem::GetModeFromSettingsStruct(featureName);

                    std::cout << "After reset - Binding mode: " << (binding->toggleMode ? "Toggle" : "Hold") << std::endl;
                    std::cout << "After reset - Keys struct mode: " << (keysStructMode ? "Toggle" : "Hold") << std::endl;
                    std::cout << "After reset - Settings struct mode: " << (settingsStructMode ? "Toggle" : "Hold") << std::endl;

                    // If there's a mismatch, force another update
                    if (binding->toggleMode != toggleMode || keysStructMode != toggleMode || settingsStructMode != toggleMode) {
                        std::cout << "MODE MISMATCH DETECTED during reset! Forcing another update..." << std::endl;

                        // Force update the Keys struct field directly
                        HotkeySystem::UpdateKeysModeField(featureName, toggleMode);

                        // Update the binding
                        binding->toggleMode = toggleMode;

                        // Update Settings struct
                        Settings.Keys.Keys = Keys;

                        // Force save settings
                        SettingsHelper::SaveSettings();

                        // Verify mode consistency
                        HotkeySystem::VerifyModeConsistency();

                        std::cout << "Forced direct update complete. Mode is now " << (toggleMode ? "Toggle" : "Hold") << std::endl;
                    }

                    // Also update the Keys struct directly
                    if (featureName == "AimbotEnable") {
                        Keys.AimbotEnable = 0;
                        std::cout << "Directly reset Keys.AimbotEnable to 0" << std::endl;

                        // Force update the feature state
                        if (toggleMode) {
                            // Toggle mode - keep current state
                        } else {
                            // Hold mode - set to false initially
                            Aimbot.Enable = false;
                            std::cout << "Forced update Aimbot.Enable to OFF after reset" << std::endl;
                        }
                    } else if (featureName == "AimbotAimLock") {
                        Keys.AimbotAimLock = 0;
                        std::cout << "Directly reset Keys.AimbotAimLock to 0" << std::endl;

                        // Force update the feature state
                        if (toggleMode) {
                            // Toggle mode - keep current state
                        } else {
                            // Hold mode - set to false initially
                            Aimbot.AimLock = false;
                            std::cout << "Forced update Aimbot.AimLock to OFF after reset" << std::endl;
                        }
                    } else if (featureName == "PlayerEspEnable") {
                        Keys.PlayerEspEnable = 0;
                        std::cout << "Directly reset Keys.PlayerEspEnable to 0" << std::endl;

                        // Force update the feature state
                        if (toggleMode) {
                            // Toggle mode - keep current state
                        } else {
                            // Hold mode - set to false initially
                            Players.Enable = false;
                            std::cout << "Forced update Players.Enable to OFF after reset" << std::endl;
                        }
                    }

                    // Force sync with Keys struct
                    HotkeySystem::SyncWithKeysStruct();

                    // Reinitialize the HotkeySystem to ensure all bindings are updated
                    HotkeySystem::Initialize(true);

                    // Save settings to ensure the changes are persisted
                    if (SettingsHelper::SaveSettings()) {
                        std::cout << "Settings saved successfully after reset" << std::endl;
                    } else {
                        std::cout << "Failed to save settings after reset" << std::endl;
                    }
                }
            }

            // Force save settings to ensure the reset is persisted
            try {
                // Then save settings to ensure the reset is persisted
                if (SettingsHelper::SaveSettings()) {
                    std::cout << "Settings saved successfully after resetting hotkey" << std::endl;
                } else {
                    std::cout << "Failed to save settings after resetting hotkey" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "Exception while saving settings: " << e.what() << std::endl;
            }
        }

        if (nav_elements::Button(ICON_SAVE_2_LINE "Save", ImVec2(150, 30))) {
            it_anim->second.want_close = true;

            // Force save settings to ensure all changes are persisted
            try {

                // Then save settings to ensure all changes are persisted
                if (SettingsHelper::SaveSettings()) {
                    std::cout << "Settings saved successfully after clicking Save button" << std::endl;
                } else {
                    std::cout << "Failed to save settings after clicking Save button" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "Exception while saving settings: " << e.what() << std::endl;
            }
        }

        if (IsKeyPressed(ImGuiKey_Escape))
            it_anim->second.want_close = true;

        PopFont();


        if (it_anim->second.want_close && it_anim->second.alpha < 0.02f) {
            CloseCurrentPopup();
            it_anim->second.want_close = false;
        }

        ImGui::EndPopup();
    }

    PopStyleVar(4);
    PopStyleColor(3);

    /*
    PushStyleColor(ImGuiCol_Text, c::text::text);
    PushFont(font::icomoon_page);
    RenderTextClipped(clickable.Min - ImVec2(30, 0), clickable.Max, "j", NULL, NULL, ImVec2(0.0f, 0.5f));
    PopFont();
    PopStyleColor();
    */

    if (style.GrabMinSize != 70.f) {
        PushStyleColor(ImGuiCol_Text, it_anim->second.text);
        RenderTextClipped(clickable.Min, clickable.Max, buf_display, NULL, &label_size, ImVec2(0.5f, 0.5f));
        PopStyleColor();
    }
    else
    {
        ImVec2 icon_size = CalcTextSize(ICON_KEYBOARD_LINE);

        window->DrawList->AddText(total_bb.GetCenter() - icon_size / 2, GetColorU32(it_anim->second.text), ICON_KEYBOARD_LINE);
        window->DrawList->AddShadowCircle(total_bb.GetCenter(), 7.f, GetColorU32(it_anim->second.text), 45.f, ImVec2(0, 0));
    }

    return value_changed;
}

void nav_elements::Pickerbox(std::string label, const char* description, bool* v, float col[4])
{
    std::string picker_name = "##picker" + label;

    PushStyleVar(ImGuiStyleVar_GrabMinSize, 70);
    nav_elements::Checkbox(label.c_str(), v, description);

    ImGui::SameLine();

    nav_elements::ColorEdit4Ex(picker_name.c_str(), "123", (float*)col, ImGuiColorEditFlags_NoSidePreview | ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaPreview);
    PopStyleVar();
}

void nav_elements::Keybox(std::string label, const char* description, bool* v, int* key, bool* mode)
{
    std::string keybind_name = label + " ";

    PushStyleVar(ImGuiStyleVar_GrabMinSize, 70);
    nav_elements::Checkbox(label.c_str(), v, description);

    ImGui::SameLine();

    nav_elements::Keybind(keybind_name.c_str(), "123", key, mode);
    PopStyleVar();
}


static void ColorEditRestoreH(const float* col, float* H) {
    ImGuiContext& g = *GImGui;
    IM_ASSERT(g.ColorEditCurrentID != 0);
    if (g.ColorEditSavedID != g.ColorEditCurrentID ||
        g.ColorEditSavedColor != ImGui::ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], 0)))
        return;
    *H = g.ColorEditSavedHue;
}

static void ColorEditRestoreHS(const float* col, float* H, float* S, float* V) {
    ImGuiContext& g = *GImGui;
    IM_ASSERT(g.ColorEditCurrentID != 0);
    if (g.ColorEditSavedID != g.ColorEditCurrentID ||
        g.ColorEditSavedColor != ImGui::ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], 0)))
        return;

    if (*S == 0.0f || (*H == 0.0f && g.ColorEditSavedHue == 1))
        *H = g.ColorEditSavedHue;

    if (*V == 0.0f) *S = g.ColorEditSavedSat;
}


struct edit_state {
    ImVec4 text;
    float alpha = 0.f, alpha_search;
    bool hovered, colors_hovered, active = false;
    ImVec2 picker_offset{ 0, 200.f };
};


bool nav_elements::ColorEdit4Ex(const char* label, const char* description, float col[4], ImGuiColorEditFlags flags) {
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const float square_sz = 18.f;
    const float w_full = 60.f;
    const float w_button = (flags & ImGuiColorEditFlags_NoSmallPreview) ? 0.0f : square_sz;
    const float w_inputs = w_full - w_button;
    const char* label_display_end = FindRenderedTextEnd(label);
    g.NextItemData.ClearFlags();

    char buf[64];
    static bool search_col = false;

    BeginGroup();
    PushID(label);
    const bool set_current_color_edit_id = (g.ColorEditCurrentID == 0);
    if (set_current_color_edit_id) g.ColorEditCurrentID = window->IDStack.back();

    const ImGuiColorEditFlags flags_untouched = flags;

    if (flags & ImGuiColorEditFlags_NoInputs)
        flags = (flags & (~ImGuiColorEditFlags_DisplayMask_)) | ImGuiColorEditFlags_DisplayRGB |
        ImGuiColorEditFlags_NoOptions;

    if (!(flags & ImGuiColorEditFlags_NoOptions)) ColorEditOptionsPopup(col, flags);

    if (!(flags & ImGuiColorEditFlags_DisplayMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_DisplayMask_);
    if (!(flags & ImGuiColorEditFlags_DataTypeMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_DataTypeMask_);
    if (!(flags & ImGuiColorEditFlags_PickerMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_PickerMask_);
    if (!(flags & ImGuiColorEditFlags_InputMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_InputMask_);
    flags |= (g.ColorEditOptions &
        ~(ImGuiColorEditFlags_DisplayMask_ | ImGuiColorEditFlags_DataTypeMask_ | ImGuiColorEditFlags_PickerMask_ |
            ImGuiColorEditFlags_InputMask_));
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_DisplayMask_));
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_InputMask_));

    const bool alpha = (flags & ImGuiColorEditFlags_NoAlpha) == 0;
    const bool hdr = (flags & ImGuiColorEditFlags_HDR) != 0;
    const int components = alpha ? 4 : 3;

    float f[4] = { col[0], col[1], col[2], alpha ? col[3] : 1.0f };
    if ((flags & ImGuiColorEditFlags_InputHSV) && (flags & ImGuiColorEditFlags_DisplayRGB))
        ColorConvertHSVtoRGB(f[0], f[1], f[2], f[0], f[1], f[2]);
    else if ((flags & ImGuiColorEditFlags_InputRGB) && (flags & ImGuiColorEditFlags_DisplayHSV)) {
        ColorConvertRGBtoHSV(f[0], f[1], f[2], f[0], f[1], f[2]);
        ColorEditRestoreHS(col, &f[0], &f[1], &f[2]);
    }
    int i[4] = { IM_F32_TO_INT8_UNBOUND(f[0]), IM_F32_TO_INT8_UNBOUND(f[1]), IM_F32_TO_INT8_UNBOUND(f[2]),
                IM_F32_TO_INT8_UNBOUND(f[3]) };

    bool value_changed = false;
    bool value_changed_as_float = false;

    const ImVec2 pos = window->DC.CursorPos;
    const float inputs_offset_x = (style.ColorButtonPosition == ImGuiDir_Left) ? w_button : 0.0f;
    window->DC.CursorPos.x = pos.x + inputs_offset_x;

    if ((flags & (ImGuiColorEditFlags_DisplayRGB | ImGuiColorEditFlags_DisplayHSV)) != 0 &&
        (flags & ImGuiColorEditFlags_NoInputs) == 0) {
        const float w_item_one = ImMax(1.0f,
            IM_FLOOR((w_inputs - (style.ItemInnerSpacing.x) * (components - 1)) /
                (float)components));
        const float w_item_last = ImMax(1.0f,
            IM_FLOOR(
                w_inputs - (w_item_one + style.ItemInnerSpacing.x) * (components - 1)));

        const bool hide_prefix = (w_item_one <=
            CalcTextSize((flags & ImGuiColorEditFlags_Float) ? "M:0.000" : "M:000").x);
        static const char* ids[4] = { "##X", "##Y", "##Z", "##W" };
        static const char* fmt_table_int[3][4] =
        {
            {"%3d", "%3d", "%3d", "%3d"},
            {"R:%3d", "G:%3d", "B:%3d", "A:%3d"},
            {"H:%3d", "S:%3d", "V:%3d", "A:%3d"}
        };

        static const char* fmt_table_float[3][4] =
        {
            {"%0.3f", "%0.3f", "%0.3f", "%0.3f"},
            {"R:%0.3f", "G:%0.3f", "B:%0.3f", "A:%0.3f"},
            {"H:%0.3f", "S:%0.3f", "V:%0.3f", "A:%0.3f"}
        };

        const int fmt_idx = hide_prefix ? 0 : (flags & ImGuiColorEditFlags_DisplayHSV) ? 2 : 1;

        for (int n = 0; n < components; n++) {
            if (n > 0) SameLine(0, style.ItemInnerSpacing.x);
            SetNextItemWidth((n + 1 < components) ? w_item_one : w_item_last);

            if (flags & ImGuiColorEditFlags_Float) {
                value_changed |= DragFloat(ids[n],
                    &f[n],
                    1.0f / 255.0f,
                    0.0f,
                    hdr ? 0.0f : 1.0f,
                    fmt_table_float[fmt_idx][n]);
                value_changed_as_float |= value_changed;
            }
            else {
                value_changed |= DragInt(ids[n], &i[n], 1.0f, 0, hdr ? 0 : 255, fmt_table_int[fmt_idx][n]);
            }
            if (!(flags & ImGuiColorEditFlags_NoOptions))
                OpenPopupOnItemClick("context",
                    ImGuiPopupFlags_MouseButtonRight);
        }
    }

    static std::map<ImGuiID, edit_state> anim;
    edit_state& state = anim[ImGui::GetID(label)];

    ImGuiWindow* picker_active_window = NULL;
    if (!(flags & ImGuiColorEditFlags_NoSmallPreview)) {
        const float button_offset_x = ((flags & ImGuiColorEditFlags_NoInputs) ||
            (style.ColorButtonPosition == ImGuiDir_Left)) ? 0.0f : w_inputs +
            style.ItemInnerSpacing.x;
        window->DC.CursorPos = ImVec2(pos.x + button_offset_x, pos.y);

        const ImVec4 col_v4(col[0], col[1], col[2], alpha ? col[3] : 1.0f);

        // Directly open the popup when the ColorButton is clicked
        if (nav_elements::ColorButton("##ColorButton", col_v4, flags, ImVec2(20, 20))) {
            if (!(flags & ImGuiColorEditFlags_NoPicker)) {
                state.active = true;
                // Only set picker_active when actually opening the popup, not on hover
                gui.picker_active = true;
                g.ColorPickerRef = col_v4;
                OpenPopup("color_picker");
                SetNextWindowPos(g.LastItemData.Rect.GetBL() + ImVec2(0.0f, style.ItemSpacing.y));
            }
        }

        if (!(flags & ImGuiColorEditFlags_NoOptions)) OpenPopupOnItemClick("context", ImGuiPopupFlags_MouseButtonRight);

        state.alpha_search = ImLerp(state.alpha_search, search_col ? 1.f : 0.f, g.IO.DeltaTime * 6.f);

        PushStyleColor(ImGuiCol_WindowBg, func.ImColorToImVec4(gui.window_bg));
        PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
        PushStyleColor(ImGuiCol_Border, func.ImColorToImVec4(gui.stroke));

        PushStyleVar(ImGuiStyleVar_WindowRounding, style.FrameRounding);
        PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
        PushStyleVar(ImGuiStyleVar_WindowBorderSize, 1.f);

        POINT cursorPos;
        COLORREF color;


        std::string label_str = label;

        if (state.active && gui.picker_active)
            EasingAnimationV2("picker_state" + label_str,
                &state.picker_offset,
                ImVec2(0.f, 0.f),
                0.01f,
                imanim::EasingCurve::Type::OutInBack,
                -1);
        else
            EasingAnimationV2("picker_state" + label_str,
                &state.picker_offset,
                ImVec2(0.f, 0.f),
                0.6f,
                imanim::EasingCurve::Type::OutInQuad,
                -1);

        SetNextWindowPos(gui.window_pos + gui.window_size / 2 - ImVec2(128.5, 126) + state.picker_offset);

        if (BeginPopup("color_picker",
            ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_AlwaysAutoResize /*|
            ImGuiWindowFlags_NoFocusOnAppearing */ | ImGuiWindowFlags_NoNavFocus)) {
            state.hovered = ImGui::IsMouseHoveringRect(ImGui::GetWindowPos(),
                ImGui::GetWindowPos() + ImGui::GetWindowSize());

            if (state.hovered)
                ImGui::SetWindowFocus();

            if (!state.colors_hovered && !state.hovered && ImGui::IsMouseClicked(ImGuiMouseButton_Left) || ImGui::IsKeyPressed(ImGuiKey_Escape)) {
                state.active = false;
                CloseCurrentPopup();
                gui.picker_active = false;
            }

            state.active = true;

            gui.picker_window = ImGui::GetCurrentWindow();

            ImVec4 col_v4(col[0], col[1], col[2], (flags & ImGuiColorEditFlags_NoAlpha) ? 1.0f : col[3]);

            GetCursorPos(&cursorPos);
            HDC hdc = GetDC(NULL);
            color = GetPixel(hdc, cursorPos.x, cursorPos.y);

            if (search_col) {

                static DWORD dwTickStart = GetTickCount();
                if (GetTickCount() - dwTickStart > 150) {
                    col[0] = GetRValue(color) / 255.f;
                    col[1] = GetGValue(color) / 255.f;
                    col[2] = GetBValue(color) / 255.f;
                    dwTickStart = GetTickCount();

                    if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) search_col = false;

                }
            }

            if (alpha)
                ImFormatString(buf,
                    IM_ARRAYSIZE(buf),
                    "#%02X%02X%02X%02X",
                    ImClamp(i[0], 0, 255),
                    ImClamp(i[1], 0, 255),
                    ImClamp(i[2], 0, 255),
                    ImClamp(i[3], 0, 255));
            else
                ImFormatString(buf,
                    IM_ARRAYSIZE(buf),
                    "#%02X%02X%02X",
                    ImClamp(i[0], 0, 255),
                    ImClamp(i[1], 0, 255),
                    ImClamp(i[2], 0, 255));

            picker_active_window = g.CurrentWindow;
            ImGuiColorEditFlags picker_flags_to_forward =
                ImGuiColorEditFlags_DataTypeMask_ | ImGuiColorEditFlags_PickerMask_ | ImGuiColorEditFlags_InputMask_ |
                ImGuiColorEditFlags_HDR | ImGuiColorEditFlags_NoAlpha | ImGuiColorEditFlags_AlphaBar;
            ImGuiColorEditFlags picker_flags =
                (flags_untouched & picker_flags_to_forward) | ImGuiColorEditFlags_DisplayMask_ |
                ImGuiColorEditFlags_NoLabel | ImGuiColorEditFlags_AlphaPreviewHalf;

            SetNextItemWidth(square_sz * 11.5f);
            value_changed |= ColorPicker4("##picker", col, picker_flags, &g.ColorPickerRef.x);

            PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(0, 2));
            if (InputTextEx("v",
                "HEX COLOR",
                buf,
                IM_ARRAYSIZE(buf),
                ImVec2(233, 35),
                ImGuiInputTextFlags_CharsHexadecimal | ImGuiInputTextFlags_CharsUppercase)) {
                value_changed = true;
                char* p = buf;
                while (*p == '#' || ImCharIsBlankA(*p)) p++;
                i[0] = i[1] = i[2] = 0;
                i[3] = 0xFF;
                int r;
                if (alpha)
                    r = sscanf(p,
                        "%02X%02X%02X%02X",
                        (unsigned int*)&i[0],
                        (unsigned int*)&i[1],
                        (unsigned int*)&i[2],
                        (unsigned int*)&i[3]); // Treat at unsigned (%X is unsigned)
                else
                    r = sscanf(p,
                        "%02X%02X%02X",
                        (unsigned int*)&i[0],
                        (unsigned int*)&i[1],
                        (unsigned int*)&i[2]);
                IM_UNUSED(r);
            }
            PopStyleVar();
            EndPopup();
        }
    }
    PopStyleColor(3);
    PopStyleVar(3);

    PopID();
    EndGroup();

    return value_changed;
}

static void RenderArrowsForVerticalBar(ImDrawList* draw_list, ImVec2 pos, ImVec2 half_sz, float bar_w, float alpha) {
    ImU32 alpha8 = IM_F32_TO_INT8_SAT(alpha);
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + half_sz.x + 1, pos.y),
        ImVec2(half_sz.x + 2, half_sz.y + 1),
        ImGuiDir_Right,
        IM_COL32(0, 0, 0, alpha8));
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + half_sz.x, pos.y),
        half_sz,
        ImGuiDir_Right,
        IM_COL32(255, 255, 255, alpha8));
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + bar_w - half_sz.x - 1, pos.y),
        ImVec2(half_sz.x + 2, half_sz.y + 1),
        ImGuiDir_Left,
        IM_COL32(0, 0, 0, alpha8));
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + bar_w - half_sz.x, pos.y),
        half_sz,
        ImGuiDir_Left,
        IM_COL32(255, 255, 255, alpha8));
}

struct picker_state {
    float hue_bar;
    float alpha_bar;
    float circle, color_alpha;
    ImVec2 circle_move;
};

// Global state for CheckboxComponent
struct CheckboxComponentState {
    ImGuiID color_edit_id = 0;
    int color_edit_index = -1;
} g_CheckboxComponentState;

bool nav_elements::ColorPicker4(const char* label, float col[4], ImGuiColorEditFlags flags, const float* ref_col) {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImDrawList* draw_list = window->DrawList;
    ImGuiStyle& style = g.Style;
    ImGuiIO& io = g.IO;

    const float width = CalcItemWidth();
    g.NextItemData.ClearFlags();

    PushID(label);
    const bool set_current_color_edit_id = (g.ColorEditCurrentID == 0);
    if (set_current_color_edit_id)
        g.ColorEditCurrentID = window->IDStack.back();
    BeginGroup();

    if (!(flags & ImGuiColorEditFlags_NoSidePreview)) flags |= ImGuiColorEditFlags_NoSmallPreview;

    if (!(flags & ImGuiColorEditFlags_NoOptions)) ColorPickerOptionsPopup(col, flags);

    if (!(flags & ImGuiColorEditFlags_PickerMask_))
        flags |= ((g.ColorEditOptions & ImGuiColorEditFlags_PickerMask_) ? g.ColorEditOptions
            : ImGuiColorEditFlags_DefaultOptions_) &
        ImGuiColorEditFlags_PickerMask_;
    if (!(flags & ImGuiColorEditFlags_InputMask_))
        flags |= ((g.ColorEditOptions & ImGuiColorEditFlags_InputMask_) ? g.ColorEditOptions
            : ImGuiColorEditFlags_DefaultOptions_) &
        ImGuiColorEditFlags_InputMask_;
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_PickerMask_));
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_InputMask_));
    if (!(flags & ImGuiColorEditFlags_NoOptions)) flags |= (g.ColorEditOptions & ImGuiColorEditFlags_AlphaBar);

    int components = (flags & ImGuiColorEditFlags_NoAlpha) ? 3 : 4;
    bool alpha_bar = (flags & ImGuiColorEditFlags_AlphaBar) && !(flags & ImGuiColorEditFlags_NoAlpha);
    ImVec2 bars_pos = window->DC.CursorPos;
    float square_sz = GetFrameHeight();
    float bars_width = 15.f;
    float sv_picker_size = ImMax(bars_width * 1, width - (alpha_bar ? 2 : 1) * (bars_width));
    float bar0_pos_x = bars_pos.x + 4.f;
    float bar1_pos_x = bar0_pos_x + bars_width + 10;
    ImVec2 picker_pos = ImVec2(bar1_pos_x + 25.f, window->DC.CursorPos.y);
    float bars_triangles_half_sz = IM_TRUNC(bars_width * 0.20f);

    float backup_initial_col[4];
    memcpy(backup_initial_col, col, components * sizeof(float));

    float H = col[0], S = col[1], V = col[2];
    float R = col[0], G = col[1], B = col[2];
    if (flags & ImGuiColorEditFlags_InputRGB) {
        ColorConvertRGBtoHSV(R, G, B, H, S, V);
        ColorEditRestoreHS(col, &H, &S, &V);
    }
    else if (flags & ImGuiColorEditFlags_InputHSV) {
        ColorConvertHSVtoRGB(H, S, V, R, G, B);
    }

    bool value_changed = false, value_changed_h = false, value_changed_sv = false;

    ImGui::SetCursorScreenPos(picker_pos);
    InvisibleButton("sv", ImVec2(sv_picker_size, sv_picker_size));
    if (IsItemActive()) {
        S = ImSaturate((io.MousePos.x - picker_pos.x) / (sv_picker_size - 1));
        V = 1.0f - ImSaturate((io.MousePos.y - picker_pos.y) / (sv_picker_size - 1));
        ColorEditRestoreH(col, &H);
        value_changed = value_changed_sv = true;
    }
    if (!(flags & ImGuiColorEditFlags_NoOptions)) OpenPopupOnItemClick("context", ImGuiPopupFlags_MouseButtonRight);

    SetCursorScreenPos(ImVec2(bar0_pos_x, picker_pos.y));
    InvisibleButton("hue", ImVec2(bars_width, sv_picker_size));
    if (IsItemActive()) {
        H = ImSaturate((io.MousePos.y - picker_pos.y) / (sv_picker_size - 1));
        value_changed = value_changed_h = true;
    }

    if (alpha_bar) {
        SetCursorScreenPos(ImVec2(bar1_pos_x, picker_pos.y));
        InvisibleButton("alpha", ImVec2(bars_width, sv_picker_size));
        if (IsItemActive()) {
            col[3] = 1.0f - ImSaturate((io.MousePos.y - picker_pos.y) / (sv_picker_size - 1));
            value_changed = true;
        }
    }

    if (value_changed_h || value_changed_sv) {
        if (flags & ImGuiColorEditFlags_InputRGB) {
            ColorConvertHSVtoRGB(H, S, V, col[0], col[1], col[2]);
            g.ColorEditSavedHue = H;
            g.ColorEditSavedSat = S;
            g.ColorEditSavedID = g.ColorEditCurrentID;
            g.ColorEditSavedColor = ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], 0));

            // Force value changed to ensure it's saved
            value_changed = true;
        }
        else if (flags & ImGuiColorEditFlags_InputHSV) {
            col[0] = H;
            col[1] = S;
            col[2] = V;

            // Force value changed to ensure it's saved
            value_changed = true;
        }
    }

    if (value_changed) {
        if (flags & ImGuiColorEditFlags_InputRGB) {
            R = col[0];
            G = col[1];
            B = col[2];
            ColorConvertRGBtoHSV(R, G, B, H, S, V);
            ColorEditRestoreHS(col, &H, &S, &V);
        }
        else if (flags & ImGuiColorEditFlags_InputHSV) {
            H = col[0];
            S = col[1];
            V = col[2];
            ColorConvertHSVtoRGB(H, S, V, R, G, B);
        }
    }

    const int style_alpha8 = IM_F32_TO_INT8_SAT(style.Alpha);
    const ImU32 col_black = IM_COL32(0, 0, 0, style_alpha8);
    const ImU32 col_white = IM_COL32(255, 255, 255, style_alpha8);
    const ImU32 col_midgrey = IM_COL32(128, 128, 128, style_alpha8);
    const ImU32 col_hues[6 + 1] = { IM_COL32(255, 0, 0, style_alpha8), IM_COL32(255, 255, 0, style_alpha8),
                                   IM_COL32(0, 255, 0, style_alpha8), IM_COL32(0, 255, 255, style_alpha8),
                                   IM_COL32(0, 0, 255, style_alpha8), IM_COL32(255, 0, 255, style_alpha8),
                                   IM_COL32(255, 0, 0, style_alpha8) };

    ImVec4 hue_color_f(1, 1, 1, style.Alpha);
    ColorConvertHSVtoRGB(H, 1, 1, hue_color_f.x, hue_color_f.y, hue_color_f.z);
    ImU32 hue_color32 = ColorConvertFloat4ToU32(hue_color_f);
    ImU32 user_col32_striped_of_alpha = ColorConvertFloat4ToU32(ImVec4(R, G, B, style.Alpha));

    static std::map<ImGuiID, picker_state> anim;
    picker_state& state = anim[ImGui::GetID(label)];

    ImVec2 sv_cursor_pos;

    draw_list->AddRectFilledMultiColor(picker_pos,
        picker_pos + ImVec2(sv_picker_size, sv_picker_size),
        col_white,
        hue_color32,
        hue_color32,
        col_white,
        5.f);
    draw_list->AddRectFilledMultiColor(picker_pos - ImVec2(1, 1),
        picker_pos + ImVec2(sv_picker_size + 1, sv_picker_size + 1),
        0,
        0,
        col_black,
        col_black,
        5.f);

    sv_cursor_pos.x = ImClamp(IM_ROUND(picker_pos.x + ImSaturate(S) * sv_picker_size),
        picker_pos.x + 2,
        picker_pos.x + sv_picker_size - 2);
    sv_cursor_pos.y = ImClamp(IM_ROUND(picker_pos.y + ImSaturate(1 - V) * sv_picker_size),
        picker_pos.y + 2,
        picker_pos.y + sv_picker_size - 2);

    for (int i = 0; i < 6; ++i)
        draw_list->AddRectFilledMultiColor(ImVec2(bar0_pos_x,
            picker_pos.y + i * (sv_picker_size / 6) - (i == 5 ? 1 : 0)),
            ImVec2(bar0_pos_x + bars_width,
                picker_pos.y + (i + 1) * (sv_picker_size / 6) + (i == 0 ? 1 : 0)),
            col_hues[i],
            col_hues[i],
            col_hues[i + 1],
            col_hues[i + 1],
            10.f,
            i == 0 ? ImDrawFlags_RoundCornersTop : i == 5
            ? ImDrawFlags_RoundCornersBottom
            : ImDrawFlags_RoundCornersNone);

    float bar0_line_y = IM_ROUND(picker_pos.y + H * sv_picker_size);
    bar0_line_y = ImClamp(bar0_line_y, picker_pos.y + 3.f, picker_pos.y + (sv_picker_size - 13));

    state.hue_bar = ImLerp(state.hue_bar, bar0_line_y + 5, g.IO.DeltaTime * 24.f);

    draw_list->AddShadowCircle(ImVec2(bar0_pos_x + 7.5f, state.hue_bar), 4.5f, col_black, 15.f, ImVec2(0, 0));
    draw_list->AddCircle(ImVec2(bar0_pos_x + 7.5f, state.hue_bar), 4.5f, col_white, 100.f, 1.5f);

    float sv_cursor_rad = value_changed_sv ? 10.0f : 6.0f;
    int sv_cursor_segments = draw_list->_CalcCircleAutoSegmentCount(sv_cursor_rad);

    state.circle_move = ImLerp(state.circle_move, sv_cursor_pos, g.IO.DeltaTime * 20.f);
    state.circle = ImLerp(state.circle, value_changed_sv ? 6.0f : 4.0f, g.IO.DeltaTime * 24.f);

    draw_list->AddCircle(state.circle_move, state.circle, col_white, sv_cursor_segments, 2.f);

    if (alpha_bar) {
        float alpha = ImSaturate(col[3]);
        ImRect bar1_bb(bar1_pos_x, picker_pos.y, bar1_pos_x + bars_width, picker_pos.y + sv_picker_size);

        ImGui::RenderColorRectWithAlphaCheckerboard(draw_list,
            bar1_bb.Min,
            bar1_bb.Max,
            ImColor(1.f, 1.f, 1.f, 0.6f),
            5.f,
            ImVec2(1, 1),
            100.f,
            ImDrawFlags_RoundCornersAll);
        draw_list->AddRectFilledMultiColor(bar1_bb.Min,
            bar1_bb.Max,
            user_col32_striped_of_alpha,
            user_col32_striped_of_alpha,
            user_col32_striped_of_alpha & ~IM_COL32_A_MASK,
            user_col32_striped_of_alpha & ~IM_COL32_A_MASK,
            100.f);

        draw_list->AddRect(bar1_bb.Min - ImVec2(2, 2), bar1_bb.Max + ImVec2(2, 2), gui.window_bg, 100.f, 0, 3.f);

        float bar1_line_y = IM_ROUND(picker_pos.y + (1.0f - alpha) * sv_picker_size);
        bar1_line_y = ImClamp(bar1_line_y, picker_pos.y + 3.f, picker_pos.y + (sv_picker_size - 13));

        state.alpha_bar = ImLerp(state.alpha_bar, bar1_line_y + 5, g.IO.DeltaTime * 24.f);
        draw_list->AddShadowCircle(ImVec2(bar1_pos_x + 7.5f, state.alpha_bar), 4.5f, col_black, 15.f, ImVec2(0, 0));
        draw_list->AddCircle(ImVec2(bar1_pos_x + 7.5f, state.alpha_bar), 4.5f, col_white, 100.f, 1.5f);
    }

    EndGroup();

    // Always mark as changed if any interaction occurred with the color picker
    // Check for any interaction with the color picker
    bool any_interaction = value_changed || value_changed_h || value_changed_sv ||
                          ImGui::IsItemActive() || ImGui::IsMouseDown(0);

    // Force the color to be marked as changed
    if (any_interaction) {
        // Force the color to be different from the backup to ensure it's saved
        if (memcmp(backup_initial_col, col, components * sizeof(float)) == 0) {
            // Add a tiny value to ensure it's different
            col[0] = ImClamp(col[0] + 0.0001f, 0.0f, 1.0f);
        }

        // Mark item as edited to ensure changes are saved
        MarkItemEdited(g.LastItemData.ID);

        // Set value_changed to true to ensure the caller knows the color changed
        value_changed = true;

        // Save the current color as the new saved color
        g.ColorEditSavedColor = ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], col[3]));

        // Force an immediate update of the color
        memcpy(col, col, components * sizeof(float));
    }

    // Always return true if there was any interaction to force the caller to update
    if (any_interaction)
        return true;

    PopID();

    return value_changed;
}

//bool nav_elements::CheckboxComponent(const char* name, bool* v, const char* description, bool hasHotkey, std::vector<ColorState> colorStates, int* key, bool* mode) {
//    // --- Early return check ---
//    ImGuiWindow* window = GetCurrentWindow();
//    if (window->SkipItems)
//        return false;
//
//    // --- Setup and ID generation ---
//    ImGuiContext& g = *GImGui;
//    const ImGuiStyle& style = g.Style;
//    const ImGuiID id = window->GetID(name);
//    std::string label_str = name;
//
//    // --- Layout calculations ---
//    const ImVec2 pos = window->DC.CursorPos;
//    const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;
//
//    // Calculate total width needed for color pickers and settings button
//    float leftSideWidth = 0;
//    const float colorPickerSize = 26; // Same size as checkbox
//    const float settingsButtonSize = 26; // Same size as checkbox
//    const float spacing = 5.0f;
//
//    // Add width for each color picker
//    leftSideWidth += colorStates.size() * (colorPickerSize + spacing);
//
//    // Add width for settings button if needed
//    if (hasHotkey)
//        leftSideWidth += settingsButtonSize + spacing;
//
//    // Adjust for spacing
//    if (leftSideWidth > 0)
//        leftSideWidth -= spacing; // Remove last spacing
//
//    const float component_height = 60.f;
//
//    const ImRect total_bb(pos, pos + ImVec2(content_width, component_height));
//    ItemSize(total_bb, 0.f);
//
//    if (!ItemAdd(total_bb, id))
//        return false;
//
//    // --- Checkbox dimensions and positioning ---
//    ImVec2 check_size = ImVec2(50, 26);
//    const float check_spacing = (total_bb.GetSize().y - check_size.y) / 2;
//    const ImRect check_bb(
//        total_bb.Max - ImVec2(check_spacing, check_spacing) - check_size,
//        total_bb.Max - ImVec2(check_spacing, check_spacing)
//    );
//
//    // --- Input handling ---
//    bool hovered, held;
//    bool pressed = ButtonBehavior(total_bb, id, &hovered, &held);
//
//    // --- Text positioning ---
//    const ImVec2 description_size = CalcTextSize(description, NULL, true);
//    const ImVec2 label_size = CalcTextSize(name, NULL, true);
//    const ImVec2 label_pos = ImVec2(
//        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
//        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
//    );
//
//    // --- Animation state management ---
//    static std::map<ImGuiID, check_state> anim;
//    auto it_anim = anim.emplace(id, check_state()).first;
//
//    // Update animation colors
//    const float anim_speed = GetAnimSpeed();
//    const bool is_active = *v;
//
//    it_anim->second.check_color = ImLerp(
//        it_anim->second.check_color,
//        is_active ? gui.checkboxstroke : ImColor(34, 27, 27, 255),
//        anim_speed / 3
//    );
//
//    it_anim->second.check_rect_color = ImLerp(
//        it_anim->second.check_rect_color,
//        is_active ? gui.checkboxstrokeactive : ImColor(34, 27, 27, 255),
//        anim_speed / 3
//    );
//
//    it_anim->second.check_color_circle = ImLerp(
//        it_anim->second.check_color_circle,
//        is_active ? ImColor(0, 120, 255, 255) : ImColor(255, 255, 255, 255),
//        anim_speed / 3
//    );
//
//    it_anim->second.check_color_shadow = ImLerp(
//        it_anim->second.check_color_shadow,
//        is_active ? ImColor(0, 120, 255, 255) : ImColor(255, 255, 255, 255),
//        anim_speed / 3
//    );
//
//    ImColor main = ImLerpCol(gui.main, gui.main, ImGui::GetIO().DeltaTime * 10.f);
//    it_anim->second.icon_color = ImLerp(
//        it_anim->second.icon_color,
//        is_active || hovered ? func.GetDarkColor(main) : main,
//        anim_speed
//    );
//
//    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity, (is_active ? 0.08f : hovered ? 0.04f : 0.2f), 0.14f * (1.0f - ImGui::GetIO().DeltaTime));
//
//    it_anim->second.background_color = ImLerp(
//        it_anim->second.background_color,
//        is_active || hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
//        anim_speed
//    );
//    it_anim->second.background_rect_color = ImLerp(
//        it_anim->second.background_rect_color,
//        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f),
//        anim_speed
//    );
//
//    // Handle circle animation
//    if (is_active) {
//        EasingAnimationV2(
//            "circle_offset" + label_str,
//            &it_anim->second.circle_offset,
//            ImVec2(24, 0),
//            0.25f,
//            imanim::EasingCurve::Type::InOutCubic,
//            -1
//        );
//    }
//    else {
//        EasingAnimationV2(
//            "circle_offset" + label_str,
//            &it_anim->second.circle_offset,
//            ImVec2(0, 0),
//            0.25f,
//            imanim::EasingCurve::Type::InOutCubic,
//            -1
//        );
//    }
//
//    // --- Rendering ---
//    // Background
//    window->DrawList->AddRectFilled(
//        total_bb.Min,
//        total_bb.Max,
//        GetColorU32(it_anim->second.background_color),
//        3.0f
//    );
//
//    // Border
//    window->DrawList->AddRect(
//        total_bb.Min,
//        total_bb.Max,
//        GetColorU32(it_anim->second.background_rect_color),
//        3.0f
//    );
//
//    // Icon
//    PushFont(iconsBig);
//    window->DrawList->AddText(
//        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2 - CalcTextSize(ICON_CIRCLE_FILL).x / 2, total_bb.Min.y + total_bb.GetSize().y / 2 - CalcTextSize(ICON_CIRCLE_FILL).y / 2),
//        GetColorU32(it_anim->second.icon_color),
//        ICON_CIRCLE_FILL
//    );
//    PopFont();
//
//    // Text
//    window->DrawList->AddText(label_pos, gui.text[0], name);
//    window->DrawList->AddText(ImVec2(label_pos.x, label_pos.y + label_size.y + 2), gui.text[1], description);
//
//    // Red line on the left
//    window->DrawList->AddRectFilled(
//        total_bb.Min,
//        ImVec2(total_bb.Min.x + 5, total_bb.Max.y),
//        GetColorU32(it_anim->second.rect_color),
//        3.0f,
//        ImDrawFlags_RoundCornersLeft
//    );
//
//    // Shadow for the red line
//    window->DrawList->AddShadowRect(
//        total_bb.Min, // Start position of the line
//        total_bb.Min + ImVec2(total_bb.GetSize().y - 5, total_bb.GetSize().y),   // End position with small inset
//        GetColorU32(it_anim->second.rect_shadow_color), // Shadow color
//        20.f,       // Slightly smaller shadow size
//        ImVec2(0, 0), // Negative X offset to pull shadow away from red line
//        0,          // Shadow flags (0 for default)
//        ImDrawFlags_RoundCornersLeft       // Shadow rounding
//    );
//
//    // Checkbox background
//    GetWindowDrawList()->AddRectFilled(
//        check_bb.Min,
//        check_bb.Max,
//        GetColorU32(it_anim->second.check_color),
//        30.f
//    );
//
//    // Checkbox border
//    GetWindowDrawList()->AddRect(
//        check_bb.Min,
//        check_bb.Max,
//        GetColorU32(it_anim->second.check_rect_color),
//        30.f
//    );
//
//    // Checkbox shadow
//    window->DrawList->AddShadowCircle(
//        check_bb.Min + check_bb.Max,
//        8.f,
//        GetColorU32(it_anim->second.check_color),
//        15.f,
//        ImVec2(0, 0),
//        0,
//        360
//    );
//
//    // Circle shadow
//    window->DrawList->AddShadowCircle(
//        check_bb.Min + ImVec2(13 + it_anim->second.circle_offset.x, 13),
//        6.f,
//        GetColorU32(it_anim->second.check_color_circle),
//        70.f,
//        ImVec2(0, 0),
//        0,
//        360
//    );
//
//    // Icon circle shadow
//    window->DrawList->AddShadowCircle(
//        total_bb.Min + ImVec2(total_bb.GetSize().y / 2, total_bb.GetSize().y / 2),
//        9.f,
//        GetColorU32(it_anim->second.icon_color),
//        40,
//        ImVec2(0, 0),
//        0,
//        360
//    );
//
//    // Slider circle
//    window->DrawList->AddCircleFilled(
//        check_bb.Min + ImVec2(13 + it_anim->second.circle_offset.x, 13),
//        8.f,
//        GetColorU32(it_anim->second.check_color_circle),
//        360
//    );
//
//    // --- Color pickers from right to left and settings button at left ---
//    // Consistent 10px padding between all elements
//    const float elementPadding = 10.0f;
//    // Start position for the rightmost color picker
//    float currentX = total_bb.Max.x - check_size.x - elementPadding;
//
//    // Draw color pickers from right to left
//    for (int i = 0; i < colorStates.size(); i++) {
//        // Calculate position for this color picker - going from right to left
//        // Use consistent 10px padding between color pickers
//        currentX -= (colorPickerSize + elementPadding);
//        ImVec2 colorPickerPos = ImVec2(currentX, total_bb.Min.y + (total_bb.GetSize().y - colorPickerSize) / 2);
//        // Create a color value from the color state
//        ImVec4 colorValue(colorStates[i].color[0], colorStates[i].color[1], colorStates[i].color[2], colorStates[i].color[3]);
//
//        // Save the current cursor position
//        ImVec2 oldCursorPos = window->DC.CursorPos;
//
//        // Set cursor position to the color picker position
//        window->DC.CursorPos = colorPickerPos;
//        std::string colorEditId = "##ColorEdit" + std::to_string(i) + "_" + name;
//        // Call ColorEdit4Ex directly - this will handle opening the popup
//        nav_elements::ColorEdit4Ex(colorEditId.c_str(), colorStates[i].label.c_str(), colorStates[i].color,
//            ImGuiColorEditFlags_NoInputs |
//            ImGuiColorEditFlags_AlphaBar |
//            ImGuiColorEditFlags_AlphaPreview |
//            ImGuiColorEditFlags_NoLabel |
//            ImGuiColorEditFlags_NoOptions |
//            ImGuiColorEditFlags_NoSidePreview);
//
//        // Prevent the click from toggling the checkbox
//        pressed = false;
//
//        // Add tooltip for the color picker
//        if (ImGui::IsItemHovered()) {
//            ImGui::BeginTooltip();
//            ImGui::Text("%s", colorStates[i].label.c_str());
//            ImGui::EndTooltip();
//        }
//
//        // Restore the cursor position
//        window->DC.CursorPos = oldCursorPos;
//    }
//
//    // Draw settings button on the left if needed
//    if (hasHotkey && key != nullptr && mode != nullptr) {
//        // Calculate position for the settings button with consistent 10px padding
//        // Position it to the left of the color pickers with 10px padding
//        currentX -= (settingsButtonSize + elementPadding);
//        ImVec2 settingsButtonPos = ImVec2(currentX, total_bb.Min.y + (total_bb.GetSize().y - settingsButtonSize) / 2);
//        ImRect settingsButtonRect(settingsButtonPos, ImVec2(settingsButtonPos.x + settingsButtonSize, settingsButtonPos.y + settingsButtonSize));
//
//        // Check if mouse is hovering over the settings button
//        bool settingsHovered = ImGui::IsMouseHoveringRect(settingsButtonRect.Min, settingsButtonRect.Max);
//
//        // Draw settings button background (same as checkbox background)
//        window->DrawList->AddRectFilled(
//            settingsButtonRect.Min,
//            settingsButtonRect.Max,
//            GetColorU32(it_anim->second.check_color),
//            3.0f
//        );
//
//        // Draw settings button border
//        window->DrawList->AddRect(
//            settingsButtonRect.Min,
//            settingsButtonRect.Max,
//            GetColorU32(it_anim->second.check_rect_color),
//            3.0f
//        );
//
//        // Draw settings icon
//        PushFont(iconsBig);
//        ImVec2 iconSize = CalcTextSize(ICON_SETTINGS_LINE);
//        window->DrawList->AddText(
//            settingsButtonRect.Min + ImVec2((settingsButtonSize - iconSize.x) / 2, (settingsButtonSize - iconSize.y) / 2),
//            GetColorU32(it_anim->second.check_color_circle),
//            ICON_SETTINGS_LINE
//        );
//        PopFont();
//
//        // Handle settings button click - open Keybind directly
//        if (settingsHovered && ImGui::IsMouseClicked(0)) {
//            // Open keybind popup directly
//            ImGui::OpenPopup((std::string("keybind_popup_") + name).c_str());
//
//            // Center the popup over the settings button
//            ImVec2 popupPos = ImVec2(
//                settingsButtonRect.Min.x + settingsButtonSize/2,
//                settingsButtonRect.Min.y + settingsButtonSize/2
//            );
//            ImGui::SetNextWindowPos(popupPos, ImGuiCond_Always, ImVec2(0.5f, 0.5f));
//
//            // Prevent the click from toggling the checkbox
//            pressed = false;
//        }
//
//        // Add tooltip for the settings button
//        if (settingsHovered) {
//            ImGui::BeginTooltip();
//            ImGui::Text("Hotkey Settings");
//            ImGui::EndTooltip();
//        }
//
//        // Handle the keybind popup
//        if (ImGui::BeginPopup((std::string("keybind_popup_") + name).c_str())) {
//            // Set popup style to match ColorPicker4
//            ImGui::PushStyleColor(ImGuiCol_PopupBg, ImVec4(0.11f, 0.11f, 0.14f, 0.94f));
//            ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(10, 10));
//            ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 5.0f);
//
//            // Call Keyset directly
//            ImGui::Text("%s Hotkey", name);
//            ImGui::Spacing();
//
//            // Add keybind setting
//            nav_elements::Keyset(name, key);
//            ImGui::Spacing();
//
//            // Add toggle/hold mode selection
//            nav_elements::ToggleButton("Hold", "Toggle", mode, ImVec2(150, 30));
//            ImGui::Spacing();
//            ImGui::Separator();
//            ImGui::Spacing();
//
//            // Add reset button that doesn't close the popup
//            if (ImGui::Button("Reset Hotkey", ImVec2(150, 30))) {
//                *key = 0; // Reset to no key without closing the popup
//            }
//
//            ImGui::PopStyleVar(2);
//            ImGui::PopStyleColor();
//            ImGui::EndPopup();
//        }
//    }
//
//    // The popup is now handled directly by ColorEdit4Ex
//
//    // Toggle the checkbox value if pressed (and not clicking on color picker or settings)
//    if (pressed) {
//        *v = !(*v);
//        MarkItemEdited(id);
//    }
//
//    return pressed;
//}
bool nav_elements::CheckboxComponent(const char* name, bool* v, const char* description, bool hasHotkey, std::vector<ColorState> colorStates, int* key, bool* mode) {
    // --- Early return check ---
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    // --- Setup and ID generation ---
    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(name);
    std::string label_str = name;

    // --- Layout calculations ---
    const ImVec2 pos = window->DC.CursorPos;
    const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;

    // Calculate total width needed for color pickers and settings button
    float leftSideWidth = 0;
    const float colorPickerSize = 26; // Same size as checkbox
    const float settingsButtonSize = 26; // Same size as checkbox
    const float elementPadding = 15.0f; // Consistent padding between elements

    // Add width for each color picker
    leftSideWidth += colorStates.size() * (colorPickerSize + elementPadding);

    // Add width for settings button if needed
    if (hasHotkey)
        leftSideWidth += settingsButtonSize + elementPadding;

    // Adjust for spacing
    if (leftSideWidth > 0)
        leftSideWidth -= elementPadding; // Remove last spacing

    const float component_height = 60.f;

    const ImVec2 total_size(content_width, component_height);
    const ImRect total_bb(pos, ImVec2(pos.x + total_size.x, pos.y + total_size.y));
    ItemSize(total_bb, 0.f);

    if (!ItemAdd(total_bb, id))
        return false;

    // --- Checkbox dimensions and positioning ---
    ImVec2 check_size = ImVec2(50, 26);
    const float check_spacing = (total_bb.GetHeight() - check_size.y) / 2;
    const ImRect check_bb(
        ImVec2(total_bb.Max.x - check_spacing - check_size.x, total_bb.Max.y - check_spacing - check_size.y),
        ImVec2(total_bb.Max.x - check_spacing, total_bb.Max.y - check_spacing)
    );

    // --- Input handling ---
    bool hovered, held;
    bool pressed = ButtonBehavior(total_bb, id, &hovered, &held);

    // Track if settings button was clicked to prevent checkbox toggle
    bool settingsButtonClicked = false;

    // Synchronize checkbox state with hotkey system if hotkey exists
    if (hasHotkey && key != nullptr && name != nullptr && v != nullptr) {
        try {
            std::string featureName = std::string(name);
            auto hotkey = HotkeySystem::GetHotkey(featureName);
            if (hotkey && hotkey->statePtr) {
                // Update the checkbox to match the hotkey system's state
                *v = *(hotkey->statePtr);
            }
        } catch (...) {
            // Catch any exceptions to prevent crashes
        }
    }

    // --- Text positioning ---
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    const ImVec2 label_size = CalcTextSize(name, NULL, true);
    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
    );

    // --- Animation state management ---
    static std::map<ImGuiID, check_state> anim;
    auto it_anim = anim.emplace(id, check_state()).first;

    static std::map<ImGuiID, edit_state> animation;
    edit_state& state = animation[ImGui::GetID(name)];

    // Update animation colors
    const float anim_speed = GetAnimSpeed();
    const bool is_active = *v;

    it_anim->second.check_color = ImLerp(
        it_anim->second.check_color,
        is_active ? gui.checkboxstroke : ImColor(34, 27, 27, 255),
        anim_speed / 3
    );

    it_anim->second.check_color_circle = ImLerp(
        it_anim->second.check_color_circle,
        is_active ? gui.main : ImColor(64, 57, 57, 255),
        anim_speed / 3
    );

    it_anim->second.rect_color = ImLerp(
        it_anim->second.rect_color,
        is_active || hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f),
        anim_speed
    );

    it_anim->second.rect_shadow_color = ImLerp(
        it_anim->second.rect_shadow_color,
        is_active || hovered ? gui.main : func.GetColorWithAlpha(ImColor(0, 0, 0, 255), 0.3f),
        anim_speed
    );

    it_anim->second.check_rect_color = ImLerp(
        it_anim->second.check_rect_color,
        is_active ? gui.checkboxstrokeactive : gui.checkboxstroke,
        anim_speed / 3
    );

    it_anim->second.circle_alpha = ImLerp(
        it_anim->second.circle_alpha,
        is_active ? 0.9f : 0.6f,
        anim_speed
    );

    ImColor main = ImLerpCol(gui.main, gui.main, ImGui::GetIO().DeltaTime * 10.f);
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        is_active || hovered ? func.GetDarkColor(main) : main,
        anim_speed
    );

    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity, (is_active ? 0.08f : hovered ? 0.04f : 0.2f), 0.14f * (1.0f - ImGui::GetIO().DeltaTime));

    it_anim->second.background_color = ImLerp(
        it_anim->second.background_color,
        is_active || hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
        anim_speed
    );
    it_anim->second.background_rect_color = ImLerp(
        it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f),
        anim_speed
    );

    // Handle circle animation
    if (is_active) {
        EasingAnimationV2(
            "circle_offset" + label_str,
            &it_anim->second.circle_offset,
            ImVec2(24, 0),
            0.25f,
            imanim::EasingCurve::Type::InOutCubic,
            -1
        );
    }
    else {
        EasingAnimationV2(
            "circle_offset" + label_str,
            &it_anim->second.circle_offset,
            ImVec2(0, 0),
            0.25f,
            imanim::EasingCurve::Type::InOutCubic,
            -1
        );
    }

    // --- Rendering ---
    // Background
    window->DrawList->AddRectFilled(
        total_bb.Min,
        total_bb.Max,
        GetColorU32(it_anim->second.background_color),
        3.0f
    );

    // Border
    window->DrawList->AddRect(
        total_bb.Min,
        total_bb.Max,
        GetColorU32(it_anim->second.background_rect_color),
        3.0f
    );

    // Icon
    PushFont(iconsBig);
    window->DrawList->AddText(
        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2 - CalcTextSize(ICON_CIRCLE_FILL).x / 2, total_bb.Min.y + total_bb.GetSize().y / 2 - CalcTextSize(ICON_CIRCLE_FILL).y / 2),
        GetColorU32(it_anim->second.icon_color),
        ICON_CIRCLE_FILL
    );
    PopFont();

    // Text
    window->DrawList->AddText(label_pos, gui.text[0], name);
    window->DrawList->AddText(ImVec2(label_pos.x, label_pos.y + label_size.y + 2), gui.text[1], description);

    // Left background rectangle
    GetWindowDrawList()->AddRectFilled(
        total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color),
        style.FrameRounding,
        ImDrawFlags_RoundCornersLeft
    );

    // Shadow for the left rectangle
    window->DrawList->AddShadowRect(
        total_bb.Min, // Start position of the line
        ImVec2(total_bb.Min.x + total_bb.GetSize().y - 5, total_bb.Max.y),   // End position with small inset
        GetColorU32(it_anim->second.rect_shadow_color), // Shadow color
        20.f,       // Slightly smaller shadow size
        ImVec2(0, 0), // Negative X offset to pull shadow away from red line
        0,          // Shadow flags (0 for default)
        ImDrawFlags_RoundCornersLeft       // Shadow rounding
    );

    // Checkbox background
    GetWindowDrawList()->AddRectFilled(
        check_bb.Min,
        check_bb.Max,
        GetColorU32(it_anim->second.check_color),
        30.f
    );

    // Checkbox border
    GetWindowDrawList()->AddRect(
        check_bb.Min,
        check_bb.Max,
        GetColorU32(it_anim->second.check_rect_color),
        30.f
    );

    // Checkbox shadow
    window->DrawList->AddShadowCircle(
        ImVec2((check_bb.Min.x + check_bb.Max.x) / 2, (check_bb.Min.y + check_bb.Max.y) / 2),
        8.f,
        GetColorU32(it_anim->second.check_color),
        15.f,
        ImVec2(0, 0),
        0,
        360
    );

    // Circle shadow
    window->DrawList->AddShadowCircle(
        ImVec2(check_bb.Min.x + 13 + it_anim->second.circle_offset.x, check_bb.Min.y + 13),
        6.f,
        GetColorU32(it_anim->second.check_color_circle),
        70.f,
        ImVec2(0, 0),
        0,
        360
    );

    // Icon circle shadow
    window->DrawList->AddShadowCircle(
        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2, total_bb.Min.y + total_bb.GetSize().y / 2),
        9.f,
        GetColorU32(it_anim->second.icon_color),
        40,
        ImVec2(0, 0),
        0,
        360
    );

    // Slider circle
    window->DrawList->AddCircleFilled(
        ImVec2(check_bb.Min.x + 13 + it_anim->second.circle_offset.x, check_bb.Min.y + 13),
        8.f,
        GetColorU32(it_anim->second.check_color_circle),
        360
    );

    // --- Color pickers from right to left and settings button at left ---
    // Start position for the rightmost color picker
    float currentX = total_bb.Max.x - check_size.x - elementPadding;

    // Global state for tracking which color picker is active for each feature
    static std::map<std::string, int> activeColorPickerIndexMap;
    auto& activeColorPickerIndex = activeColorPickerIndexMap[std::string(name)];
    if (activeColorPickerIndex < 0) activeColorPickerIndex = -1; // Initialize if not set
    static ImGuiID activeColorPickerID = 0;

    // Draw color pickers from right to left

    // Handle the color picker popup
    ImGui::PushStyleColor(ImGuiCol_WindowBg, func.ImColorToImVec4(gui.window_bg));
    ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
    ImGui::PushStyleColor(ImGuiCol_Border, func.ImColorToImVec4(gui.stroke));

    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, style.FrameRounding);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 1.f);

    if (activeColorPickerIndex >= 0 && gui.picker_active) {
        EasingAnimationV2("picker_state" + label_str,
            &it_anim->second.picker_offset,
            ImVec2(0.f, 2.f),
            0.25f,
            imanim::EasingCurve::Type::OutBack,
            -1);
    }
    else {
        EasingAnimationV2("picker_state" + label_str,
            &it_anim->second.picker_offset,
            ImVec2(0.f, 0.f),
            0.25f,
            imanim::EasingCurve::Type::InQuad,
            -1);
    }
    // Dark overlay is handled in GUI.cpp

    // Center the color picker popup in the menu window
    ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
    SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));

    // Create a unique popup ID for this specific feature
    std::string uniquePopupId = "##ColorPickerPopup_" + std::string(name);

    // Use a map to track popup state for each feature
    static std::map<std::string, bool> popupJustOpenedMap;
    auto& isPopupJustOpened = popupJustOpenedMap[std::string(name)];

    if (isPopupJustOpened) {
        if (!ImGui::IsPopupOpen(uniquePopupId.c_str()))
            ImGui::OpenPopup(uniquePopupId.c_str());
        isPopupJustOpened = false;
    }

    if (ImGui::BeginPopup(uniquePopupId.c_str(), ImGuiWindowFlags_NoMove)) {
        // Check if mouse is hovering over the color picker window
        state.hovered = ImGui::IsWindowHovered(ImGuiHoveredFlags_AllowWhenBlockedByPopup |
                                              ImGuiHoveredFlags_AllowWhenBlockedByActiveItem |
                                              ImGuiHoveredFlags_ChildWindows);

        // Set focus to the window when hovered
        if (state.hovered)
            ImGui::SetWindowFocus();

        // Close the popup when pressing Escape
        if (ImGui::IsKeyPressed(ImGuiKey_Escape))
        {
            activeColorPickerIndexMap[std::string(name)] = -1; // Reset for this specific feature
            gui.picker_active = false;
            state.active = false;
            ImGui::CloseCurrentPopup();
        }

        // Close the popup when clicking outside, but only if not clicking on a UI element
        bool clicked_outside = ImGui::IsMouseClicked(ImGuiMouseButton_Left) &&
                              !ImGui::IsWindowHovered(ImGuiHoveredFlags_AnyWindow |
                                                     ImGuiHoveredFlags_AllowWhenBlockedByPopup);
        if (clicked_outside && !ImGui::IsAnyItemHovered())
        {
            activeColorPickerIndexMap[std::string(name)] = -1; // Reset for this specific feature
            gui.picker_active = false;
            state.active = false;
            ImGui::CloseCurrentPopup();
        }

        // Keep the state active while the popup is open
        state.active = true;
        ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, style.FrameRounding);
        // Only show color picker buttons if we have more than 1 color
        if (colorStates.size() > 1) {
            // Calculate button width based on number of colors (max 5)
            int numColors = (int)colorStates.size();
            if (numColors > 5) numColors = 5; // Limit to max 5 colors
            const float totalWidth = ImGui::GetContentRegionAvail().x;
            const float buttonWidth = (totalWidth / numColors) - (elementPadding * (numColors - 1) / numColors);
            const float buttonHeight = 30.0f;

            ImGui::BeginGroup();

            // Draw color picker buttons in a horizontal row with rounded borders
            for (int i = 0; i < numColors; i++) {
                ImVec4 color(
                    colorStates[i].color[0],
                    colorStates[i].color[1],
                    colorStates[i].color[2],
                    colorStates[i].color[3]
                );

                // Create a unique ID for this color picker button
                std::string colorButtonId = "##clrbtn_" + std::string(name) + "_" + std::to_string(i);

                // Push style for rounded buttons
                ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 5.0f);
                ImGui::PushStyleColor(ImGuiCol_Button, color);
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, color);
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, color);

                if (i > 0) ImGui::SameLine(0, elementPadding);
                if (nav_elements::ColorButton(colorButtonId.c_str(), color, ImGuiColorEditFlags_NoSidePreview | ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaPreview, ImVec2(buttonWidth, buttonHeight)) ||
                    (i == 0 && activeColorPickerIndex == -1)) {
                    activeColorPickerIndex = i;
                    // No need to set gui.picker_active here since we're already in the popup
                }

                // Add tooltip for the color button
                if (ImGui::IsItemHovered()) {
                    ImGui::BeginTooltip();
                    ImGui::Text("%s", colorStates[i].label.c_str());
                    ImGui::EndTooltip();
                }

                ImGui::PopStyleColor(3);
                ImGui::PopStyleVar();
            }

            ImGui::EndGroup();
            ImGui::Spacing();
        } else if (!colorStates.empty()) {
            // If only one color, set it as active without showing buttons
            activeColorPickerIndex = 0;
        }

        // Only show color section if we have colors
        if (!colorStates.empty()) {
            ImGui::Separator();
            ImGui::Spacing();
            ImGui::Text("Color Settings");
            ImGui::Spacing();

            // Show color picker if we have colors
            if (activeColorPickerIndex >= 0 && activeColorPickerIndex < colorStates.size()) {
                // Create a local copy of the color that we can modify
                float tempColor[4] = {
                    colorStates[activeColorPickerIndex].color[0],
                    colorStates[activeColorPickerIndex].color[1],
                    colorStates[activeColorPickerIndex].color[2],
                    colorStates[activeColorPickerIndex].color[3]
                };

                ImGuiColorEditFlags flags =
                    ImGuiColorEditFlags_DisplayRGB |
                    ImGuiColorEditFlags_AlphaBar |
                    ImGuiColorEditFlags_NoSidePreview;

                const float square_sz = 18.f;
                ImGui::SetNextItemWidth(square_sz * 11.5f);

                // Call ColorPicker4 with our temporary color
                bool colorChanged = nav_elements::ColorPicker4("##picker", tempColor, flags, nullptr);

                // Check if mouse is interacting with the color picker
                bool isInteracting = ImGui::IsItemActive() || ImGui::IsMouseDown(0);

                // If color changed or user is interacting with the picker
                if (colorChanged || isInteracting) {
                    // Update the color in the colorStates vector
                    colorStates[activeColorPickerIndex].color[0] = tempColor[0];
                    colorStates[activeColorPickerIndex].color[1] = tempColor[1];
                    colorStates[activeColorPickerIndex].color[2] = tempColor[2];
                    colorStates[activeColorPickerIndex].color[3] = tempColor[3];

                    // If we have a pointer to the original color in Settings, update it directly
                    if (colorStates[activeColorPickerIndex].originalColor != nullptr) {
                        float* origColor = colorStates[activeColorPickerIndex].originalColor;
                        origColor[0] = tempColor[0];
                        origColor[1] = tempColor[1];
                        origColor[2] = tempColor[2];
                        origColor[3] = tempColor[3];
                    }

                    // Mark the item as edited to ensure changes are saved
                    ImGui::MarkItemEdited(ImGui::GetCurrentContext()->LastItemData.ID);

                    // Force a save by marking the parent item as edited too
                    ImGui::MarkItemEdited(id);
                }

                ImGui::Spacing();
            }
        }

        // Hotkey settings section
        if (hasHotkey && key != nullptr && mode != nullptr) {
            ImGui::Separator();
            ImGui::Spacing();
            ImGui::Text("Hotkey Settings");
            ImGui::Spacing();

            // Add keybind setting with unique ID
            std::string keysetId = std::string(name) + "_keyset";
            nav_elements::Keyset(keysetId.c_str(), key);
            ImGui::Spacing();

            // Put toggle/hold and reset on the same line
            float availWidth = ImGui::GetContentRegionAvail().x;
            float toggleWidth = availWidth * 0.6f;
            float resetWidth = availWidth * 0.35f;

            // Toggle/Hold button with unique ID
            std::string toggleId = std::string(name) + "_toggle";
            nav_elements::ToggleButton("Hold", "Toggle", mode, ImVec2(toggleWidth, 30));

            ImGui::SameLine(0, 10);

            // Reset button with fixed text "Reset"
            if (ImGui::Button("Reset", ImVec2(resetWidth, 30))) {
                *key = 0; // Reset to no key without closing the popup
            }
            ImGui::SetItemTooltip("Reset Hotkey");
        }

        ImGui::PopStyleVar(2);
        ImGui::PopStyleColor();
        ImGui::EndPopup();
    }

    ImGui::PopStyleColor(3);
    ImGui::PopStyleVar(3);

    // Draw settings button on the left if needed
    if (hasHotkey && key != nullptr && mode != nullptr) {
        // Calculate position for the settings button with consistent 10px padding
        // Position it to the left of the color pickers with 10px padding
        currentX -= (settingsButtonSize + elementPadding);
        ImVec2 settingsButtonPos = ImVec2(currentX, total_bb.Min.y + (total_bb.GetSize().y - settingsButtonSize) / 2);
        ImRect settingsButtonRect(settingsButtonPos, ImVec2(settingsButtonPos.x + settingsButtonSize, settingsButtonPos.y + settingsButtonSize));
            // Check if mouse is hovering over the settings button
            bool settingsHovered = ImGui::IsMouseHoveringRect(settingsButtonRect.Min, settingsButtonRect.Max);

        // Draw settings button background (same as checkbox background)
        window->DrawList->AddRectFilled(
            settingsButtonRect.Min,
            settingsButtonRect.Max,
            GetColorU32(it_anim->second.check_color),
            3.0f
        );

        // Draw settings button border
        window->DrawList->AddRect(
            settingsButtonRect.Min,
            settingsButtonRect.Max,
            GetColorU32(it_anim->second.check_rect_color),
            3.0f
        );

        // Use iconsSmall font for even smaller icon
        PushFont(iconsSmall);

        // Get icon size
        ImVec2 iconSize = CalcTextSize(ICON_MS_SETTINGS_MOTION_MODE);

        // Calculate exact center of the button
        ImVec2 buttonCenter = ImVec2(
            settingsButtonRect.Min.x + settingsButtonSize / 2,
            settingsButtonRect.Min.y + settingsButtonSize / 2
        );

        // Position icon precisely at center with a small adjustment to ensure perfect centering
        ImVec2 iconPos = ImVec2(
            buttonCenter.x - (iconSize.x / 2),
            buttonCenter.y - (iconSize.y / 2)
        );

        // Apply a vertical offset to move the icon up for better visual centering
        // This corrects the icon appearing too low in the button
        //iconPos.x -= 1.0f;
        iconPos.y -= 6.5f; // Move icon up by 2.5 pixels

        window->DrawList->AddText(
            iconPos,
            gui.main,
            ICON_MS_SETTINGS_MOTION_MODE
        );
        PopFont();

        // Create a unique popup ID for this specific feature
        std::string uniquePopupId = "##ColorPickerPopup_" + std::string(name);

        // Handle settings button click - open color picker popup directly
        if (settingsHovered && ImGui::IsMouseClicked(0)) {
            activeColorPickerIndex = 0; // Initialize color index
            // Only set picker_active when actually opening the popup, not on hover
            gui.picker_active = true;
            state.active = true;

            // Set flag in the map for this specific feature
            popupJustOpenedMap[std::string(name)] = true;

            // Open the popup with the unique ID
            ImGui::OpenPopup(uniquePopupId.c_str());

            // Mark that settings button was clicked to prevent checkbox toggle
            settingsButtonClicked = true;
            pressed = false;
        }

        // Add tooltip for the settings button
        if (settingsHovered) {
            ImGui::BeginTooltip();
            ImGui::Text("Settings");
            ImGui::EndTooltip();
        }
    }

    // Toggle the checkbox value if clicked (and not clicking on settings button)
    // Use the same reliable approach as the simple Checkbox function
    if (ImGui::IsItemClicked() && !settingsButtonClicked && v != nullptr) {
        // Toggle the checkbox value
        bool newState = !(*v);
        *v = newState;
        MarkItemEdited(id);

        // If this checkbox has a hotkey, update the hotkey system to ensure proper synchronization
        if (hasHotkey && key != nullptr && name != nullptr) {
            try {
                // Try to find the corresponding hotkey binding and update the feature state
                std::string featureName = std::string(name);

                // Get the hotkey binding for this feature
                auto hotkey = HotkeySystem::GetHotkey(featureName);
                if (hotkey && hotkey->statePtr) {
                    // Update the hotkey system's state pointer
                    *(hotkey->statePtr) = newState;

                    // Call UpdateFeatureState to ensure all related states are synchronized
                    HotkeySystem::UpdateFeatureState(featureName, newState);

                    // Save settings to persist the change
                    HotkeySystem::SaveSettings();
                }
            } catch (...) {
                // Catch any exceptions to prevent crashes
                // Log error if needed, but don't crash the application
            }
        }
    }

    return pressed;
}




bool nav_elements::ColorButton(const char* desc_id, const ImVec4& col, ImGuiColorEditFlags flags, const ImVec2& size_arg) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *ImGui::GetCurrentContext();
    const ImGuiID id = window->GetID(desc_id);

    // Use the provided size or default to 20x20
    const ImVec2 size = size_arg.x <= 0 || size_arg.y <= 0 ? ImVec2(20, 20) : size_arg;
    const ImRect bb(window->DC.CursorPos, window->DC.CursorPos + size);

    const ImGuiStyle& style = g.Style;

    ImGui::ItemSize(bb);
    if (!ImGui::ItemAdd(bb, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(bb, id, &hovered, &held);

    ImVec4 col_rgb = col;
    if (flags & ImGuiColorEditFlags_InputHSV)
        ImGui::ColorConvertHSVtoRGB(col_rgb.x, col_rgb.y, col_rgb.z, col_rgb.x, col_rgb.y, col_rgb.z);

    static std::map<ImGuiID, check_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, check_state() });
        it_anim = anim.find(id);
    }

    // Update animation colors
    const float anim_speed = GetAnimSpeed();

    it_anim->second.check_color = ImLerp(
        it_anim->second.check_color,
        hovered ? gui.checkboxstroke : ImColor(34, 27, 27, 255),
        anim_speed / 3
    );

    it_anim->second.check_rect_color = ImLerp(
        it_anim->second.check_rect_color,
        hovered ? gui.checkboxstrokeactive : gui.checkboxstroke,
        anim_speed / 3
    );

    // Draw square background with rounded borders for the color picker
    const float rounding = 5.0f; // Rounded corners radius

    // Draw background
    window->DrawList->AddRectFilled(
        bb.Min,
        bb.Max,
        GetColorU32(it_anim->second.check_color),
        rounding
    );

    // Draw border
    window->DrawList->AddRect(
        bb.Min,
        bb.Max,
        GetColorU32(it_anim->second.check_rect_color),
        rounding,
        0,
        1.5f
    );

    // Draw inner color square with the selected color
    window->DrawList->AddRectFilled(
        bb.Min + ImVec2(4.0f, 4.0f),
        bb.Max - ImVec2(4.0f, 4.0f),
        ImGui::ColorConvertFloat4ToU32(col_rgb),
        rounding - 1.0f
    );

    // Add shadow for the color square with reduced intensity
    window->DrawList->AddShadowRect(
        bb.Min + ImVec2(4.0f, 4.0f),
        bb.Max - ImVec2(4.0f, 4.0f),
        ImGui::ColorConvertFloat4ToU32(col_rgb),
        30.f,  // Reduced shadow intensity from 70 to 30
        ImVec2(0, 0),
        rounding - 1.0f
    );

    // Add tooltip if not disabled
    if (!(flags & ImGuiColorEditFlags_NoTooltip) && hovered) {
        ImGui::BeginTooltip();
        ImGui::ColorButton("##preview", col, (flags & (ImGuiColorEditFlags_AlphaPreview | ImGuiColorEditFlags_AlphaPreviewHalf)) | ImGuiColorEditFlags_NoTooltip, ImVec2(20, 20));
        ImGui::SameLine();
        ImGui::Text("Color: %.2f, %.2f, %.2f, %.2f", col.x, col.y, col.z, col.w);
        ImGui::EndTooltip();

        // Don't set gui.picker_active on hover - only when actually clicked
    }

    return pressed;
}

struct toggle_state {
    ImVec4 background;
    float smooth_swap, alpha_line, line_size;
};

bool nav_elements::ToggleButton(const char* first_text, const char* second_text, bool* v, const ImVec2& size_arg) {
    ImGuiWindow* window = GetCurrentWindow();

    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(first_text);
    const ImVec2 label_size = CalcTextSize(first_text, NULL, true), pos = window->DC.CursorPos;

    ImVec2 size = CalcItemSize(size_arg, label_size.x, label_size.y);

    const ImRect bb(pos, pos + size);

    const ImRect dark_bb(bb.Min, bb.Min + ImVec2(size.x / 2, size.y));
    const ImRect white_bb(bb.Min + ImVec2(size.x / 2, 0.f), bb.Max);

    ItemSize(size, 0.f);
    if (!ItemAdd(bb, id, &bb)) return false;

    bool hovered, held, pressed = ButtonBehavior(bb, id, &hovered, &held, NULL);

    static std::map<ImGuiID, toggle_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, toggle_state() });
        it_anim = anim.find(id);
    }

    if (pressed) {
        // Toggle the value
        *v = !*v;

        // Mark the item as edited to ensure changes are saved
        MarkItemEdited(id);

        // Handle hotkey mode toggle
        if (strcmp(first_text, "Hold") == 0 && strcmp(second_text, "Toggle") == 0) {
            // When v=true, it means Toggle mode; when v=false, it means Hold mode
            bool isToggleMode = *v;

            // Extract the feature name from the popup window name
            std::string featureName = "";
            ImGuiWindow* parentWindow = ImGui::GetCurrentWindow();
            if (parentWindow && parentWindow->Name) {
                std::string windowName = parentWindow->Name;

                // Extract feature name from the popup window name
                size_t colorPickerPos = windowName.find("##ColorPickerPopup_");
                if (colorPickerPos != std::string::npos) {
                    featureName = windowName.substr(colorPickerPos + 18); // Length of "##ColorPickerPopup_"

                    // Get the current hotkey for this feature
                    auto hotkey = HotkeySystem::GetHotkey(featureName);
                    if (hotkey) {
                        int keyCode = hotkey->keyCode;

                        // Update the hotkey with the new mode (directly use the toggle mode)
                        HotkeySystem::UpdateHotkey(featureName, keyCode, isToggleMode);

                        // Verify the mode was updated correctly
                        bool keysStructMode = HotkeySystem::GetModeFromKeysStruct(featureName);
                        bool settingsStructMode = HotkeySystem::GetModeFromSettingsStruct(featureName);

                        // If there's a mismatch, force another update
                        if (hotkey->toggleMode != isToggleMode || keysStructMode != isToggleMode || settingsStructMode != isToggleMode) {
                            // Force update the Keys struct field directly
                            HotkeySystem::UpdateKeysModeField(featureName, isToggleMode);

                            // Update the binding
                            hotkey->toggleMode = isToggleMode;

                            // Update Settings struct
                            Settings.Keys.Keys = Keys;

                            // Force save settings
                            SettingsHelper::SaveSettings();

                            // Verify mode consistency
                            HotkeySystem::VerifyModeConsistency();

                            // Update the toggle button value to match
                            *v = isToggleMode;
                        }
                    }
                }
            }

            // Force save settings to ensure the mode change is persisted
            try {
                if (SettingsHelper::SaveSettings()) {
                    // Verify the mode was saved correctly by loading it again
                    SettingsHelper::LoadSettings();

                    // Verify the feature name's mode specifically
                    if (!featureName.empty()) {
                        bool savedMode = HotkeySystem::GetModeFromKeysStruct(featureName);

                        // If there's still a mismatch after reload, force one more update
                        if (savedMode != isToggleMode) {
                            // Get the current hotkey for this feature
                            auto hotkey = HotkeySystem::GetHotkey(featureName);
                            if (hotkey) {
                                int keyCode = hotkey->keyCode;

                                // Update the hotkey with the correct mode
                                HotkeySystem::UpdateHotkey(featureName, keyCode, isToggleMode);
                                SettingsHelper::SaveSettings();

                                // Update the toggle button value to match
                                *v = isToggleMode;
                            }
                        }
                    }
                }
            } catch (const std::exception& e) {
                // Handle exception silently
            }
        }
    }

    // Update animation based on the current value
    // When v=true, show Toggle mode (right side); when v=false, show Hold mode (left side)
    it_anim->second.smooth_swap = ImLerp(it_anim->second.smooth_swap, *v ? size.x / 2 : 0.f, g.IO.DeltaTime * 12.f);

    // Draw the background
    GetWindowDrawList()->AddRectFilled(bb.Min, bb.Max, func.GetColorWithAlpha(gui.second, 1.f - (1.f - style.Alpha)), c::page::rounding);

    // Draw the active side (Hold or Toggle)
    GetWindowDrawList()->AddRectFilled(
        bb.Min + ImVec2(it_anim->second.smooth_swap, 0.f),
        ImVec2(bb.Min.x + size.x / 2 + it_anim->second.smooth_swap, bb.Max.y),
        func.GetColorWithAlpha(gui.main, 0.5f - (1.f - style.Alpha)),
        c::page::rounding
    );

    // Draw the text labels
    GetWindowDrawList()->AddText(func.CalcTextPos(dark_bb.Min, dark_bb.Max, first_text), func.GetColorWithAlpha(gui.main, 1.f - (1.f - style.Alpha)), first_text);
    GetWindowDrawList()->AddText(func.CalcTextPos(white_bb.Min, white_bb.Max, second_text), func.GetColorWithAlpha(gui.main, 1.f - (1.f - style.Alpha)), second_text);

    return pressed;
}




struct button_state {
    ImVec4 background, text;
};

bool nav_elements::Button(const char* label, const ImVec2& size_arg, ImColor color) {
    ImGuiWindow* window = GetCurrentWindow();

    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);
    const ImVec2 label_size = CalcTextSize(label, NULL, true), pos = window->DC.CursorPos;

    ImVec2 size = CalcItemSize(size_arg, label_size.x, label_size.y);

    const ImRect bb(pos, pos + size);

    ItemSize(size, 0.f);
    if (!ItemAdd(bb, id)) return false;

    bool hovered, held, pressed = ButtonBehavior(bb, id, &hovered, &held, NULL);

    static std::map<ImGuiID, button_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, button_state() });
        it_anim = anim.find(id);
    }

    bool color_changed = color != func.ImColorToImVec4(gui.main);

    it_anim->second.text = ImLerp(it_anim->second.text,
        IsItemActive() || hovered && color_changed ? color : !color_changed ? gui.text[0] : color_changed ? func.GetColorWithAlpha(color, 0.7f) : gui.text[1],
        g.IO.DeltaTime * 6.f);

    const float anim_speed = GetAnimSpeed();

    it_anim->second.background = ImLerp(
        it_anim->second.background,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
        anim_speed
    );
    window->DrawList->AddRectFilled(bb.Min, bb.Max, GetColorU32(it_anim->second.background), 3.0f);


    GetWindowDrawList()->AddRectFilled(bb.Min, bb.Max, GetColorU32(it_anim->second.background), c::page::rounding);

    PushClipRect(bb.Min, bb.Max, true);

    GetWindowDrawList()->AddText(ImVec2(bb.Min.x + (size_arg.x - CalcTextSize(label).x) / 2, bb.Max.y - CalcTextSize(label).y - (size.y - CalcTextSize(label).y) / 2), GetColorU32(it_anim->second.text), label);

    PopClipRect();

    return pressed;
}

// Button animation state structure matching tab design
struct ButtonAnimState {
    float element_opacity = 0.0f;
    float text_opacity = 0.3f;
    ImVec4 icon_color = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
};

// Map to store button animation states
static std::map<ImGuiID, ButtonAnimState> button_anim_map;

// Custom button function with icon and animation
bool nav_elements::IconButton(const char* label, const char* icon, const ImVec2& size_arg, ImColor color, bool selected) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);

    // Calculate icon and text sizes - use smaller icons for better proportions
    ImVec2 icon_size(0, 0);
    if (icon && *icon) {
        ImGui::PushFont(iconsSmall); // Use smaller icons for better proportions
        icon_size = ImGui::CalcTextSize(icon);
        ImGui::PopFont();
    }

    ImVec2 text_size(0, 0);
    bool has_text = label && strlen(label) > 0;
    if (has_text) {
        text_size = ImGui::CalcTextSize(label);
    }

    // Dynamic spacing calculation
    float left_padding = 8.0f;
    float icon_text_spacing = (icon_size.x > 0 && has_text) ? 6.0f : 0.0f;
    float right_padding = 8.0f;

    // Calculate total content width
    float content_width = left_padding + icon_size.x + icon_text_spacing + text_size.x + right_padding;
    float content_height = ImMax(icon_size.y, text_size.y);

    // Calculate final size with dynamic spacing
    ImVec2 size = ImGui::CalcItemSize(size_arg,
        ImMax(content_width, 60.0f), // Minimum width
        ImMax(content_height + 8.0f, 28.0f)  // Minimum height with padding
    );

    const ImVec2 pos = window->DC.CursorPos;
    const ImRect rect(pos, pos + size);

    ImGui::ItemSize(rect, style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || selected) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
    }

    // Animation state
    auto it_anim = button_anim_map.find(id);
    if (it_anim == button_anim_map.end()) {
        button_anim_map.insert({ id, ButtonAnimState() });
        it_anim = button_anim_map.find(id);
    }

    // Animation system matching tab design exactly
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
        (selected ? 0.04f : hovered ? 0.01f : 0.0f),
        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    it_anim->second.text_opacity = ImLerp(it_anim->second.text_opacity,
        (selected ? 1.0f : hovered ? 0.5f : 0.3f),
        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Icon color animation matching tab design
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        selected || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f),
        GetAnimSpeed()
    );

    // Draw background rectangle exactly like tabs
    window->DrawList->AddRectFilled(rect.Min, rect.Max,
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Draw border exactly like tabs
    ImVec4 border_color = selected || hovered ?
        ImVec4(ImColor(255, 255, 255, 10)) :
        ImVec4(ImColor(255, 255, 255, 10));
    window->DrawList->AddRect(rect.Min, rect.Max,
        ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

    // Calculate positions with proper dynamic spacing and centering
    ImVec2 icon_pos(0, 0);
    ImVec2 text_pos(0, 0);

    if (has_text) {
        // Position icon on the left with proper padding
        icon_pos = ImVec2(
            rect.Min.x + left_padding,
            rect.Min.y + (rect.GetHeight() - icon_size.y) * 0.5f + 1.0f // Perfect vertical centering
        );

        // Position text after icon with proper spacing
        text_pos = ImVec2(
            icon_pos.x + icon_size.x + icon_text_spacing,
            rect.Min.y + (rect.GetHeight() - text_size.y) * 0.5f
        );
    } else {
        // Center icon alone perfectly
        icon_pos = ImVec2(
            rect.Min.x + (rect.GetWidth() - icon_size.x) * 0.5f,
            rect.Min.y + (rect.GetHeight() - icon_size.y) * 0.5f + 1.0f // Perfect vertical centering
        );
    }

    // Draw icon shadow with proper positioning
    if (icon && *icon) {
        ImVec2 icon_center = ImVec2(
            icon_pos.x + icon_size.x * 0.5f,
            icon_pos.y + icon_size.y * 0.5f
        );

        window->DrawList->AddShadowCircle(
            icon_center,
            6.f, // Smaller shadow for smaller icons
            ImGui::GetColorU32(it_anim->second.icon_color),
            30, // Reduced shadow intensity
            ImVec2(0, 0),
            0,
            360
        );
    }

    // Draw icon with smaller font
    if (icon && *icon) {
        ImGui::PushFont(iconsSmall); // Use smaller icons for better proportions
        window->DrawList->AddText(
            icon_pos,
            ImGui::GetColorU32(it_anim->second.icon_color),
            icon
        );
        ImGui::PopFont();
    }

    // Draw text exactly like tabs
    if (has_text) {
        window->DrawList->AddText(
            text_pos,
            ImColor(1.0f, 1.0f, 1.0f, it_anim->second.text_opacity),
            label
        );
    }

    return pressed;
}
// Icon-only button variant
bool nav_elements::IconOnlyButton(const char* icon, const ImVec2& size_arg, ImColor color, bool selected) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(icon);

    // Calculate icon size with smaller font for better proportions
    ImGui::PushFont(iconsSmall);
    const ImVec2 icon_size = ImGui::CalcTextSize(icon);
    ImGui::PopFont();

    // Dynamic sizing for icon-only buttons
    float padding = 8.0f;
    ImVec2 pos = window->DC.CursorPos;
    ImVec2 size = ImGui::CalcItemSize(size_arg,
        ImMax(icon_size.x + padding * 2.0f, 32.0f), // Minimum width
        ImMax(icon_size.y + padding, 28.0f)  // Minimum height
    );

    const ImRect rect(pos, ImVec2(pos.x + size.x, pos.y + size.y));
    ImGui::ItemSize(rect, style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || selected) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
    }

    // Get or create animation state
    auto it_anim = button_anim_map.find(id);
    if (it_anim == button_anim_map.end()) {
        button_anim_map.insert({ id, ButtonAnimState() });
        it_anim = button_anim_map.find(id);
    }

    // Animation system matching tab design exactly
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
        (selected ? 0.04f : hovered ? 0.01f : 0.0f),
        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Icon color animation matching tab design
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        selected || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f),
        GetAnimSpeed()
    );

    // Draw background rectangle exactly like tabs
    window->DrawList->AddRectFilled(rect.Min, rect.Max,
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Draw border exactly like tabs
    ImVec4 border_color = selected || hovered ?
        ImVec4(ImColor(255, 255, 255, 10)) :
        ImVec4(ImColor(255, 255, 255, 10));
    window->DrawList->AddRect(rect.Min, rect.Max,
        ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

    // Center icon perfectly with proper vertical alignment
    ImVec2 icon_pos = ImVec2(
        rect.Min.x + (rect.GetWidth() - icon_size.x) * 0.5f,
        rect.Min.y + (rect.GetHeight() - icon_size.y) * 0.5f + 1.0f // Perfect vertical centering
    );

    // Draw icon shadow with proper positioning
    ImVec2 icon_center = ImVec2(
        icon_pos.x + icon_size.x * 0.5f,
        icon_pos.y + icon_size.y * 0.5f
    );

    window->DrawList->AddShadowCircle(
        icon_center,
        6.f, // Smaller shadow for smaller icons
        ImGui::GetColorU32(it_anim->second.icon_color),
        30, // Reduced shadow intensity
        ImVec2(0, 0),
        0,
        360
    );

    // Draw icon with smaller font
    ImGui::PushFont(iconsSmall); // Use smaller icons for better proportions
    window->DrawList->AddText(
        icon_pos,
        ImGui::GetColorU32(it_anim->second.icon_color),
        icon
    );
    ImGui::PopFont();

    return pressed;
}

void nav_elements::BeginGroup() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;

    g.GroupStack.resize(g.GroupStack.Size + 1);
    ImGuiGroupData& group_data = g.GroupStack.back();
    group_data.WindowID = window->ID;
    group_data.BackupCursorPos = window->DC.CursorPos;
    group_data.BackupCursorMaxPos = window->DC.CursorMaxPos;
    group_data.BackupIndent = window->DC.Indent;
    group_data.BackupGroupOffset = window->DC.GroupOffset;
    group_data.BackupCurrLineSize = window->DC.CurrLineSize;
    group_data.BackupCurrLineTextBaseOffset = window->DC.CurrLineTextBaseOffset;
    group_data.BackupActiveIdIsAlive = g.ActiveIdIsAlive;
    group_data.BackupHoveredIdIsAlive = g.HoveredId != 0;
    group_data.BackupActiveIdPreviousFrameIsAlive = g.ActiveIdPreviousFrameIsAlive;
    group_data.EmitItem = true;

    window->DC.GroupOffset.x = window->DC.CursorPos.x - window->Pos.x - window->DC.ColumnsOffset.x;
    window->DC.Indent = window->DC.GroupOffset;
    window->DC.CursorMaxPos = window->DC.CursorPos;
    window->DC.CurrLineSize = ImVec2(0.0f, 0.0f);
    if (g.LogEnabled) g.LogLinePosY = -FLT_MAX;
}

void nav_elements::EndGroup() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;
    IM_ASSERT(g.GroupStack.Size > 0);

    ImGuiGroupData& group_data = g.GroupStack.back();
    IM_ASSERT(group_data.WindowID == window->ID);

    if (window->DC.IsSetPos) ErrorCheckUsingSetCursorPosToExtendParentBoundaries();

    ImRect group_bb(group_data.BackupCursorPos, ImMax(window->DC.CursorMaxPos, group_data.BackupCursorPos));

    window->DC.CursorPos = group_data.BackupCursorPos;
    window->DC.CursorMaxPos = ImMax(group_data.BackupCursorMaxPos, window->DC.CursorMaxPos);
    window->DC.Indent = group_data.BackupIndent;
    window->DC.GroupOffset = group_data.BackupGroupOffset;
    window->DC.CurrLineSize = group_data.BackupCurrLineSize;
    window->DC.CurrLineTextBaseOffset = group_data.BackupCurrLineTextBaseOffset;
    if (g.LogEnabled) g.LogLinePosY = -FLT_MAX;

    if (!group_data.EmitItem) {
        g.GroupStack.pop_back();
        return;
    }

    window->DC.CurrLineTextBaseOffset = ImMax(window->DC.PrevLineTextBaseOffset,
        group_data.BackupCurrLineTextBaseOffset);
    ItemSize(group_bb.GetSize());
    ItemAdd(group_bb, 0, NULL, ImGuiItemFlags_NoTabStop);

    const bool group_contains_curr_active_id =
        (group_data.BackupActiveIdIsAlive != g.ActiveId) && (g.ActiveIdIsAlive == g.ActiveId) && g.ActiveId;
    const bool group_contains_prev_active_id =
        (group_data.BackupActiveIdPreviousFrameIsAlive == false) && (g.ActiveIdPreviousFrameIsAlive == true);
    if (group_contains_curr_active_id) g.LastItemData.ID = g.ActiveId;
    else if (group_contains_prev_active_id) g.LastItemData.ID = g.ActiveIdPreviousFrame;
    g.LastItemData.Rect = group_bb;

    const bool group_contains_curr_hovered_id = (group_data.BackupHoveredIdIsAlive == false) && g.HoveredId != 0;
    if (group_contains_curr_hovered_id) g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_HoveredWindow;

    if (group_contains_curr_active_id && g.ActiveIdHasBeenEditedThisFrame)
        g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_Edited;

    g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_HasDeactivated;
    if (group_contains_prev_active_id && g.ActiveId != g.ActiveIdPreviousFrame)
        g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_Deactivated;

    g.GroupStack.pop_back();
}

const ImWchar* nav_elements::GetGlyphRangesChinese()
{
    static const ImWchar ranges[] =
    {
        0x0020, 0x00FF, // Basic Latin + Latin Supplement
        0x3000, 0x30FF, // Punctuations, Hiragana, Katakana
        0x31F0, 0x31FF, // Katakana Phonetic Extensions
        0xFF00, 0xFFEF, // Half-width characters
        0x4e00, 0x9FAF, // CJK Ideograms
        0,
    };
    return &ranges[0];
}

struct begin_state {
    ImVec4 background, text, outline, rect_color, combo_col, preview_col, icon_color;
    float alpha = 0.f, shadow_opticaly;
    bool opened_combo = false, hovered = false, want_close, window_hovered;
    float arrow_roll, preview_value_size;
    ImVec2 combo_size;
    ImGuiWindow* window;
    ImVec2 rotation_window;
    char search[64];
};

bool nav_elements::BeginCombo(const char* label, const char* preview_value, int val, const char* description, ImGuiComboFlags flags) {
    LOG_DEBUG("nav_elements::BeginCombo - Label: %s, Preview: %s, Val: %d",
              label, preview_value ? preview_value : "NULL", val);

    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = GetCurrentWindow();

    g.NextWindowData.ClearFlags();
    if (window->SkipItems) {
        LOG_DEBUG("nav_elements::BeginCombo - Window skip items, returning false");
        return false;
    }

    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);

    const float w = ((GetContentRegionMax().x - style.WindowPadding.x));

    const ImRect bb(window->DC.CursorPos, window->DC.CursorPos + ImVec2(w, 60));
    std::string label_str = label;

    ImVec2 check_size = ImVec2(50, 26);
    const float check_spacing = (bb.GetSize().y - check_size.y) / 2;

    static std::map<ImGuiID, begin_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, begin_state() });
        it_anim = anim.find(id);
    }

    //PushFont(fonts["semibold"][2]);
    const ImRect combo_bb(
        bb.Max - ImVec2(check_spacing + it_anim->second.preview_value_size, check_spacing) - check_size,
        bb.Max - ImVec2(check_spacing, check_spacing));
    //PopFont();

    ItemSize(bb, 0.f);

    if (!ItemAdd(bb, id, &bb)) return false;

    bool hovered, held, pressed = ButtonBehavior(bb, id, &hovered, &held);

    //PushFont(fonts["regular"][3]);
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    //PopFont();

    const ImVec2 label_size = CalcTextSize(label, NULL, true);

    const ImVec2 label_pos = ImVec2(bb.Min.x + 60.f + (bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        bb.GetCenter().y - (label_size.y + description_size.y) / 2);

    it_anim->second.hovered = ImGui::IsMouseHoveringRect(combo_bb.Min, combo_bb.Max, true);

    if (hovered && g.IO.MouseClicked[0] && !it_anim->second.want_close) {
        LOG_DEBUG("nav_elements::BeginCombo - Opening combo popup");
        it_anim->second.opened_combo = true;
    }

    if (it_anim->second.opened_combo && g.IO.MouseClicked[0] && !it_anim->second.hovered &&
        !it_anim->second.want_close && !it_anim->second.window_hovered)
        it_anim->second.want_close = true;

    if (it_anim->second.want_close && it_anim->second.combo_size.y < 20.f && it_anim->second.opened_combo) {
        LOG_DEBUG("nav_elements::BeginCombo - Not opened or already closed, returning false");
        return false;
    }

    //PushFont(fonts["semibold"][2]);
    it_anim->second.preview_value_size = ImLerp(it_anim->second.preview_value_size,
        CalcTextSize(preview_value).x,
        GetAnimSpeed());
    //PopFont();

    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color,
        hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f),
        GetAnimSpeed());
    it_anim->second.arrow_roll = ImLerp(it_anim->second.arrow_roll,
        it_anim->second.opened_combo ? -1.f : 1.f,
        g.IO.DeltaTime * 6.f);

    it_anim->second.preview_col = ImLerp(it_anim->second.preview_col,
        it_anim->second.opened_combo ? gui.main : gui.text[1],
        g.IO.DeltaTime * 6.f);
    it_anim->second.combo_col = ImLerp(it_anim->second.combo_col,
        it_anim->second.opened_combo ? func.GetColorWithAlpha(gui.main, 0.3f)
        : gui.second,
        g.IO.DeltaTime * 6.f);

    ImVec2 size_expected = {
    0.f,
    35.f
    };

    it_anim->second.combo_size = ImLerp(it_anim->second.combo_size,
        it_anim->second.opened_combo && !it_anim->second.want_close ? size_expected
        : ImVec2(0.f, 0.f),
        g.IO.DeltaTime * 12.f);

    std::string label_string = label;

    if (it_anim->second.opened_combo)
        EasingAnimationV2("combo_rotation" + label_string,
            &it_anim->second.rotation_window,
            ImVec2(IM_PI / 2, 0.f),
            0.01f,
            imanim::EasingCurve::Type::InOutBack,
            -1);
    else
        EasingAnimationV2("combo_rotation" + label_string,
            &it_anim->second.rotation_window,
            ImVec2(IM_PI / 2 + 0.1f, 0.f),
            0.6f,
            imanim::EasingCurve::Type::InOutBack,
            -1);


    const float anim_speed = GetAnimSpeed();

    it_anim->second.background = ImLerp(
        it_anim->second.background,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
        anim_speed
    );
    window->DrawList->AddRectFilled(bb.Min, bb.Max, GetColorU32(it_anim->second.background), 3.0f);

    GetWindowDrawList()->AddRectFilled(bb.Min,
        bb.Min + ImVec2(bb.GetSize().y, +bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color),
        style.FrameRounding,
        ImDrawFlags_RoundCornersLeft);

    GetWindowDrawList()->AddRectFilled(combo_bb.Min,
        combo_bb.Max,
        GetColorU32(it_anim->second.combo_col),
        style.FrameRounding);

    /*   PushFont(fonts["semibold"][2]);
       GetWindowDrawList()->AddText(fonts["semibold"][2],
           fonts["semibold"][2]->FontSize,
           ImVec2(combo_bb.Min.x + 10,
               func.CalcTextPos(combo_bb.Min,
                   combo_bb.Max,
                   preview_value,
                   fonts["semibold"][2]).y),
           GetColorU32(it_anim->second.preview_col),
           preview_value);
       PopFont();*/

    window->DrawList->AddShadowCircle(bb.Min + ImVec2(bb.GetSize().y / 2, bb.GetSize().y / 2),
        9.f,
        GetColorU32(it_anim->second.icon_color),
        40,
        ImVec2(0, 0),
        0,
        360);

    //PushFont(fonts["semibold"][3]);
    GetWindowDrawList()->AddText(
        ImVec2(combo_bb.Max.x - CalcTextSize(ICON_DOWN_LINE).x - 4.f, combo_bb.GetCenter().y - CalcTextSize(ICON_DOWN_LINE).y / 2),
        gui.main,
        ICON_DOWN_LINE);
    //PopFont();


    GetWindowDrawList()->AddText(label_pos, gui.text[0], label);

    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color,
        hovered ? func.GetDarkColor(gui.main) : gui.main,
        GetAnimSpeed());

    //PushFont(fonts["regular"][3]);
    GetWindowDrawList()->AddText(label_pos + ImVec2(0.f, label_size.y),
        gui.text[1],
        getStringBeforeCaret(description) != nullptr ? getStringBeforeCaret(description)
        : description);
    //PopFont();

   /* if (findSymbolAfterCaret(description) != nullptr) {
        PushFont(fonts["semibold"][6]);
        GetWindowDrawList()->AddText(func.CalcTextPos(bb.Min,
            bb.Min + ImVec2(bb.GetSize().y, bb.GetSize().y),
            findSymbolAfterCaret(description),
            fonts["semibold"][6]),
            GetColorU32(it_anim->second.icon_color),
            findSymbolAfterCaret(description));
        PopFont();
    }*/

    if (!IsRectVisible(bb.Min, bb.Max + ImVec2(0, 2))) {
        it_anim->second.want_close = true;
        it_anim->second.combo_size.y = 0.f;
    }

    if (!it_anim->second.opened_combo && it_anim->second.combo_size.y < 5.f) return false;


    ImGui::SetNextWindowPos(ImVec2(combo_bb.Min.x, combo_bb.Max.y + 5));

    ImGuiWindowFlags window_flags =
        ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoMove;


    PushStyleColor(ImGuiCol_WindowBg, func.ImColorToImVec4(gui.second));
    PushStyleVar(ImGuiStyleVar_WindowRounding, c::elements::rounding);
    PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
    PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(2, 2));
    PushStyleVar(ImGuiStyleVar_ScrollbarSize, 3.f);
    ImGui::SetNextWindowFocus();


    bool ret = Begin(label, NULL, window_flags);

    it_anim->second.window = ImGui::GetCurrentWindow();

    it_anim->second.window_hovered = ImGui::IsWindowHovered();

    PopStyleVar(4);
    PopStyleColor(1);

    it_anim->second.hovered = IsWindowHovered();

    LOG_DEBUG("nav_elements::BeginCombo - Combo window created: %p", it_anim->second.window);

    return true;
}

void nav_elements::EndCombo() {
    LOG_DEBUG("nav_elements::EndCombo - Ending combo popup");
    gui.combo_window = ImGui::GetCurrentWindow();
    End();
}

void nav_elements::MultiCombo(const char* label, bool variable[], const char* labels[], int count) {
    ImGuiContext& g = *GImGui;

    std::string preview = "None";

    for (auto i = 0, j = 0; i < count; i++) {
        if (variable[i]) {
            if (j)
                preview += (", ") + (std::string)labels[i];
            else
                preview = labels[i];

            j++;
        }
    }

    if (BeginCombo(label, preview.c_str(), count)) {
        for (auto i = 0; i < count; i++) {
            PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(15, 15));
            PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
            nav_elements::Selectable(labels[i], &variable[i], ImGuiSelectableFlags_DontClosePopups);
            PopStyleVar(2);
        }
        End();
    }

    preview = ("None");
}

bool nav_elements::BeginComboPreview() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;
    ImGuiComboPreviewData* preview_data = &g.ComboPreviewData;

    if (window->SkipItems || !(g.LastItemData.StatusFlags & ImGuiItemStatusFlags_Visible)) return false;

    IM_ASSERT(g.LastItemData.Rect.Min.x == preview_data->PreviewRect.Min.x &&
        g.LastItemData.Rect.Min.y == preview_data->PreviewRect.Min.y);

    if (!window->ClipRect.Overlaps(preview_data->PreviewRect)) return false;

    preview_data->BackupCursorPos = window->DC.CursorPos;
    preview_data->BackupCursorMaxPos = window->DC.CursorMaxPos;
    preview_data->BackupCursorPosPrevLine = window->DC.CursorPosPrevLine;
    preview_data->BackupPrevLineTextBaseOffset = window->DC.PrevLineTextBaseOffset;
    preview_data->BackupLayout = window->DC.LayoutType;
    window->DC.CursorPos = preview_data->PreviewRect.Min + g.Style.FramePadding;
    window->DC.CursorMaxPos = window->DC.CursorPos;
    window->DC.LayoutType = ImGuiLayoutType_Horizontal;
    window->DC.IsSameLine = false;
    PushClipRect(preview_data->PreviewRect.Min, preview_data->PreviewRect.Max, true);

    return true;
}

void nav_elements::EndComboPreview() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;
    ImGuiComboPreviewData* preview_data = &g.ComboPreviewData;

    ImDrawList* draw_list = window->DrawList;
    if (window->DC.CursorMaxPos.x < preview_data->PreviewRect.Max.x &&
        window->DC.CursorMaxPos.y < preview_data->PreviewRect.Max.y)
        if (draw_list->CmdBuffer.Size > 1) {
            draw_list->_CmdHeader.ClipRect = draw_list->CmdBuffer[draw_list->CmdBuffer.Size -
                1].ClipRect = draw_list->CmdBuffer[
                    draw_list->CmdBuffer.Size - 2].ClipRect;
            draw_list->_TryMergeDrawCmds();
        }
    PopClipRect();
    window->DC.CursorPos = preview_data->BackupCursorPos;
    window->DC.CursorMaxPos = ImMax(window->DC.CursorMaxPos, preview_data->BackupCursorMaxPos);
    window->DC.CursorPosPrevLine = preview_data->BackupCursorPosPrevLine;
    window->DC.PrevLineTextBaseOffset = preview_data->BackupPrevLineTextBaseOffset;
    window->DC.LayoutType = preview_data->BackupLayout;
    window->DC.IsSameLine = false;
    preview_data->PreviewRect = ImRect();
}

static const char* Items_ArrayGetter(void* data, int idx) {
    const char* const* items = (const char* const*)data;
    return items[idx];
}

static const char* Items_SingleStringGetter(void* data, int idx) {
    const char* items_separated_by_zeros = (const char*)data;
    int items_count = 0;
    const char* p = items_separated_by_zeros;
    while (*p) {
        if (idx == items_count)
            break;
        p += strlen(p) + 1;
        items_count++;
    }
    return *p ? p : NULL;
}
static float CalcMaxPopupHeightFromItemCount(int items_count) {
    ImGuiContext& g = *GImGui;
    if (items_count <= 0)
        return FLT_MAX;
    return (g.FontSize + g.Style.ItemSpacing.y) * items_count - g.Style.ItemSpacing.y + (g.Style.WindowPadding.y * 2);
}
bool nav_elements::Combo(const char* label,
    int* current_item,
    const char* (*getter)(void* user_data, int idx),
    void* user_data,
    int items_count,
    const char* description,
    int popup_max_height_in_items) {


    ImGuiContext& g = *GImGui;

    const char* preview_value = NULL;
    if (*current_item >= 0 && *current_item < items_count) {
        preview_value = getter(user_data, *current_item);
    } else {
    }

    if (popup_max_height_in_items != -1 && !(g.NextWindowData.Flags & ImGuiNextWindowDataFlags_HasSizeConstraint)) {
        SetNextWindowSizeConstraints(ImVec2(0, 0),
            ImVec2(FLT_MAX, CalcMaxPopupHeightFromItemCount(popup_max_height_in_items)));
    }

    if (!nav_elements::BeginCombo(label, preview_value, items_count, description, ImGuiComboFlags_None)) {
        return false;
    }

    bool value_changed = false;
    PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(15, 15));
    for (int i = 0; i < items_count; i++) {
        const char* item_text = getter(user_data, i);
        if (item_text == NULL)
            item_text = "*Unknown item*";

        PushID(i);
        const bool item_selected = (i == *current_item);
        if (nav_elements::SelectableEx(item_text, item_selected) && *current_item != i) {
            value_changed = true;
            *current_item = i;
        }
        if (item_selected)
            SetItemDefaultFocus();
        PopID();
    }
    PopStyleVar();

    EndCombo();

    if (value_changed) {
        MarkItemEdited(g.LastItemData.ID);
    }

    return value_changed;
}

bool nav_elements::Combo(const char* label,
    int* current_item,
    const char* const items[],
    int items_count,
    const char* description,
    int height_in_items) {
    const bool value_changed = Combo(label,
        current_item,
        Items_ArrayGetter,
        (void*)items,
        items_count,
        description,
        height_in_items);
    return value_changed;
}

bool nav_elements::Combo(const char* label,
    int* current_item,
    const char* items_separated_by_zeros,
    const char* description,
    int height_in_items) {
    int items_count = 0;
    const char* p = items_separated_by_zeros;
    while (*p) {
        p += strlen(p) + 1;
        items_count++;
    }
    bool value_changed = Combo(label,
        current_item,
        Items_SingleStringGetter,
        (void*)items_separated_by_zeros,
        items_count,
        description,
        height_in_items);
    return value_changed;
}

struct select_state {
    ImVec4 text, background, line;
    float circle_radius, text_offset;
};

bool nav_elements::SelectableEx(const char* label, bool selected, ImGuiSelectableFlags flags, const ImVec2& size_arg) {
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;

    ImGuiID id = window->GetID(label);
    ImVec2 label_size = CalcTextSize(label, NULL, true);
    ImVec2 size(size_arg.x != 0.0f ? size_arg.x : label_size.x, size_arg.y != 0.0f ? size_arg.y : label_size.y);
    ImVec2 pos = window->DC.CursorPos;
    pos.y += window->DC.CurrLineTextBaseOffset;
    ItemSize(size, 0.0f);

    const bool span_all_columns = (flags & ImGuiSelectableFlags_SpanAllColumns) != 0;
    const float min_x = span_all_columns ? window->ParentWorkRect.Min.x : pos.x;
    const float max_x = span_all_columns ? window->ParentWorkRect.Max.x : window->WorkRect.Max.x;
    if (size_arg.x == 0.0f || (flags & ImGuiSelectableFlags_SpanAvailWidth))
        size.x = ImMax(label_size.x,
            max_x - min_x);

    const ImVec2 text_min = pos;
    const ImVec2 text_max(min_x + size.x, pos.y + size.y);

    ImRect bb(min_x, pos.y, text_max.x, text_max.y);
    if ((flags & ImGuiSelectableFlags_NoPadWithHalfSpacing) == 0) {
        const float spacing_x = span_all_columns ? 0.0f : style.ItemSpacing.x;
        const float spacing_y = style.ItemSpacing.y;
        const float spacing_L = IM_TRUNC(spacing_x * 0.50f);
        const float spacing_U = IM_TRUNC(spacing_y * 0.50f);
        bb.Min.x -= spacing_L;
        bb.Min.y -= spacing_U;
        bb.Max.x += (spacing_x - spacing_L);
        bb.Max.y += (spacing_y - spacing_U);
    }

    const float backup_clip_rect_min_x = window->ClipRect.Min.x;
    const float backup_clip_rect_max_x = window->ClipRect.Max.x;
    if (span_all_columns) {
        window->ClipRect.Min.x = window->ParentWorkRect.Min.x;
        window->ClipRect.Max.x = window->ParentWorkRect.Max.x;
    }

    const bool disabled_item = (flags & ImGuiSelectableFlags_Disabled) != 0;
    const bool item_add = ItemAdd(bb, id, NULL, disabled_item ? ImGuiItemFlags_Disabled : ImGuiItemFlags_None);
    if (span_all_columns) {
        window->ClipRect.Min.x = backup_clip_rect_min_x;
        window->ClipRect.Max.x = backup_clip_rect_max_x;
    }

    if (!item_add) return false;

    const bool disabled_global = (g.CurrentItemFlags & ImGuiItemFlags_Disabled) != 0;
    if (disabled_item && !disabled_global) BeginDisabled();

    if (span_all_columns && window->DC.CurrentColumns) PushColumnsBackground();
    else if (span_all_columns && g.CurrentTable) TablePushBackgroundChannel();

    ImGuiButtonFlags button_flags = 0;
    if (flags & ImGuiSelectableFlags_NoHoldingActiveID) { button_flags |= ImGuiButtonFlags_NoHoldingActiveId; }
    if (flags & ImGuiSelectableFlags_NoSetKeyOwner) { button_flags |= ImGuiButtonFlags_NoSetKeyOwner; }
    if (flags & ImGuiSelectableFlags_SelectOnClick) { button_flags |= ImGuiButtonFlags_PressedOnClick; }
    if (flags & ImGuiSelectableFlags_SelectOnRelease) { button_flags |= ImGuiButtonFlags_PressedOnRelease; }
    if (flags & ImGuiSelectableFlags_AllowDoubleClick) {
        button_flags |= ImGuiButtonFlags_PressedOnClickRelease | ImGuiButtonFlags_PressedOnDoubleClick;
    }
    if ((flags & ImGuiSelectableFlags_AllowOverlap) ||
        (g.LastItemData.InFlags & ImGuiItemFlags_AllowOverlap)) {
        button_flags |= ImGuiButtonFlags_AllowOverlap;
    }

    const bool was_selected = selected;
    bool hovered, held, pressed = ButtonBehavior(bb, id, &hovered, &held, button_flags);

    if ((flags & ImGuiSelectableFlags_SelectOnNav) && g.NavJustMovedToId != 0 &&
        g.NavJustMovedToFocusScopeId == g.CurrentFocusScopeId)
        if (g.NavJustMovedToId == id) selected = pressed = true;

    // Update NavId when clicking or when Hovering (this doesn't happen on most widgets), so navigation can be resumed with gamepad/keyboard
    if (pressed || (hovered && (flags & ImGuiSelectableFlags_SetNavIdOnHover))) {
        if (!g.NavDisableMouseHover && g.NavWindow == window && g.NavLayer == window->DC.NavLayerCurrent) {
            SetNavID(id,
                window->DC.NavLayerCurrent,
                g.CurrentFocusScopeId,
                WindowRectAbsToRel(window, bb)); // (bb == NavRect)
            g.NavDisableHighlight = true;
        }
    }
    if (pressed) MarkItemEdited(id);

    if (selected != was_selected) g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_ToggledSelection;


    if (g.NavId == id) RenderNavHighlight(bb, id, ImGuiNavHighlightFlags_NoRounding);

    if (span_all_columns && window->DC.CurrentColumns) PopColumnsBackground();
    else if (span_all_columns && g.CurrentTable) TablePopBackgroundChannel();

    static std::map<ImGuiID, select_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, select_state() });
        it_anim = anim.find(id);
    }

    it_anim->second.text = ImLerp(it_anim->second.text, selected ? gui.text[0] : gui.text[1], GetAnimSpeed());
    it_anim->second.text_offset = ImLerp(it_anim->second.text_offset, selected ? 20.f : 4.5f, GetAnimSpeed());
    it_anim->second.background = ImLerp(it_anim->second.background,
        selected ? gui.third : func.GetColorWithAlpha(gui.third, 0.f),
        GetAnimSpeed());
    it_anim->second.line = ImLerp(it_anim->second.line,
        selected ? gui.main : func.GetColorWithAlpha(gui.main, 0.f),
        GetAnimSpeed());

    window->DrawList->AddRectFilled(bb.Min, bb.Max, GetColorU32(it_anim->second.background), c::elements::rounding);

    window->DrawList->AddRectFilled(bb.Max - ImVec2(3.f, bb.GetSize().y - 5.f),
        bb.Max - ImVec2(0.f, 5.f),
        GetColorU32(it_anim->second.line),
        30,
        ImDrawFlags_RoundCornersLeft);

    if (gui.combo_window != nullptr) {
        //PushFont(fonts["medium"][3]);
        const int vtx_idx_3 = GetWindowDrawList()->VtxBuffer.Size;
        /*window->DrawList->AddText(ImVec2(bb.Min.x + 6.f, func.CalcTextPos(bb.Min, bb.Max, label, fonts["medium"][3]).y),
            GetColorU32(it_anim->second.text),
            label);*/
        const int vtx_idx_4 = GetWindowDrawList()->VtxBuffer.Size;
        ShadeVertsLinearColorGradientSetAlpha(GetWindowDrawList(),
            vtx_idx_3,
            vtx_idx_4,
            ImVec2(gui.combo_window->Pos.x + gui.combo_window->Size.x - 60.f,
                bb.Min.y),
            ImVec2(gui.combo_window->Pos.x + gui.combo_window->Size.x, bb.Max.y),
            gui.text[0],
            func.GetColorWithAlpha(gui.text[0], 0.f));
        //PopFont();
    }

    if (pressed && (window->Flags & ImGuiWindowFlags_Popup) && !(flags & ImGuiSelectableFlags_DontClosePopups) &&
        !(g.LastItemData.InFlags & ImGuiItemFlags_SelectableDontClosePopup))
        CloseCurrentPopup();

    if (disabled_item && !disabled_global) EndDisabled();

    return pressed;
}

bool nav_elements::Selectable(const char* label, bool* p_selected, ImGuiSelectableFlags flags, const ImVec2& size_arg) {
    if (nav_elements::SelectableEx(label, *p_selected, flags, size_arg)) {
        *p_selected = !*p_selected;
        return true;
    }
    return false;
}

// Add after the existing TabVS2 implementation but before Checkbox

// Map to store animation states for horizontal tabs
std::map<ImGuiID, tab_elements> h_anim;

bool nav_elements::HorizontalTab(const char* label, int* v, int number) {
    // Check if the current tab is active
    bool is_active = (*v == number);

    // Call the horizontal tab function with the icon and label
    if (HorizontalTabV2(label, is_active)) {
        // If the tab is pressed, update the selected tab
        *v = number;
        return true;
    }

    return false;
}


bool nav_elements::HorizontalTabV2(const char* name, bool boolean)
{
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(name);

    const ImVec2 label_size = ImGui::CalcTextSize(name);
    ImVec2 pos = window->DC.CursorPos;

    const ImRect rect(pos, ImVec2(pos.x + 150, pos.y + 42));
    ImGui::ItemSize(ImVec4(rect.Min.x, rect.Min.y, rect.Max.x, rect.Max.y + 5), style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || boolean) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
    }

    auto it_anim = anim_2.find(id);
    if (it_anim == anim_2.end()) {
        anim_2.insert({ id, { 0.0f, 0.0f, 0.0f, ImVec4(1.0f, 1.0f, 1.0f, 0.7f), 0.0f } });
        it_anim = anim_2.find(id);
    }

    ImVec2 size({ window->Size.x, 24 });
    if (pressed)
        content_anim = 0.f;

    // Animation logic
    it_anim->second.line_opacity = ImLerp(it_anim->second.line_opacity, (boolean || hovered) ? 1.0f : 0.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity, (boolean ? 0.04f : hovered ? 0.01f : 0.0f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.rect_opacity = ImLerp(it_anim->second.rect_opacity, (boolean ? 1.0f : 0.0f), 0.15f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.text_opacity = ImLerp(it_anim->second.text_opacity, (boolean ? 1.0f : hovered ? 0.5f : 0.3f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Icon color animation
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        boolean || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f),
        GetAnimSpeed()
    );

    // Background rectangle
    window->DrawList->AddRectFilled(rect.Min, rect.Max, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Border
    ImVec4 border_color = boolean || hovered ? ImVec4(ImColor(255, 255, 255, 10)) : ImVec4(ImColor(255, 255, 255, 10));
    window->DrawList->AddRect(rect.Min, rect.Max, ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

    // Text and icon positioning
    const ImVec2 label_pos = ImVec2(rect.GetCenter().x - label_size.x / 2, rect.GetCenter().y - label_size.y / 2 - 2);
    // Label text
    window->DrawList->AddText(label_pos, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.text_opacity), name);

    // Horizontal bottom line parameters
    const float line_height = 1.0f;
    const float line_padding = 10.0f;
    ImVec2 line_start = { rect.Min.x + line_padding, rect.Max.y - 2 };
    ImVec2 line_end = { rect.Max.x - line_padding, rect.Max.y - 2 + line_height };

    // Shadow for horizontal line
    if (boolean || hovered) {
        window->DrawList->AddShadowRect(
            line_start,
            line_end,
            gui.main,
            25.f,
            ImVec2(0, 2),
            ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight,
            60.f
        );
    }

    // Main glow line
    ImColor glow_color = ImColor(accent_color[2], accent_color[1], accent_color[0], it_anim->second.line_opacity);
    window->DrawList->AddRectFilled(
        line_start,
        line_end,
        glow_color,
        360.f,
        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
    );

    // Subtle background line
    window->DrawList->AddRectFilled(
        line_start,
        line_end,
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.line_opacity * 0.2f),
        360.f,
        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
    );

    return pressed;
}
