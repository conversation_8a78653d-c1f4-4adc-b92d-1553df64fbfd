#include "PlayerVisuals.h"
#include "../../Framwork/Vectors.h"
#include "../Caching/Cache.h"
#include "../../Kernel Driver/Driver/Driver.h"
#include "../../GameClass/Offsets.h"
#include "../../GameClass/GameSettings.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../Menu UI/ImGui/imgui.h"
#include "../Aimbot/Aimbot.h"
#include "Drawing/Drawing.h"
#include "../../../Utils.h"
#include "../Loot/Loot.h"
#include "../../Settings/Settings.h"

// Initialize static members
bool PlayerVisuals::hasTarget = false;
bool PlayerVisuals::isReady = false;

float PlayerVisuals::CalculateDistance(Vector3& HeadBone3D) {
	return vCamera.Location.Distance(HeadBone3D) / 100;
}

void PlayerVisuals::HandleToggleKeys() {
	if ((GetAsyncKeyState(Keys.Toggle) & 0x8001) == 0x8001) Aimbot.Enable = !Aimbot.Enable;
	if ((GetAsyncKeyState(Keys.TogglePlayers) & 0x8001) == 0x8001) Players.Enable = !Players.Enable;
	if ((GetAsyncKeyState(Keys.ToggleItems) & 0x8001) == 0x8001) {
		Items.Ammo.Enable = !Items.Ammo.Enable;
		Items.Consumable.Enable = !Items.Consumable.Enable;
		Items.Weapon.Enable = !Items.Weapon.Enable;
		Items.Other.Enable = !Items.Other.Enable;
	}
	if ((GetAsyncKeyState(Keys.ToggleRadar) & 0x8001) == 0x8001) Radar.Enable = !Radar.Enable;
}

void PlayerVisuals::DrawAllPlayers() {
	// Ensure settings are synced before drawing
	SettingsHelper::SyncSettings();
	
	ImVec2 center(OverlayWidth / 2, OverlayHeight / 2);

	// Draw FOV and Crosshair if enabled
	if (Aimbot.DrawFov) Drawing::DrawFovCircle(Aimbot.FOV, Aimbot.DrawFovFilled, Aimbot.DrawFovRGB, false);
	if (Aimbot.DrawCrossHair) Drawing::DrawCrosshair(center);

	g_ClosestDistance = FLT_MAX;
	g_CurrentTarget = NULL;
    hasTarget = false;

	// Process each player in the CachedPlayerList
	const std::vector<CachedPlayer>& players = CacheSystem::GetCachedPlayers();
    for (const auto& player : players) {
        ProcessPlayerESP(player);
    }

	DrawItems();

	if (Aimbot.Enable) {
		if (g_CurrentTarget && hasTarget) {
			AimbotSpace::ProcessTarget(g_CurrentTarget, g_Targeted_fort_pawn, g_AcknowlegedPawn, Results::PlayerCameraManager);
		}
		else if (!hasTarget) {
			g_CurrentTarget = NULL;
			g_ClosestDistance = FLT_MAX;
		}
	}

	if (Radar.Enable) {
		Drawing::DrawRadar(Radar.PositionX, Radar.PositionY, Radar.CirleSize, Radar.RectangleSize);
	}
}

void PlayerVisuals::ProcessPlayerESP(const CachedPlayer& Actor) {
    // Skip invalid actors
    if (!Actor.Pawn || !Actor.Mesh || !Actor.PlayerState || !Actor.PlayerStates) 
        return;

    // Check for lobby state
    float player_health = GameFunctions::GetHealth(Actor.Pawn);
    bool currentInLobby = (player_health <= 0);
    if (currentInLobby != InLobby) {
        if (currentInLobby) {
            ResetLootCache();
            InLobby = true;
        } else {
            InLobby = false;
        }
    }

    // Team check filtering
    if (Players.TeamCheck && Actor.AcknowledgedPawn) {
        int localPlayerTeamIndex = UseDriver::read<int>(UseDriver::read<uint64_t>(Actor.AcknowledgedPawn + Offsets::PlayerState) + Offsets::TeamIndex);
        int playerTeamIndex = UseDriver::read<int>(Actor.PlayerState + Offsets::TeamIndex);
        if (playerTeamIndex == localPlayerTeamIndex) return;
    }

    // AI filtering
    if (!Players.PlayerAi) {
        uintptr_t bIsABot = (UseDriver::read<char>(Actor.PlayerStates + Offsets::bIsABot) >> 3) & 1;
        if (bIsABot) return;
    }

    // Downed player filtering
    if (Aimbot.IgnoreDowned || Players.IgnoreDowned) {
        uintptr_t bIsKnocked = (UseDriver::read<char>(Actor.Pawn + Offsets::bIsKnocked) >> 4) & 1;
        if ((Aimbot.IgnoreDowned && bIsKnocked) || (Players.IgnoreDowned && bIsKnocked)) return;
    }

    // Get bone and screen positions
    Vector3 HeadBone3D = GameFunctions::GetBoneWithRotation(Actor.Mesh, 110);
    Vector2 Head2D = GameFunctions::ProjectWorldToScreen(HeadBone3D + Vector3(0, 0, 15));
    Vector2 Bottom2D = GameFunctions::ProjectWorldToScreen(GameFunctions::GetBoneWithRotation(Actor.Mesh, 0));
    float distance = CalculateDistance(HeadBone3D);

    // Calculate box dimensions
    float boxHeight = abs(Head2D.y - Bottom2D.y);
    float boxWidth = boxHeight * 0.6f;
    int BoxX = Head2D.x - (boxWidth / 2);
    int BoxY = Head2D.y;
    float CornerWidth = boxHeight * 0.6;
    float CornerHeight = abs(Head2D.y - Bottom2D.y);

    // Check visibility and knocked state
    bool isVisible = GameFunctions::IsVisible(Actor.Mesh);
    bool isKnocked = (UseDriver::read<char>(Actor.Pawn + Offsets::isDBNO) >> 6) & 1;

    // Set colors based on visibility and knocked state
    ImColor lineColor = isKnocked ? 
        (isVisible ? ImColor(PlayersColors.KnockedLineVisible[0], PlayersColors.KnockedLineVisible[1], PlayersColors.KnockedLineVisible[2]) 
                   : ImColor(PlayersColors.KnockedLineNonVisible[0], PlayersColors.KnockedLineNonVisible[1], PlayersColors.KnockedLineNonVisible[2]))
        : (isVisible ? ImColor(PlayersColors.LineVisible[0], PlayersColors.LineVisible[1], PlayersColors.LineVisible[2]) 
                     : ImColor(PlayersColors.LineNonVisible[0], PlayersColors.LineNonVisible[1], PlayersColors.LineNonVisible[2]));

    ImColor boxColor = isKnocked ? 
        (isVisible ? ImColor(PlayersColors.KnockedBoxVisible[0], PlayersColors.KnockedBoxVisible[1], PlayersColors.KnockedBoxVisible[2]) 
                   : ImColor(PlayersColors.KnockedBoxNonVisible[0], PlayersColors.KnockedBoxNonVisible[1], PlayersColors.KnockedBoxNonVisible[2]))
        : (isVisible ? ImColor(PlayersColors.BoxVisible[0], PlayersColors.BoxVisible[1], PlayersColors.BoxVisible[2]) 
                     : ImColor(PlayersColors.BoxNonVisible[0], PlayersColors.BoxNonVisible[1], PlayersColors.BoxNonVisible[2]));

    ImColor boxFillColor = isKnocked ? 
        (isVisible ? ImColor(PlayersColors.KnockedBoxFillVisible[0], PlayersColors.KnockedBoxFillVisible[1], PlayersColors.KnockedBoxFillVisible[2], PlayersColors.KnockedBoxFillVisible[3])
                   : ImColor(PlayersColors.KnockedBoxFillNonVisible[0], PlayersColors.KnockedBoxFillNonVisible[1], PlayersColors.KnockedBoxFillNonVisible[2], PlayersColors.KnockedBoxFillNonVisible[3]))
        : (isVisible ? ImColor(PlayersColors.BoxFillVisible[0], PlayersColors.BoxFillVisible[1], PlayersColors.BoxFillVisible[2], PlayersColors.BoxFillVisible[3])
                     : ImColor(PlayersColors.BoxFillNonVisible[0], PlayersColors.BoxFillNonVisible[1], PlayersColors.BoxFillNonVisible[2], PlayersColors.BoxFillNonVisible[3]));

    ImColor headCircleColor = isKnocked ? 
        (isVisible ? ImColor(PlayersColors.KnockedHeadVisible[0], PlayersColors.KnockedHeadVisible[1], PlayersColors.KnockedHeadVisible[2]) 
                   : ImColor(PlayersColors.KnockedHeadNonVisible[0], PlayersColors.KnockedHeadNonVisible[1], PlayersColors.KnockedHeadNonVisible[2]))
        : (isVisible ? ImColor(PlayersColors.HeadVisible[0], PlayersColors.HeadVisible[1], PlayersColors.HeadVisible[2]) 
                     : ImColor(PlayersColors.HeadNonVisible[0], PlayersColors.HeadNonVisible[1], PlayersColors.HeadNonVisible[2]));

    ImColor skeletonColor = isKnocked ? 
        (isVisible ? ImColor(PlayersColors.KnockedSkeletonVisible[0], PlayersColors.KnockedSkeletonVisible[1], PlayersColors.KnockedSkeletonVisible[2]) 
                   : ImColor(PlayersColors.KnockedSkeletonNonVisible[0], PlayersColors.KnockedSkeletonNonVisible[1], PlayersColors.KnockedSkeletonNonVisible[2]))
        : (isVisible ? ImColor(PlayersColors.SkeletonVisible[0], PlayersColors.SkeletonVisible[1], PlayersColors.SkeletonVisible[2]) 
                     : ImColor(PlayersColors.SkeletonNonVisible[0], PlayersColors.SkeletonNonVisible[1], PlayersColors.SkeletonNonVisible[2]));

    ImColor radarColor = isKnocked ? 
        (isVisible ? ImColor(PlayersColors.KnockedRadarVisible[0], PlayersColors.KnockedRadarVisible[1], PlayersColors.KnockedRadarVisible[2]) 
                   : ImColor(PlayersColors.KnockedRadarNonVisible[0], PlayersColors.KnockedRadarNonVisible[1], PlayersColors.KnockedRadarNonVisible[2]))
        : (isVisible ? ImColor(PlayersColors.RadarVisible[0], PlayersColors.RadarVisible[1], PlayersColors.RadarVisible[2]) 
                     : ImColor(PlayersColors.RadarNonVisible[0], PlayersColors.RadarNonVisible[1], PlayersColors.RadarNonVisible[2]));

    // Add player to radar if enabled
    if (Radar.Enable) {
        Drawing::AddPlayerToRadar(GameFunctions::GetBoneWithRotation(Actor.Mesh, 0), distance, radarColor);
    }

    // Skip if not on screen
    if (!GameFunctions::isOnScreen(Head2D)) return;

    // Skip if outside range or ESP disabled
    if (!Players.Enable || distance >= Players.MaxDistance) return;

    // Draw visual elements
    if (Players.Lines) {
        ImGui::GetBackgroundDrawList()->AddLine(ImVec2(OverlayWidth / 2, 0), ImVec2(Head2D.x, Head2D.y), lineColor, Players.LinesThickness);
    }
    
    if (Players.Box) {
        if (Players.BoxType == 0) {
            float maxRounding = (std::min)(boxWidth, boxHeight) / 2.0f;
            float rounding = (std::min)(Players.BoxRounding, maxRounding);
            Drawing::DrawBox(BoxX, BoxY, (int)boxWidth, (int)boxHeight, boxColor, Players.BoxThickness, rounding, boxFillColor);
        } else if (Players.BoxType == 1) {
            Drawing::DrawCornerBox(Head2D.x - (CornerWidth / 2), Head2D.y, CornerWidth, CornerHeight, boxColor, Players.BoxThickness, boxFillColor);
        }
    }

    if (Players.Skeleton) Drawing::DrawSkeleton(Actor.Mesh, skeletonColor);
    if (Players.HeadCircle) Drawing::DrawHeadCircle(Actor.Mesh, headCircleColor);
    if (Players.Distance && !InLobby) Drawing::DrawDistanceText(distance, Bottom2D);
    if (Players.NickName || Players.Platform) Drawing::DrawEspText(Actor.PlayerStates, Actor.Pawn, Head2D);
    if (Players.Rank || Players.Kills || Players.Level) Drawing::DrawEspRankandLevel(Actor.PlayerStates, Actor.Pawn, Head2D);
    if (Players.Weapon && !InLobby) Drawing::DrawWeaponText(Actor.WeaponName, Bottom2D, Actor.PickColor);

    // Handle aimbot if enabled
    if (Aimbot.Enable) {
        if (!IsKeyDown(Keys.HoldPrimary) || !IsKeyDown(Keys.HoldSecondary)) {
            g_CurrentTarget = NULL;
            g_ClosestDistance = FLT_MAX;
        }

        AimbotSpace::HandleAimbot(Actor.Pawn, Actor.Mesh, HeadBone3D, Actor.TargetedPawn, Actor.AcknowledgedPawn, Actor.PlayerState);
        if (g_CurrentTarget && Actor.Pawn == g_CurrentTarget) {
            hasTarget = true;
        }
    }
}

// External function used to call the player visuals system
void DrawEntityList() {
    PlayerVisuals::DrawAllPlayers();
}

// Placeholder for DrawItems until implementation
void PlayerVisuals::DrawItems() {
    LootSystem::DrawItems();
}
