#include "InputManager.h"
#include "HotkeySystem.h"
#include "../../Settings/Settings.h"
#include "../Config/Config.h"
#include <iostream>

// Initialize the input system
void InitializeInputSystem() {
    // Initialize the input manager with debug mode enabled
    InputManager::Initialize(true);

    // Initialize the hotkey system with debug mode enabled
    HotkeySystem::Initialize(true);

    // Explicitly enable the hotkey system
    HotkeySystem::SetEnabled(true);

    // Monitor all letter keys (A-Z)
    for (int key = 'A'; key <= 'Z'; key++) {
        InputManager::Monitor<PERSON>ey(key);
    }

    // Also monitor lowercase letters (a-z) for maximum compatibility
    for (int key = 'a'; key <= 'z'; key++) {
        InputManager::MonitorKey(key);
    }

    // Monitor number keys (0-9)
    for (int key = '0'; key <= '9'; key++) {
        InputManager::MonitorKey(key);
    }

    // Monitor function keys (F1-F12)
    for (int key = VK_F1; key <= VK_F12; key++) {
        InputManager::MonitorKey(key);
    }

    // Monitor special keys
    InputManager::MonitorKey(VK_SPACE);
    InputManager::MonitorKey(VK_SHIFT);
    InputManager::MonitorKey(VK_LSHIFT);
    InputManager::MonitorKey(VK_RSHIFT);
    InputManager::MonitorKey(VK_CONTROL);
    InputManager::MonitorKey(VK_LCONTROL);
    InputManager::MonitorKey(VK_RCONTROL);
    InputManager::MonitorKey(VK_MENU); // Alt
    InputManager::MonitorKey(VK_LMENU); // Left Alt
    InputManager::MonitorKey(VK_RMENU); // Right Alt
    InputManager::MonitorKey(VK_TAB);
    InputManager::MonitorKey(VK_ESCAPE);
    InputManager::MonitorKey(VK_RETURN);
    InputManager::MonitorKey(VK_BACK);
    InputManager::MonitorKey(VK_DELETE);
    InputManager::MonitorKey(VK_INSERT);
    InputManager::MonitorKey(VK_HOME);
    InputManager::MonitorKey(VK_END);
    InputManager::MonitorKey(VK_PRIOR); // Page Up
    InputManager::MonitorKey(VK_NEXT);  // Page Down
    InputManager::MonitorKey(VK_LEFT);
    InputManager::MonitorKey(VK_RIGHT);
    InputManager::MonitorKey(VK_UP);
    InputManager::MonitorKey(VK_DOWN);
    InputManager::MonitorKey(VK_NUMPAD0);
    InputManager::MonitorKey(VK_NUMPAD1);
    InputManager::MonitorKey(VK_NUMPAD2);
    InputManager::MonitorKey(VK_NUMPAD3);
    InputManager::MonitorKey(VK_NUMPAD4);
    InputManager::MonitorKey(VK_NUMPAD5);
    InputManager::MonitorKey(VK_NUMPAD6);
    InputManager::MonitorKey(VK_NUMPAD7);
    InputManager::MonitorKey(VK_NUMPAD8);
    InputManager::MonitorKey(VK_NUMPAD9);
    InputManager::MonitorKey(VK_MULTIPLY);
    InputManager::MonitorKey(VK_ADD);
    InputManager::MonitorKey(VK_SUBTRACT);
    InputManager::MonitorKey(VK_DECIMAL);
    InputManager::MonitorKey(VK_DIVIDE);

    // Monitor mouse buttons
    InputManager::MonitorKey(VK_LBUTTON);
    InputManager::MonitorKey(VK_RBUTTON);
    InputManager::MonitorKey(VK_MBUTTON);
    InputManager::MonitorKey(VK_XBUTTON1);
    InputManager::MonitorKey(VK_XBUTTON2);

    // Specifically monitor the S key for AimbotEnable (redundant but for clarity)
    InputManager::MonitorKey('S');
    InputManager::MonitorKey('s');

    std::cout << "Input system initialized successfully with all keys monitored" << std::endl;

    // Register all hotkeys from the Keys struct
    HotkeySystem::RegisterAllHotkeys();

    // Print all registered hotkeys
    HotkeySystem::PrintRegisteredHotkeys();
}

// Update the input system (call every frame)
void UpdateInputSystem() {
    // Update the input manager first to get the latest key states
    InputManager::Update();

    // Update the hotkey system to process hotkeys based on the latest key states
    HotkeySystem::Update();

    // Debug output for key states (every 60 frames to avoid console spam)
    static int frameCount = 0;
    frameCount++;

    if (frameCount % 60 == 0) {
        // Check if the S key is down (for AimbotEnable)
        if (InputManager::IsKeyDown('S')) {
            std::cout << "S key is currently down" << std::endl;
        }

        // Print the current state of some features
        std::cout << "Current feature states:" << std::endl;
        std::cout << "  Aimbot.Enable: " << (Aimbot.Enable ? "ON" : "OFF") << std::endl;
        std::cout << "  Settings.Aim.AimbotConfig.Enable: " << (Settings.Aim.AimbotConfig.Enable ? "ON" : "OFF") << std::endl;
    }

    // Ensure settings are synced with the global state
    SettingsHelper::SyncSettings();
}

// Set the menu open state
void SetMenuOpenState(bool open) {
    // Set the menu open state in the hotkey system
    HotkeySystem::SetMenuOpen(open);
}

// Enable or disable the hotkey system
void SetHotkeySystemEnabled(bool enable) {
    // Enable or disable the hotkey system
    HotkeySystem::SetEnabled(enable);
}

// Reset all hotkeys
void ResetAllHotkeys() {
    // Reset all hotkeys
    HotkeySystem::ResetAllHotkeys();

    // Save settings to persist the changes
    try {
        if (SettingsHelper::SaveSettings()) {
            std::cout << "Settings saved successfully after resetting all hotkeys" << std::endl;
        } else {
            std::cout << "Failed to save settings after resetting all hotkeys" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cout << "Exception while saving settings: " << e.what() << std::endl;
    }
}

// Update a hotkey
void UpdateHotkey(const std::string& name, int keyCode, bool toggleMode) {
    // Update the hotkey
    HotkeySystem::UpdateHotkey(name, keyCode, toggleMode);

    // Update the Keys struct directly
    if (name == "AimbotEnable") {
        Keys.AimbotEnable = keyCode;
        Keys.AimbotEnableMode = toggleMode;
        std::cout << "Directly set Keys.AimbotEnable to " << keyCode << std::endl;

        // Force update the feature state
        if (toggleMode) {
            // Toggle mode - keep current state
        } else {
            // Hold mode - set to false initially unless key is down
            if (!InputManager::IsKeyDown(keyCode)) {
                Aimbot.Enable = false;
                std::cout << "Hold mode: Setting Aimbot.Enable to OFF (key not down)" << std::endl;
            } else {
                Aimbot.Enable = true;
                std::cout << "Hold mode: Setting Aimbot.Enable to ON (key is down)" << std::endl;
            }
        }
    } else if (name == "AimbotAimLock") {
        Keys.AimbotAimLock = keyCode;
        Keys.AimbotAimLockMode = toggleMode;
        std::cout << "Directly set Keys.AimbotAimLock to " << keyCode << std::endl;

        // Force update the feature state
        if (toggleMode) {
            // Toggle mode - keep current state
        } else {
            // Hold mode - set to false initially unless key is down
            if (!InputManager::IsKeyDown(keyCode)) {
                Aimbot.AimLock = false;
                std::cout << "Hold mode: Setting Aimbot.AimLock to OFF (key not down)" << std::endl;
            } else {
                Aimbot.AimLock = true;
                std::cout << "Hold mode: Setting Aimbot.AimLock to ON (key is down)" << std::endl;
            }
        }
    } else if (name == "PlayerEspEnable") {
        Keys.PlayerEspEnable = keyCode;
        Keys.PlayerEspEnableMode = toggleMode;
        std::cout << "Directly set Keys.PlayerEspEnable to " << keyCode << std::endl;

        // Force update the feature state
        if (toggleMode) {
            // Toggle mode - keep current state
        } else {
            // Hold mode - set to false initially unless key is down
            if (!InputManager::IsKeyDown(keyCode)) {
                Players.Enable = false;
                std::cout << "Hold mode: Setting Players.Enable to OFF (key not down)" << std::endl;
            } else {
                Players.Enable = true;
                std::cout << "Hold mode: Setting Players.Enable to ON (key is down)" << std::endl;
            }
        }
    }

    // Save settings to persist the changes
    try {
        if (SettingsHelper::SaveSettings()) {
            std::cout << "Settings saved successfully after updating hotkey" << std::endl;
        } else {
            std::cout << "Failed to save settings after updating hotkey" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cout << "Exception while saving settings: " << e.what() << std::endl;
    }

    // Force update the input manager to ensure the key state is up to date
    InputManager::Update();

    // Force update the hotkey system to ensure the feature state is up to date
    HotkeySystem::Update();
}

// Reset a hotkey
void ResetHotkey(const std::string& name) {
    // Reset the hotkey
    HotkeySystem::ResetHotkey(name);

    // Save settings to persist the changes
    try {
        if (SettingsHelper::SaveSettings()) {
            std::cout << "Settings saved successfully after resetting hotkey" << std::endl;
        } else {
            std::cout << "Failed to save settings after resetting hotkey" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cout << "Exception while saving settings: " << e.what() << std::endl;
    }
}

// Get the name of a key from its virtual key code
std::string GetKeyName(int keyCode) {
    return InputManager::GetKeyName(keyCode);
}

// Check if a key is currently down
bool IsKeyDown(int keyCode) {
    return InputManager::IsKeyDown(keyCode);
}

// Check if a key was just pressed this frame
bool IsKeyJustPressed(int keyCode) {
    return InputManager::IsKeyJustPressed(keyCode);
}

// Check if a key was just released this frame
bool IsKeyJustReleased(int keyCode) {
    return InputManager::IsKeyJustReleased(keyCode);
}
