#define IMGUI_DEFINE_MATH_OPERATORS
#include "Components.h"
#include <functional>
#include "../ImGui/imgui_internal.h"
#include "nav_elements.h"
#include "../Fonts/font_defines.h"
#include "../ImGui/blur/blur.hpp"
#include <iostream>

// External DirectX resources from main.cpp
extern IDXGISwapChain* g_pSwapChain;

// Logging macro (if not already defined)
#ifndef LOG_DEBUG
#define DEBUG_LOG 1
#if DEBUG_LOG
#define LOG_DEBUG(msg, ...) printf("[DEBUG_COMP] " msg "\n", ##__VA_ARGS__)
#else
#define LOG_DEBUG(msg, ...)
#endif
#endif

// Create a global instance of UIComponents
UIComponents Components;

// Create global instances of ModernUI components
ModernUI::MenuContainer MainMenu;
ModernUI::NavigationPanel NavPanel;
ModernUI::ContentSection Content;
ModernUI::UIElements Elements;

void UIComponents::Initialize(ID3D11Device* d3dDevice) {
    // Any initialization needed for UI components
    // Could include loading textures, setting up styles, etc.
}

bool UIComponents::RenderButton(const char* label, ImVec2 size) {
    return ImGui::Button(label, size);
}

bool UIComponents::RenderCheckbox(const char* label, bool* value) {
    return ImGui::Checkbox(label, value);
}

bool UIComponents::RenderSlider(const char* label, float* value, float min, float max, const char* format) {
    return ImGui::SliderFloat(label, value, min, max, format);
}

bool UIComponents::RenderSlider(const char* label, int* value, int min, int max, const char* format) {
    return ImGui::SliderInt(label, value, min, max, format);
}

bool UIComponents::RenderColorPicker(const char* label, float* color, ImGuiColorEditFlags flags) {
    return ImGui::ColorEdit3(label, color, flags);
}

bool UIComponents::RenderToggleButton(const char* label, bool* value, ImVec2 size) {
    ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 3.0f);

    // Set colors based on state
    if (*value) {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.8f, 0.2f, 0.7f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.0f, 0.9f, 0.2f, 0.8f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.0f, 1.0f, 0.2f, 0.9f));
    } else {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.1f, 0.1f, 0.7f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.9f, 0.1f, 0.1f, 0.8f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(1.0f, 0.1f, 0.1f, 0.9f));
    }

    bool pressed = ImGui::Button(label, size);
    if (pressed) {
        *value = !(*value);
    }

    ImGui::PopStyleColor(3);
    ImGui::PopStyleVar();

    return pressed;
}

// ModernUI MenuContainer Implementation
ModernUI::MenuContainer::MenuContainer() :
    m_WindowSize(ImVec2(888, 535)),
    m_WindowPos(ImVec2(0, 0)),
    m_Opacity(1.0f)
{
}

void ModernUI::MenuContainer::Render(bool& showMenu, const char* title) {
    if (!showMenu)
        return;

    // Initialize window position if needed (first-time rendering)
    static bool init = true;
    if (init) {
        ImGui::SetNextWindowPos(ImVec2(300, 300));
        init = false;
    }

    ImGui::SetNextWindowSize(m_WindowSize);

    // Set up window flags for modern look
    ImGuiWindowFlags windowFlags = ImGuiWindowFlags_NoTitleBar |
                                 ImGuiWindowFlags_NoResize |
                                 ImGuiWindowFlags_NoSavedSettings;

    ImGui::Begin(title, &showMenu, windowFlags);
    {
        m_WindowPos = ImGui::GetWindowPos();

        // Store content area for potential blur effects or other decorations
        auto drawList = ImGui::GetWindowDrawList();

        gui.window_pos = ImGui::GetWindowPos();
        gui.window_size = ImGui::GetWindowSize();

        auto draw = ImGui::GetWindowDrawList();
        const auto& p = ImGui::GetWindowPos();
        ImGuiStyle& style = ImGui::GetStyle();

        // Apply blur effect to the entire window background
        //if (draw) {
        //    // Adjust blur settings for the main window
        //    float radius = style.WindowRounding > 0 ? style.WindowRounding : 10.0f;

        //    // Apply blur using blurme only
        //    if (g_pSwapChain) {
        //        blurme(draw, p, p + gui.window_size, radius);
        //    }
        //}

        // Apply any window-level styling here if needed
    }
    // Note: Don't end the window here - it will be ended after content is rendered
}

// NavigationPanel Implementation
ModernUI::NavigationPanel::NavigationPanel() {
    // Initialize tab names and icons
    m_TabNames[TAB_AIMBOT] = "AIMBOT";
    m_TabNames[TAB_VISUALS] = "VISUALS";
    m_TabNames[TAB_ITEMS] = "ITEMS";
    m_TabNames[TAB_RADAR] = "RADAR";
    m_TabNames[TAB_SETTINGS] = "SETTINGS";
    m_TabNames[TAB_CONFIGS] = "CONFIGS";

    // Set icons matching what's used in nav_elements
    m_TabIcons[TAB_AIMBOT] = ICON_MS_TARGET;
    m_TabIcons[TAB_VISUALS] = ICON_MS_VISIBILITY;
    m_TabIcons[TAB_ITEMS] = ICON_MS_INVENTORY;
    m_TabIcons[TAB_RADAR] = ICON_MS_RADAR;
    m_TabIcons[TAB_SETTINGS] = ICON_MS_SETTINGS;
    m_TabIcons[TAB_CONFIGS] = ICON_MS_FOLDER;

    // Initialize animation states
    for (int i = 0; i < TAB_COUNT; i++) {
        m_TabAnimations[i] = false;
    }
}

void ModernUI::NavigationPanel::Render(int* currentTab) {
    // Create sidebar panel
    ImGui::BeginChild("Sidebar", ImVec2(160, 535), true);
    {
        // Add logo or brand area at top
        float logoAreaHeight = 120.0f;
        ImGui::SetCursorPosY(40.0f);
        ImGui::SetCursorPosX((160.0f - 60.0f) / 2.0f);

        ImVec2 windowPos = ImGui::GetWindowPos();
        ImDrawList* drawList = ImGui::GetWindowDrawList();

        // Draw logo (blue circle with B letter)
        ImVec4 accentColor = ImVec4(0.0f, 0.58f, 1.0f, 1.00f);
        drawList->AddCircleFilled(
            ImVec2(windowPos.x + 80.0f, windowPos.y + 60.0f),
            30.0f,
            ImGui::ColorConvertFloat4ToU32(accentColor),
            32
        );

        // Add "BITCHEATS" text below logo
        ImGui::SetCursorPosY(115.0f);
        ImGui::SetCursorPosX(50.0f);
        ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), "BITCHEATS");

        // Add space after logo
        ImGui::Dummy(ImVec2(0.0f, 20.0f));

        // Render tabs using nav_elements
        ImGui::Indent(10.0f);
        for (int i = 0; i < TAB_COUNT; i++) {
            if (nav_elements::Tab(m_TabNames[i], m_TabIcons[i], currentTab, i)) {
                m_TabAnimations[i] = true;
            }
        }
        ImGui::Unindent(10.0f);
    }
    ImGui::EndChild();
}

// ContentSection Implementation
ModernUI::ContentSection::ContentSection() :
    m_SectionPadding(ImVec2(10, 10)),
    m_ContentSize(ImVec2(718, 525)),
    m_InTwoColumnMode(false)
{
}

void ModernUI::ContentSection::Begin() {
    ImGui::SameLine();
    ImGui::BeginChild("Content", m_ContentSize, true);
}

void ModernUI::ContentSection::End() {
    ImGui::EndChild();
    ImGui::End(); // End the main window started in MenuContainer::Render
}

void ModernUI::ContentSection::BeginSection(const char* title) {
    ImGui::Text("%s", title);
    ImGui::Separator();
    ImGui::Spacing();
}

void ModernUI::ContentSection::EndSection() {
    ImGui::Spacing();
}

void ModernUI::ContentSection::CreateTwoColumnLayout(float leftColumnWidth) {
    ImGui::Columns(2, nullptr, false);
    ImGui::SetColumnWidth(0, leftColumnWidth);
    m_InTwoColumnMode = true;
}

void ModernUI::ContentSection::NextColumn() {
    if (m_InTwoColumnMode) {
        ImGui::NextColumn();
    }
}

void ModernUI::ContentSection::EndColumns() {
    if (m_InTwoColumnMode) {
        ImGui::Columns(1);
        m_InTwoColumnMode = false;
    }
}

// UIElements Implementation
ModernUI::UIElements::UIElements() {
}

bool ModernUI::UIElements::Checkbox(const char* label, bool* value, const char* description) {
    return nav_elements::Checkbox(label, value, description ? description : "");
}

bool ModernUI::UIElements::Slider(const char* label, float* value, float min, float max, const char* format, const char* description) {
    return nav_elements::SliderFloat(label, value, min, max, description ? description : "", format);
}

bool ModernUI::UIElements::Slider(const char* label, int* value, int min, int max, const char* format, const char* description) {
    return nav_elements::SliderInt(label, value, min, max, description ? description : "", format);
}

bool ModernUI::UIElements::Button(const char* label, const ImVec2& size, ImVec4 color) {
    ImColor btnColor(color.x, color.y, color.z, color.w);
    return nav_elements::Button(label, size, btnColor);
}

bool ModernUI::UIElements::Keybind(const char* label, int* key, bool* mode, const char* description) {
    static bool defaultMode = false;
    return nav_elements::Keybind(label, description ? description : "", key, mode ? mode : &defaultMode);
}

bool ModernUI::UIElements::ColorEdit(const char* label, float* color, const char* description, ImGuiColorEditFlags flags) {
    return nav_elements::ColorEdit4Ex(label, description ? description : "", color, flags);
}

bool ModernUI::UIElements::Combo(const char* label, int* current_item, const char* const items[], int items_count, const char* description, int height_in_items) {
    LOG_DEBUG("Elements::Combo - Label: %s, Current item: %d, Items count: %d",
              label, (current_item ? *current_item : -1), items_count);

    // Check parameters
    if (!current_item) {
        LOG_DEBUG("ERROR: current_item is NULL");
        return false;
    }

    if (!items || items_count <= 0) {
        LOG_DEBUG("ERROR: items array is NULL or items_count <= 0");
        return false;
    }

    // Call the nav_elements::Combo
    bool result = nav_elements::Combo(label, current_item, items, items_count, description ? description : "", height_in_items);

    LOG_DEBUG("Elements::Combo - Result: %s, New value: %d",
              result ? "true" : "false", *current_item);

    return result;
}

void ModernUI::UIElements::Separator(float spacing) {
    ImGui::Spacing();
    if (spacing > 0) ImGui::Dummy(ImVec2(0, spacing));
    ImGui::Separator();
    if (spacing > 0) ImGui::Dummy(ImVec2(0, spacing));
}

void ModernUI::UIElements::SectionTitle(const char* title) {
    ImGui::Text("%s", title);
    ImGui::Separator();
    ImGui::Spacing();
}
