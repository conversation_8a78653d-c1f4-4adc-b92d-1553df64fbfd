#include "Config.h"
#include "../Hotkeys/HotkeyProcessor.h"
#include "../../GameClass/GameSettings.h"
#include "../../Settings/Settings.h"
#include <iostream>
#include <string>

// Implementation of LoadHotkeySettings function
void LoadHotkeySettings(const std::string& key, const std::string& value) {
    // Debug output
    std::cout << "LoadHotkeySettings: key=" << key << ", value=" << value << std::endl;

    // Check if this is a mode setting (ends with "Mode")
    bool isModeSetting = key.length() > 4 && key.substr(key.length() - 4) == "Mode";

    if (isModeSetting) {
        // This is a toggle/hold mode setting
        std::string featureName = key.substr(0, key.length() - 4);
        bool toggleMode = (value == "1"); // 1 = toggle, 0 = hold

        // Debug output
        std::cout << "  Loading mode for " << featureName << ": " << (toggleMode ? "Toggle" : "Hold") << std::endl;

        // Also update the Keys struct for compatibility
        // Aimbot modes
        if (featureName == "AimbotEnable") {
            Keys.AimbotEnableMode = toggleMode;
            std::cout << "  Set AimbotEnableMode to " << (toggleMode ? "Toggle" : "Hold") << std::endl;
        }
        else if (featureName == "AimbotAimLock") {
            Keys.AimbotAimLockMode = toggleMode;
            std::cout << "  Set AimbotAimLockMode to " << (toggleMode ? "Toggle" : "Hold") << std::endl;
        }
        else if (featureName == "AimbotPrediction") {
            Keys.AimbotPredictionMode = toggleMode;
        }
        else if (featureName == "AimbotSaveTarget") {
            Keys.AimbotSaveTargetMode = toggleMode;
        }
        else if (featureName == "AimbotVisibilityCheck") {
            Keys.AimbotVisibilityCheckMode = toggleMode;
        }
        else if (featureName == "AimbotHumanizedSmooth") {
            Keys.AimbotHumanizedSmoothMode = toggleMode;
        }
        else if (featureName == "AimbotIgnoreDowned") {
            Keys.AimbotIgnoreDownedMode = toggleMode;
        }
        else if (featureName == "AimbotPlayerAi") {
            Keys.AimbotPlayerAiMode = toggleMode;
        }
        else if (featureName == "AimbotWeaponOnly") {
            Keys.AimbotWeaponOnlyMode = toggleMode;
        }
        else if (featureName == "AimbotDrawFov") {
            Keys.AimbotDrawFovMode = toggleMode;
        }
        else if (featureName == "AimbotDrawCrossHair") {
            Keys.AimbotDrawCrossHairMode = toggleMode;
        }
        else if (featureName == "AimbotDrawTarget") {
            Keys.AimbotDrawTargetMode = toggleMode;
        }

        // ESP modes
        else if (featureName == "PlayerEspEnable") {
            Keys.PlayerEspEnableMode = toggleMode;
            std::cout << "  Set PlayerEspEnableMode to " << (toggleMode ? "Toggle" : "Hold") << std::endl;
        }
        else if (featureName == "PlayerEspBox") {
            Keys.PlayerEspBoxMode = toggleMode;
        }
        else if (featureName == "PlayerEspSkeleton") {
            Keys.PlayerEspSkeletonMode = toggleMode;
        }
        else if (featureName == "PlayerEspHeadCircle") {
            Keys.PlayerEspHeadCircleMode = toggleMode;
        }
        else if (featureName == "PlayerEspLines") {
            Keys.PlayerEspLinesMode = toggleMode;
        }
        else if (featureName == "PlayerEspDistance") {
            Keys.PlayerEspDistanceMode = toggleMode;
        }
        else if (featureName == "PlayerEspNickName") {
            Keys.PlayerEspNickNameMode = toggleMode;
        }

        // Items modes
        else if (featureName == "ItemEspEnable") {
            Keys.ItemEspEnableMode = toggleMode;
        }
        else if (featureName == "ItemConsumableEnable") {
            Keys.ItemConsumableEnableMode = toggleMode;
        }
        else if (featureName == "ItemWeaponEnable") {
            Keys.ItemWeaponEnableMode = toggleMode;
        }
        else if (featureName == "ItemAmmoEnable") {
            Keys.ItemAmmoEnableMode = toggleMode;
        }
        else if (featureName == "ItemOtherEnable") {
            Keys.ItemOtherEnableMode = toggleMode;
        }

        // Radar modes
        else if (featureName == "RadarEnable") {
            Keys.RadarEnableMode = toggleMode;
        }
        else if (featureName == "RadarDistance") {
            Keys.RadarDistanceMode = toggleMode;
        }
        else if (featureName == "RadarVisibleColor") {
            Keys.RadarVisibleColorMode = toggleMode;
        }
        else if (featureName == "RadarClosestColor") {
            Keys.RadarClosestColorMode = toggleMode;
        }
        else if (featureName == "RadarAimingAtMeColor") {
            Keys.RadarAimingAtMeColorMode = toggleMode;
        }

        // Update the Settings struct to match the Keys struct
        Settings.Keys.Keys = Keys;
    }
    else {
        // This is a key code setting
        try {
            int keyCode = std::stoi(value);

            // Debug output
            std::cout << "  Loading key for " << key << ": " << keyCode << std::endl;

            // Also update the Keys struct for compatibility
            // Aimbot keys
            if (key == "AimbotEnable") {
                Keys.AimbotEnable = keyCode;
                std::cout << "  Set AimbotEnable key to " << keyCode << std::endl;
            }
            else if (key == "AimbotAimLock") {
                Keys.AimbotAimLock = keyCode;
                std::cout << "  Set AimbotAimLock key to " << keyCode << std::endl;
            }
            else if (key == "AimbotPrediction") {
                Keys.AimbotPrediction = keyCode;
            }
            else if (key == "AimbotSaveTarget") {
                Keys.AimbotSaveTarget = keyCode;
            }
            else if (key == "AimbotVisibilityCheck") {
                Keys.AimbotVisibilityCheck = keyCode;
            }
            else if (key == "AimbotHumanizedSmooth") {
                Keys.AimbotHumanizedSmooth = keyCode;
            }
            else if (key == "AimbotIgnoreDowned") {
                Keys.AimbotIgnoreDowned = keyCode;
            }
            else if (key == "AimbotPlayerAi") {
                Keys.AimbotPlayerAi = keyCode;
            }
            else if (key == "AimbotWeaponOnly") {
                Keys.AimbotWeaponOnly = keyCode;
            }
            else if (key == "AimbotDrawFov") {
                Keys.AimbotDrawFov = keyCode;
            }
            else if (key == "AimbotDrawCrossHair") {
                Keys.AimbotDrawCrossHair = keyCode;
            }
            else if (key == "AimbotDrawTarget") {
                Keys.AimbotDrawTarget = keyCode;
            }

            // ESP keys
            else if (key == "PlayerEspEnable") {
                Keys.PlayerEspEnable = keyCode;
                std::cout << "  Set PlayerEspEnable key to " << keyCode << std::endl;
            }
            else if (key == "PlayerEspBox") {
                Keys.PlayerEspBox = keyCode;
            }
            else if (key == "PlayerEspSkeleton") {
                Keys.PlayerEspSkeleton = keyCode;
            }
            else if (key == "PlayerEspHeadCircle") {
                Keys.PlayerEspHeadCircle = keyCode;
            }
            else if (key == "PlayerEspLines") {
                Keys.PlayerEspLines = keyCode;
            }
            else if (key == "PlayerEspDistance") {
                Keys.PlayerEspDistance = keyCode;
            }
            else if (key == "PlayerEspNickName") {
                Keys.PlayerEspNickName = keyCode;
            }

            // Items keys
            else if (key == "ItemEspEnable") {
                Keys.ItemEspEnable = keyCode;
            }
            else if (key == "ItemConsumableEnable") {
                Keys.ItemConsumableEnable = keyCode;
            }
            else if (key == "ItemWeaponEnable") {
                Keys.ItemWeaponEnable = keyCode;
            }
            else if (key == "ItemAmmoEnable") {
                Keys.ItemAmmoEnable = keyCode;
            }
            else if (key == "ItemOtherEnable") {
                Keys.ItemOtherEnable = keyCode;
            }

            // Radar keys
            else if (key == "RadarEnable") {
                Keys.RadarEnable = keyCode;
            }
            else if (key == "RadarDistance") {
                Keys.RadarDistance = keyCode;
            }
            else if (key == "RadarVisibleColor") {
                Keys.RadarVisibleColor = keyCode;
            }
            else if (key == "RadarClosestColor") {
                Keys.RadarClosestColor = keyCode;
            }
            else if (key == "RadarAimingAtMeColor") {
                Keys.RadarAimingAtMeColor = keyCode;
            }

            // Update the Settings struct to match the Keys struct
            Settings.Keys.Keys = Keys;
        }
        catch (const std::exception& e) {
            std::cerr << "Error parsing key code: " << e.what() << std::endl;
        }
    }
}
