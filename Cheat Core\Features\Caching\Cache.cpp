#include "Cache.h"
#include <thread>
#include "../Loot/Loot.h"
#include "../../GameClass/Offsets.h"
#include "../../Kernel Driver/Driver/Driver.h"
#include "../../GameClass/GameFunctions.h"

// Initialize static members
std::vector<CachedPlayer> CacheSystem::cachedPlayerList;
std::mutex CacheSystem::cacheMutex;
bool CacheSystem::isRunning = false;
bool CacheSystem::isReady = false;


void CacheSystem::UpdatePlayerCache() {
    if (!isReady) return;
    
    std::vector<CachedPlayer> tempList;

	Results::Uworld = UseDriver::read<__int64>(oBaseAddress + Offsets::UWorld);

	auto OwningGameInstance = UseDriver::read<uintptr_t>(Results::Uworld + Offsets::OwningGameInstance);
	auto GameState = UseDriver::read<uintptr_t>(Results::Uworld + Offsets::GameState);
	Results::GameState = UseDriver::read<uintptr_t>(Results::Uworld + Offsets::GameState);
	auto PlayerArray = UseDriver::read<uintptr_t>(GameState + Offsets::PlayerArray);

	auto LocalPlayers = UseDriver::read<uintptr_t>(OwningGameInstance + Offsets::LocalPlayers);
	auto LocalPlayer = UseDriver::read<uintptr_t>(LocalPlayers);
	Results::LocalPlayer = UseDriver::read<uintptr_t>(LocalPlayers);
	Results::PlayerController = UseDriver::read<uintptr_t>(LocalPlayer + Offsets::PlayerController);
	uintptr_t AcknowlegedPawn = UseDriver::read<uintptr_t>(Results::PlayerController + Offsets::AcknowledgedPawn);
	Results::AcknowlegedPawn = UseDriver::read<uintptr_t>(Results::PlayerController + Offsets::AcknowledgedPawn);

	Results::PlayerCameraManager = UseDriver::read<uintptr_t>(Results::PlayerController + Offsets::PlayerCameraManager);
	auto targeted_fort_pawn = UseDriver::read<std::uintptr_t>(Results::PlayerController + Offsets::TargetedFortPawn);

	if (!GameState) { cachedPlayerList.clear(); return; }

	if (!PlayerArray) { cachedPlayerList.clear(); return; }


	int PlayerNum = UseDriver::read<int>(GameState + Offsets::PlayerArray + 0x8);
	//std::cout << "PlayerNum :" << PlayerNum << std::endl;

	for (int i = 0; i < PlayerNum; i++) {
		auto PlayerState = UseDriver::read<uintptr_t>(PlayerArray + i * 0x8);
		//std::cout << "PlayerState :" << PlayerState << std::endl;

		if (!PlayerState) continue;

		auto PawnPrivate = UseDriver::read<uintptr_t>(PlayerState + Offsets::PawnPrivate);
		//std::cout << "PawnPrivate :" << PawnPrivate << std::endl;
		if (!PawnPrivate) continue;

		auto Mesh = UseDriver::read<uintptr_t>(uintptr_t(PawnPrivate) + Offsets::Mesh);
		//std::cout << "Mesh :" << Mesh << std::endl;
		if (!Mesh) continue;

		auto PlayerStates = UseDriver::read<uint64_t>(PawnPrivate + Offsets::PlayerState);
		//std::cout << "PlayerStates :" << PlayerStates << std::endl;
		if (!PlayerStates) continue;

		if (!PawnPrivate || PawnPrivate == AcknowlegedPawn) continue;

		std::string EnemyPlayerWeapon;

		ImColor pickcolor = ImColor(0, 0, 0, 255);

		auto CurrentWeapon = UseDriver::read<uintptr_t>(PawnPrivate + Offsets::CurrentWeapon);
		auto WeaponData = UseDriver::read<uintptr_t>(CurrentWeapon + Offsets::WeaponData);
		uint8_t tier = UseDriver::read<uint8_t>(WeaponData + Offsets::Rarity);
		EnemyPlayerWeapon = GameFunctions::GetWeaponName(PawnPrivate);
		//std::cout << "EnemyPlayerWeapon :" << EnemyPlayerWeapon << std::endl;
		if (!EnemyPlayerWeapon.empty())
		{
			switch (tier) {
			case 0: pickcolor = ImColor(0, 0, 0); break;
			case 1: pickcolor = ImColor(50, 205, 50); break;
			case 2: pickcolor = ImColor(60, 140, 207); break;
			case 3: pickcolor = ImColor(255, 0, 255); break;
			case 4: pickcolor = ImColor(255, 165, 0); break;
			case 5: pickcolor = ImColor(181, 132, 12); break;
			case 6: pickcolor = ImColor(181, 132, 12); break;
			default: pickcolor = ImColor(0, 0, 0); break;
			}
		}

		CachedPlayer cache;
		cache.Pawn = PawnPrivate;
		cache.PlayerState = PlayerState;
		cache.PlayerStates = PlayerStates;
		cache.AcknowledgedPawn = AcknowlegedPawn;
		cache.TargetedPawn = targeted_fort_pawn;
		cache.Mesh = Mesh;
		cache.WeaponName = EnemyPlayerWeapon;
		cache.PickColor = pickcolor;
		//list.push_back(std::move(cache));
		tempList.push_back(cache);

	}

    // Update the cached player list atomically
    {
        cachedPlayerList.clear();
        cachedPlayerList = tempList;
    }
}


void CacheSystem::StartCacheThreads() {
    if (isRunning) return;
    
    isRunning = true;
	isReady = true;
    std::thread playerCacheThread(PlayerCacheThread);
    playerCacheThread.detach();
    
    // Start loot caching thread
    std::thread lootCacheThread(LootCacheThread);
    lootCacheThread.detach();
}

void CacheSystem::StopCacheThreads() {
    isRunning = false;
}

void CacheSystem::PlayerCacheThread() {

    while (isRunning) {
        if (isReady) {
			UpdatePlayerCache();
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(500)); // Adjust for performance
    }
}

void CacheSystem::LootCacheThread() {
    while (isRunning) {
        if (isReady) {
			LootSystem::ProcessLoot();
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(500)); // Adjust for performance
    }
}
