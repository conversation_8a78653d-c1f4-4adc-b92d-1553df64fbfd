#include "HotkeySystem.h"
#include "../../Settings/Settings.h"
#include "../../Features/Config/Config.h"
#include <iostream>

// Initialize static members
std::map<std::string, HotkeyBinding> HotkeySystem::bindings;
bool HotkeySystem::debugMode = false;
bool HotkeySystem::initialized = false;
bool HotkeySystem::enabled = true;
bool HotkeySystem::menuOpen = false;

void HotkeySystem::Initialize(bool debug) {
    Initialize(debug, false);
}

void HotkeySystem::Initialize(bool debug, bool forceReload) {
    if (initialized && !forceReload) {
        return;
    }

    // Set debug mode based on parameter
    debugMode = debug;
    initialized = true;

    // Initialize the input manager
    InputManager::Initialize(debug);

    // First, clear any existing bindings
    bindings.clear();

    // Make sure the Keys struct is properly loaded from Settings
    Keys = Settings.Keys.Keys;

    // Ensure the feature states are properly initialized
    // This ensures that the hotkey system has the correct initial state
    if (Keys.AimbotEnableMode) {
        // Toggle mode - keep current state
    } else {
        // Hold mode - set to false initially
        Aimbot.Enable = false;
    }

    if (Keys.AimbotAimLockMode) {
        // Toggle mode - keep current state
    } else {
        // Hold mode - set to false initially
        Aimbot.AimLock = false;
    }

    if (Keys.PlayerEspEnableMode) {
        // Toggle mode - keep current state
    } else {
        // Hold mode - set to false initially
        Players.Enable = false;
    }

    // Register all hotkeys
    RegisterAllHotkeys();

    // Monitor common keys that might be used for hotkeys
    InputManager::MonitorKey('G');
    InputManager::MonitorKey('F');
    InputManager::MonitorKey('H');
    InputManager::MonitorKey('T');
    InputManager::MonitorKey('Y');
    InputManager::MonitorKey('V');
    InputManager::MonitorKey('B');
    InputManager::MonitorKey('D');
    InputManager::MonitorKey(VK_SPACE);
    InputManager::MonitorKey(VK_LBUTTON);
    InputManager::MonitorKey(VK_RBUTTON);

    // Monitor any keys that are set in the Keys struct
    if (Keys.AimbotEnable > 0) InputManager::MonitorKey(Keys.AimbotEnable);
    if (Keys.AimbotAimLock > 0) InputManager::MonitorKey(Keys.AimbotAimLock);
    if (Keys.AimbotPrediction > 0) InputManager::MonitorKey(Keys.AimbotPrediction);
    if (Keys.AimbotSaveTarget > 0) InputManager::MonitorKey(Keys.AimbotSaveTarget);
    if (Keys.AimbotVisibilityCheck > 0) InputManager::MonitorKey(Keys.AimbotVisibilityCheck);
    if (Keys.AimbotHumanizedSmooth > 0) InputManager::MonitorKey(Keys.AimbotHumanizedSmooth);
    if (Keys.AimbotIgnoreDowned > 0) InputManager::MonitorKey(Keys.AimbotIgnoreDowned);
    if (Keys.AimbotPlayerAi > 0) InputManager::MonitorKey(Keys.AimbotPlayerAi);
    if (Keys.AimbotWeaponOnly > 0) InputManager::MonitorKey(Keys.AimbotWeaponOnly);
    if (Keys.AimbotDrawFov > 0) InputManager::MonitorKey(Keys.AimbotDrawFov);
    if (Keys.AimbotDrawCrossHair > 0) InputManager::MonitorKey(Keys.AimbotDrawCrossHair);
    if (Keys.AimbotDrawTarget > 0) InputManager::MonitorKey(Keys.AimbotDrawTarget);

    if (Keys.PlayerEspEnable > 0) InputManager::MonitorKey(Keys.PlayerEspEnable);
    if (Keys.PlayerEspBox > 0) InputManager::MonitorKey(Keys.PlayerEspBox);
    if (Keys.PlayerEspSkeleton > 0) InputManager::MonitorKey(Keys.PlayerEspSkeleton);
    if (Keys.PlayerEspHeadCircle > 0) InputManager::MonitorKey(Keys.PlayerEspHeadCircle);
    if (Keys.PlayerEspLines > 0) InputManager::MonitorKey(Keys.PlayerEspLines);
    if (Keys.PlayerEspDistance > 0) InputManager::MonitorKey(Keys.PlayerEspDistance);
    if (Keys.PlayerEspNickName > 0) InputManager::MonitorKey(Keys.PlayerEspNickName);
    if (Keys.PlayerEspPlatform > 0) InputManager::MonitorKey(Keys.PlayerEspPlatform);
    if (Keys.PlayerEspKills > 0) InputManager::MonitorKey(Keys.PlayerEspKills);
    if (Keys.PlayerEspLevel > 0) InputManager::MonitorKey(Keys.PlayerEspLevel);
    if (Keys.PlayerEspRank > 0) InputManager::MonitorKey(Keys.PlayerEspRank);
    if (Keys.PlayerEspIgnoreDowned > 0) InputManager::MonitorKey(Keys.PlayerEspIgnoreDowned);

    if (Keys.ItemEspEnable > 0) InputManager::MonitorKey(Keys.ItemEspEnable);
    if (Keys.ItemConsumableEnable > 0) InputManager::MonitorKey(Keys.ItemConsumableEnable);
    if (Keys.ItemWeaponEnable > 0) InputManager::MonitorKey(Keys.ItemWeaponEnable);
    if (Keys.ItemAmmoEnable > 0) InputManager::MonitorKey(Keys.ItemAmmoEnable);
    if (Keys.ItemOtherEnable > 0) InputManager::MonitorKey(Keys.ItemOtherEnable);

    if (Keys.RadarEnable > 0) InputManager::MonitorKey(Keys.RadarEnable);
    if (Keys.RadarDistance > 0) InputManager::MonitorKey(Keys.RadarDistance);
    if (Keys.RadarVisibleColor > 0) InputManager::MonitorKey(Keys.RadarVisibleColor);
    if (Keys.RadarClosestColor > 0) InputManager::MonitorKey(Keys.RadarClosestColor);
    if (Keys.RadarAimingAtMeColor > 0) InputManager::MonitorKey(Keys.RadarAimingAtMeColor);

    // No need to print debug info

    // Verify mode consistency at startup to ensure all modes are correct
    VerifyModeConsistency();

    // Force save settings to ensure all modes are persisted correctly
    SaveSettings();
}

void HotkeySystem::RegisterAllHotkeys() {
    // Register Aimbot features
    RegisterHotkey("AimbotEnable", "Aimbot", &Aimbot.Enable, Keys.AimbotEnable, Keys.AimbotEnableMode);
    RegisterHotkey("AimbotAimLock", "Aimbot", &Aimbot.AimLock, Keys.AimbotAimLock, Keys.AimbotAimLockMode);
    RegisterHotkey("AimbotPrediction", "Aimbot", &Aimbot.Predict, Keys.AimbotPrediction, Keys.AimbotPredictionMode);
    RegisterHotkey("AimbotSaveTarget", "Aimbot", &Aimbot.SaveTarget, Keys.AimbotSaveTarget, Keys.AimbotSaveTargetMode);
    RegisterHotkey("AimbotVisibilityCheck", "Aimbot", &Aimbot.VisibilityCheck, Keys.AimbotVisibilityCheck, Keys.AimbotVisibilityCheckMode);
    RegisterHotkey("AimbotHumanizedSmooth", "Aimbot", &Aimbot.HumanizedSmooth, Keys.AimbotHumanizedSmooth, Keys.AimbotHumanizedSmoothMode);
    RegisterHotkey("AimbotIgnoreDowned", "Aimbot", &Aimbot.IgnoreDowned, Keys.AimbotIgnoreDowned, Keys.AimbotIgnoreDownedMode);
    RegisterHotkey("AimbotPlayerAi", "Aimbot", &Aimbot.PlayerAi, Keys.AimbotPlayerAi, Keys.AimbotPlayerAiMode);
    RegisterHotkey("AimbotWeaponOnly", "Aimbot", &Aimbot.WeaponOnly, Keys.AimbotWeaponOnly, Keys.AimbotWeaponOnlyMode);
    RegisterHotkey("AimbotDrawFov", "Aimbot", &Aimbot.DrawFov, Keys.AimbotDrawFov, Keys.AimbotDrawFovMode);
    RegisterHotkey("AimbotDrawCrossHair", "Aimbot", &Aimbot.DrawCrossHair, Keys.AimbotDrawCrossHair, Keys.AimbotDrawCrossHairMode);
    RegisterHotkey("AimbotDrawTarget", "Aimbot", &Aimbot.DrawTarget, Keys.AimbotDrawTarget, Keys.AimbotDrawTargetMode);

    // Register Player ESP features
    RegisterHotkey("PlayerEspEnable", "ESP", &Players.Enable, Keys.PlayerEspEnable, Keys.PlayerEspEnableMode);
    RegisterHotkey("PlayerEspBox", "ESP", &Players.Box, Keys.PlayerEspBox, Keys.PlayerEspBoxMode);
    RegisterHotkey("PlayerEspSkeleton", "ESP", &Players.Skeleton, Keys.PlayerEspSkeleton, Keys.PlayerEspSkeletonMode);
    RegisterHotkey("PlayerEspHeadCircle", "ESP", &Players.HeadCircle, Keys.PlayerEspHeadCircle, Keys.PlayerEspHeadCircleMode);
    RegisterHotkey("PlayerEspLines", "ESP", &Players.Lines, Keys.PlayerEspLines, Keys.PlayerEspLinesMode);
    RegisterHotkey("PlayerEspDistance", "ESP", &Players.Distance, Keys.PlayerEspDistance, Keys.PlayerEspDistanceMode);
    RegisterHotkey("PlayerEspNickName", "ESP", &Players.NickName, Keys.PlayerEspNickName, Keys.PlayerEspNickNameMode);
    RegisterHotkey("PlayerEspPlatform", "ESP", &Players.Platform, Keys.PlayerEspPlatform, Keys.PlayerEspPlatformMode);
    RegisterHotkey("PlayerEspKills", "ESP", &Players.Kills, Keys.PlayerEspKills, Keys.PlayerEspKillsMode);
    RegisterHotkey("PlayerEspLevel", "ESP", &Players.Level, Keys.PlayerEspLevel, Keys.PlayerEspLevelMode);
    RegisterHotkey("PlayerEspRank", "ESP", &Players.Rank, Keys.PlayerEspRank, Keys.PlayerEspRankMode);
    RegisterHotkey("PlayerEspIgnoreDowned", "ESP", &Players.IgnoreDowned, Keys.PlayerEspIgnoreDowned, Keys.PlayerEspIgnoreDownedMode);

    // Register Item ESP features
    RegisterHotkey("ItemEspEnable", "ESP", &Items.Consumable.Enable, Keys.ItemEspEnable, Keys.ItemEspEnableMode);
    RegisterHotkey("ItemConsumableEnable", "ESP", &Items.Consumable.Enable, Keys.ItemConsumableEnable, Keys.ItemConsumableEnableMode);
    RegisterHotkey("ItemWeaponEnable", "ESP", &Items.Weapon.Enable, Keys.ItemWeaponEnable, Keys.ItemWeaponEnableMode);
    RegisterHotkey("ItemAmmoEnable", "ESP", &Items.Ammo.Enable, Keys.ItemAmmoEnable, Keys.ItemAmmoEnableMode);
    RegisterHotkey("ItemOtherEnable", "ESP", &Items.Other.Enable, Keys.ItemOtherEnable, Keys.ItemOtherEnableMode);

    // Register Radar features
    RegisterHotkey("RadarEnable", "Radar", &Radar.Enable, Keys.RadarEnable, Keys.RadarEnableMode);
    RegisterHotkey("RadarDistance", "Radar", &Radar.Distance, Keys.RadarDistance, Keys.RadarDistanceMode);
    RegisterHotkey("RadarVisibleColor", "Radar", &Radar.VisibleColor, Keys.RadarVisibleColor, Keys.RadarVisibleColorMode);
    RegisterHotkey("RadarClosestColor", "Radar", &Radar.ClosestColor, Keys.RadarClosestColor, Keys.RadarClosestColorMode);
    RegisterHotkey("RadarAimingAtMeColor", "Radar", &Radar.AimingAtMeColor, Keys.RadarAimingAtMeColor, Keys.RadarAimingAtMeColorMode);

    // Monitor any keys that are set in the Keys struct
    if (Keys.AimbotEnable > 0) InputManager::MonitorKey(Keys.AimbotEnable);
    if (Keys.AimbotAimLock > 0) InputManager::MonitorKey(Keys.AimbotAimLock);
    if (Keys.AimbotPrediction > 0) InputManager::MonitorKey(Keys.AimbotPrediction);
    if (Keys.AimbotSaveTarget > 0) InputManager::MonitorKey(Keys.AimbotSaveTarget);
    if (Keys.AimbotVisibilityCheck > 0) InputManager::MonitorKey(Keys.AimbotVisibilityCheck);
    if (Keys.AimbotHumanizedSmooth > 0) InputManager::MonitorKey(Keys.AimbotHumanizedSmooth);
    if (Keys.AimbotIgnoreDowned > 0) InputManager::MonitorKey(Keys.AimbotIgnoreDowned);
    if (Keys.AimbotPlayerAi > 0) InputManager::MonitorKey(Keys.AimbotPlayerAi);
    if (Keys.AimbotWeaponOnly > 0) InputManager::MonitorKey(Keys.AimbotWeaponOnly);
    if (Keys.AimbotDrawFov > 0) InputManager::MonitorKey(Keys.AimbotDrawFov);
    if (Keys.AimbotDrawCrossHair > 0) InputManager::MonitorKey(Keys.AimbotDrawCrossHair);
    if (Keys.AimbotDrawTarget > 0) InputManager::MonitorKey(Keys.AimbotDrawTarget);

    if (Keys.PlayerEspEnable > 0) InputManager::MonitorKey(Keys.PlayerEspEnable);
    if (Keys.PlayerEspBox > 0) InputManager::MonitorKey(Keys.PlayerEspBox);
    if (Keys.PlayerEspSkeleton > 0) InputManager::MonitorKey(Keys.PlayerEspSkeleton);
    if (Keys.PlayerEspHeadCircle > 0) InputManager::MonitorKey(Keys.PlayerEspHeadCircle);
    if (Keys.PlayerEspLines > 0) InputManager::MonitorKey(Keys.PlayerEspLines);
    if (Keys.PlayerEspDistance > 0) InputManager::MonitorKey(Keys.PlayerEspDistance);
    if (Keys.PlayerEspNickName > 0) InputManager::MonitorKey(Keys.PlayerEspNickName);
    if (Keys.PlayerEspPlatform > 0) InputManager::MonitorKey(Keys.PlayerEspPlatform);
    if (Keys.PlayerEspKills > 0) InputManager::MonitorKey(Keys.PlayerEspKills);
    if (Keys.PlayerEspLevel > 0) InputManager::MonitorKey(Keys.PlayerEspLevel);
    if (Keys.PlayerEspRank > 0) InputManager::MonitorKey(Keys.PlayerEspRank);
    if (Keys.PlayerEspIgnoreDowned > 0) InputManager::MonitorKey(Keys.PlayerEspIgnoreDowned);

    if (Keys.ItemEspEnable > 0) InputManager::MonitorKey(Keys.ItemEspEnable);
    if (Keys.ItemConsumableEnable > 0) InputManager::MonitorKey(Keys.ItemConsumableEnable);
    if (Keys.ItemWeaponEnable > 0) InputManager::MonitorKey(Keys.ItemWeaponEnable);
    if (Keys.ItemAmmoEnable > 0) InputManager::MonitorKey(Keys.ItemAmmoEnable);
    if (Keys.ItemOtherEnable > 0) InputManager::MonitorKey(Keys.ItemOtherEnable);

    if (Keys.RadarEnable > 0) InputManager::MonitorKey(Keys.RadarEnable);
    if (Keys.RadarDistance > 0) InputManager::MonitorKey(Keys.RadarDistance);
    if (Keys.RadarVisibleColor > 0) InputManager::MonitorKey(Keys.RadarVisibleColor);
    if (Keys.RadarClosestColor > 0) InputManager::MonitorKey(Keys.RadarClosestColor);
    if (Keys.RadarAimingAtMeColor > 0) InputManager::MonitorKey(Keys.RadarAimingAtMeColor);
}

void HotkeySystem::RegisterHotkey(const std::string& name, const std::string& category, bool* state, int keyCode, bool toggleMode) {
    bindings[name] = HotkeyBinding(name, category, state, keyCode, toggleMode);

    // Monitor the key if it's valid
    if (keyCode > 0) {
        InputManager::MonitorKey(keyCode);
    }

    // Hotkey registered successfully
}

void HotkeySystem::Update() {
    if (!initialized) {
        std::cout << "HotkeySystem not initialized, initializing now..." << std::endl;
        Initialize(true);
        enabled = true;
    }

    // Update the input manager
    InputManager::Update();

    // Always process hotkeys if enabled, regardless of menu state
    // The ProcessHotkeys function will handle the menu state internally
    if (enabled) {
        // Process hotkeys
        ProcessHotkeys();
    }
}

void HotkeySystem::ProcessHotkeys() {
    // Make sure the hotkey system is properly initialized
    if (!initialized) {
        Initialize(false);
    }

    // Skip processing if hotkey system is disabled
    if (!enabled) {
        return;
    }

    // Force update the input manager to get the latest key states
    InputManager::Update();

    // Process each binding directly
    for (auto& [name, binding] : bindings) {
        // Skip if no hotkey is set or no state pointer
        if (binding.keyCode == 0 || binding.statePtr == nullptr) {
            continue;
        }

        // Get key state using direct Windows API calls for maximum reliability
        SHORT keyState = GetAsyncKeyState(binding.keyCode);
        bool isKeyDown = (keyState & 0x8000) != 0;
        bool wasKeyPressed = (keyState & 0x0001) != 0;

        // Also get key state from InputManager for comparison
        bool isKeyDownIM = InputManager::IsKeyDown(binding.keyCode);
        bool justPressedIM = InputManager::IsKeyJustPressed(binding.keyCode);

        // Use the most reliable detection method
        bool keyIsDown = isKeyDown || isKeyDownIM;
        bool keyJustPressed = wasKeyPressed || justPressedIM;

        // Get the actual toggle mode from the Keys struct to ensure it's correct
        bool isToggleMode = binding.toggleMode;

        // Get the correct mode from the Keys struct based on feature name
        // This ensures we always use the most up-to-date mode setting
        if (name.find("Aimbot") == 0) {
            // Aimbot features
            if (name == "AimbotEnable") isToggleMode = Keys.AimbotEnableMode;
            else if (name == "AimbotAimLock") isToggleMode = Keys.AimbotAimLockMode;
            else if (name == "AimbotPrediction") isToggleMode = Keys.AimbotPredictionMode;
            else if (name == "AimbotSaveTarget") isToggleMode = Keys.AimbotSaveTargetMode;
            else if (name == "AimbotVisibilityCheck") isToggleMode = Keys.AimbotVisibilityCheckMode;
            else if (name == "AimbotHumanizedSmooth") isToggleMode = Keys.AimbotHumanizedSmoothMode;
            else if (name == "AimbotIgnoreDowned") isToggleMode = Keys.AimbotIgnoreDownedMode;
            else if (name == "AimbotPlayerAi") isToggleMode = Keys.AimbotPlayerAiMode;
            else if (name == "AimbotWeaponOnly") isToggleMode = Keys.AimbotWeaponOnlyMode;
            else if (name == "AimbotDrawFov") isToggleMode = Keys.AimbotDrawFovMode;
            else if (name == "AimbotDrawCrossHair") isToggleMode = Keys.AimbotDrawCrossHairMode;
            else if (name == "AimbotDrawTarget") isToggleMode = Keys.AimbotDrawTargetMode;
        }
        else if (name.find("PlayerEsp") == 0) {
            // Player ESP features
            if (name == "PlayerEspEnable") isToggleMode = Keys.PlayerEspEnableMode;
            else if (name == "PlayerEspBox") isToggleMode = Keys.PlayerEspBoxMode;
            else if (name == "PlayerEspSkeleton") isToggleMode = Keys.PlayerEspSkeletonMode;
            else if (name == "PlayerEspHeadCircle") isToggleMode = Keys.PlayerEspHeadCircleMode;
            else if (name == "PlayerEspLines") isToggleMode = Keys.PlayerEspLinesMode;
            else if (name == "PlayerEspDistance") isToggleMode = Keys.PlayerEspDistanceMode;
            else if (name == "PlayerEspNickName") isToggleMode = Keys.PlayerEspNickNameMode;
            else if (name == "PlayerEspPlatform") isToggleMode = Keys.PlayerEspPlatformMode;
            else if (name == "PlayerEspKills") isToggleMode = Keys.PlayerEspKillsMode;
            else if (name == "PlayerEspLevel") isToggleMode = Keys.PlayerEspLevelMode;
            else if (name == "PlayerEspRank") isToggleMode = Keys.PlayerEspRankMode;
            else if (name == "PlayerEspIgnoreDowned") isToggleMode = Keys.PlayerEspIgnoreDownedMode;
        }
        else if (name.find("Item") == 0) {
            // Item ESP features
            if (name == "ItemEspEnable") isToggleMode = Keys.ItemEspEnableMode;
            else if (name == "ItemConsumableEnable") isToggleMode = Keys.ItemConsumableEnableMode;
            else if (name == "ItemWeaponEnable") isToggleMode = Keys.ItemWeaponEnableMode;
            else if (name == "ItemAmmoEnable") isToggleMode = Keys.ItemAmmoEnableMode;
            else if (name == "ItemOtherEnable") isToggleMode = Keys.ItemOtherEnableMode;
        }
        else if (name.find("Radar") == 0) {
            // Radar features
            if (name == "RadarEnable") isToggleMode = Keys.RadarEnableMode;
            else if (name == "RadarDistance") isToggleMode = Keys.RadarDistanceMode;
            else if (name == "RadarVisibleColor") isToggleMode = Keys.RadarVisibleColorMode;
            else if (name == "RadarClosestColor") isToggleMode = Keys.RadarClosestColorMode;
            else if (name == "RadarAimingAtMeColor") isToggleMode = Keys.RadarAimingAtMeColorMode;
        }

        // Update the binding's toggle mode to match the Keys struct
        if (binding.toggleMode != isToggleMode) {
            binding.toggleMode = isToggleMode;
        }

        // Process based on mode - fixed for correct toggle/hold behavior
        if (isToggleMode) {  // TOGGLE MODE: Toggle state on key press
            // Only process toggle on key press (not release)
            if (keyJustPressed) {
                bool oldState = *binding.statePtr;
                bool newState = !oldState;  // Toggle the state

                // Update the state pointer directly
                *binding.statePtr = newState;

                // Add notification for the user
                g_NotificationManager.AddChange("Hotkey", name, oldState, newState);

                // Force update the feature in the global state
                UpdateFeatureState(name, newState);

                // Save settings
                SaveSettings();
            }
        } else {  // HOLD MODE: State matches key state (ON when key is down, OFF when key is up)
            bool oldState = *binding.statePtr;
            bool newState = keyIsDown;  // State should match key state

            // Always update the state to match the key state for hold mode
            // This ensures the feature is only active while the key is held
            if (oldState != newState) {
                // Update the state pointer directly
                *binding.statePtr = newState;

                // Add notification for the user
                g_NotificationManager.AddChange("Hotkey", name, oldState, newState);

                // Force update the feature in the global state
                UpdateFeatureState(name, newState);
            }

            // For hold mode, we don't need to save settings on every state change
            // as the state is directly tied to the key state
            // Only save periodically to avoid excessive disk writes
            static int holdModeSaveCounter = 0;
            if (++holdModeSaveCounter >= 100) { // Save every 100 hold mode changes
                holdModeSaveCounter = 0;
                SaveSettings();
            }
        }
    }

    // No need to print debug info
}

void HotkeySystem::SetMenuOpen(bool open) {
    // Only take action if the state is changing
    if (menuOpen != open) {
        bool wasOpen = menuOpen;
        menuOpen = open;

        // If the menu was just closed, force sync all feature states
        if (wasOpen && !open) {
            // Force update all features to ensure they're in sync
            for (auto& [name, binding] : bindings) {
                if (binding.statePtr) {
                    bool currentState = *binding.statePtr;
                    UpdateFeatureState(name, currentState);
                }
            }

            // Force save settings to ensure they're persisted
            SaveSettings();
        }
    }
}

void HotkeySystem::SetEnabled(bool enable) {
    enabled = enable;
}

HotkeyBinding* HotkeySystem::GetHotkey(const std::string& name) {
    auto it = bindings.find(name);
    if (it != bindings.end()) {
        return &it->second;
    }
    return nullptr;
}

const std::map<std::string, HotkeyBinding>& HotkeySystem::GetAllHotkeys() {
    return bindings;
}

void HotkeySystem::UpdateHotkey(const std::string& name, int keyCode, bool toggleMode) {
    auto it = bindings.find(name);
    if (it != bindings.end()) {
        // Unmonitor the old key if it's valid
        if (it->second.keyCode > 0) {
            InputManager::UnmonitorKey(it->second.keyCode);
        }

        // Update the binding
        it->second.keyCode = keyCode;
        it->second.toggleMode = toggleMode;

        // Monitor the new key if it's valid
        if (keyCode > 0) {
            InputManager::MonitorKey(keyCode);

            // Also monitor lowercase/uppercase variants for letter keys
            if (keyCode >= 'A' && keyCode <= 'Z') {
                // Monitor lowercase variant (a-z)
                int lowercaseKey = keyCode + ('a' - 'A');
                InputManager::MonitorKey(lowercaseKey);

                // Monitor lowercase variant silently
            }
            else if (keyCode >= 'a' && keyCode <= 'z') {
                // Monitor uppercase variant (A-Z)
                int uppercaseKey = keyCode - ('a' - 'A');
                InputManager::MonitorKey(uppercaseKey);

                // Monitor uppercase variant silently
            }
        }

        // Update the Keys struct directly to ensure it has the correct mode
        // This is a comprehensive update for all features
        if (name.find("Aimbot") == 0) {
            // Aimbot features
            if (name == "AimbotEnable") {
                Keys.AimbotEnable = keyCode;
                Keys.AimbotEnableMode = toggleMode;
            }
            else if (name == "AimbotAimLock") {
                Keys.AimbotAimLock = keyCode;
                Keys.AimbotAimLockMode = toggleMode;
            }
            else if (name == "AimbotPrediction") {
                Keys.AimbotPrediction = keyCode;
                Keys.AimbotPredictionMode = toggleMode;
            }
            else if (name == "AimbotSaveTarget") {
                Keys.AimbotSaveTarget = keyCode;
                Keys.AimbotSaveTargetMode = toggleMode;
            }
            else if (name == "AimbotVisibilityCheck") {
                Keys.AimbotVisibilityCheck = keyCode;
                Keys.AimbotVisibilityCheckMode = toggleMode;
            }
            else if (name == "AimbotHumanizedSmooth") {
                Keys.AimbotHumanizedSmooth = keyCode;
                Keys.AimbotHumanizedSmoothMode = toggleMode;
            }
            else if (name == "AimbotIgnoreDowned") {
                Keys.AimbotIgnoreDowned = keyCode;
                Keys.AimbotIgnoreDownedMode = toggleMode;
            }
            else if (name == "AimbotPlayerAi") {
                Keys.AimbotPlayerAi = keyCode;
                Keys.AimbotPlayerAiMode = toggleMode;
            }
            else if (name == "AimbotWeaponOnly") {
                Keys.AimbotWeaponOnly = keyCode;
                Keys.AimbotWeaponOnlyMode = toggleMode;
            }
            else if (name == "AimbotDrawFov") {
                Keys.AimbotDrawFov = keyCode;
                Keys.AimbotDrawFovMode = toggleMode;
            }
            else if (name == "AimbotDrawCrossHair") {
                Keys.AimbotDrawCrossHair = keyCode;
                Keys.AimbotDrawCrossHairMode = toggleMode;
            }
            else if (name == "AimbotDrawTarget") {
                Keys.AimbotDrawTarget = keyCode;
                Keys.AimbotDrawTargetMode = toggleMode;
            }
        }
        else if (name.find("PlayerEsp") == 0) {
            // Player ESP features
            if (name == "PlayerEspEnable") {
                Keys.PlayerEspEnable = keyCode;
                Keys.PlayerEspEnableMode = toggleMode;
            }
            else if (name == "PlayerEspBox") {
                Keys.PlayerEspBox = keyCode;
                Keys.PlayerEspBoxMode = toggleMode;
            }
            else if (name == "PlayerEspSkeleton") {
                Keys.PlayerEspSkeleton = keyCode;
                Keys.PlayerEspSkeletonMode = toggleMode;
            }
            else if (name == "PlayerEspHeadCircle") {
                Keys.PlayerEspHeadCircle = keyCode;
                Keys.PlayerEspHeadCircleMode = toggleMode;
            }
            else if (name == "PlayerEspLines") {
                Keys.PlayerEspLines = keyCode;
                Keys.PlayerEspLinesMode = toggleMode;
            }
            else if (name == "PlayerEspDistance") {
                Keys.PlayerEspDistance = keyCode;
                Keys.PlayerEspDistanceMode = toggleMode;
            }
            else if (name == "PlayerEspNickName") {
                Keys.PlayerEspNickName = keyCode;
                Keys.PlayerEspNickNameMode = toggleMode;
            }
            else if (name == "PlayerEspPlatform") {
                Keys.PlayerEspPlatform = keyCode;
                Keys.PlayerEspPlatformMode = toggleMode;
            }
            else if (name == "PlayerEspKills") {
                Keys.PlayerEspKills = keyCode;
                Keys.PlayerEspKillsMode = toggleMode;
            }
            else if (name == "PlayerEspLevel") {
                Keys.PlayerEspLevel = keyCode;
                Keys.PlayerEspLevelMode = toggleMode;
            }
            else if (name == "PlayerEspRank") {
                Keys.PlayerEspRank = keyCode;
                Keys.PlayerEspRankMode = toggleMode;
            }
            else if (name == "PlayerEspIgnoreDowned") {
                Keys.PlayerEspIgnoreDowned = keyCode;
                Keys.PlayerEspIgnoreDownedMode = toggleMode;
            }
        }
        else if (name.find("Item") == 0) {
            // Item ESP features
            if (name == "ItemEspEnable") {
                Keys.ItemEspEnable = keyCode;
                Keys.ItemEspEnableMode = toggleMode;
            }
            else if (name == "ItemConsumableEnable") {
                Keys.ItemConsumableEnable = keyCode;
                Keys.ItemConsumableEnableMode = toggleMode;
            }
            else if (name == "ItemWeaponEnable") {
                Keys.ItemWeaponEnable = keyCode;
                Keys.ItemWeaponEnableMode = toggleMode;
            }
            else if (name == "ItemAmmoEnable") {
                Keys.ItemAmmoEnable = keyCode;
                Keys.ItemAmmoEnableMode = toggleMode;
            }
            else if (name == "ItemOtherEnable") {
                Keys.ItemOtherEnable = keyCode;
                Keys.ItemOtherEnableMode = toggleMode;
            }
        }
        else if (name.find("Radar") == 0) {
            // Radar features
            if (name == "RadarEnable") {
                Keys.RadarEnable = keyCode;
                Keys.RadarEnableMode = toggleMode;
            }
            else if (name == "RadarDistance") {
                Keys.RadarDistance = keyCode;
                Keys.RadarDistanceMode = toggleMode;
            }
            else if (name == "RadarVisibleColor") {
                Keys.RadarVisibleColor = keyCode;
                Keys.RadarVisibleColorMode = toggleMode;
            }
            else if (name == "RadarClosestColor") {
                Keys.RadarClosestColor = keyCode;
                Keys.RadarClosestColorMode = toggleMode;
            }
            else if (name == "RadarAimingAtMeColor") {
                Keys.RadarAimingAtMeColor = keyCode;
                Keys.RadarAimingAtMeColorMode = toggleMode;
            }
        }

        // Keys struct updated

        // Update the feature state based on the new mode
        if (it->second.statePtr != nullptr) {
            // First, update the toggle mode in the binding
            it->second.toggleMode = toggleMode;

            if (toggleMode) {  // TOGGLE MODE
                // Toggle mode - keep current state

                // Make sure the state is consistent across all data structures
                bool currentState = *(it->second.statePtr);
                UpdateFeatureState(name, currentState);
            } else {  // HOLD MODE
                // Hold mode - set to match the current key state
                bool keyDown = InputManager::IsKeyDown(keyCode);
                *(it->second.statePtr) = keyDown;

                // Use the helper function to update the feature state
                UpdateFeatureState(name, keyDown);
            }

            // Update the Keys struct directly with the new mode
            if (name == "AimbotEnable") Keys.AimbotEnableMode = toggleMode;
            else if (name == "AimbotAimLock") Keys.AimbotAimLockMode = toggleMode;
            else if (name == "AimbotPrediction") Keys.AimbotPredictionMode = toggleMode;
            else if (name == "AimbotSaveTarget") Keys.AimbotSaveTargetMode = toggleMode;
            else if (name == "AimbotVisibilityCheck") Keys.AimbotVisibilityCheckMode = toggleMode;
            else if (name == "AimbotHumanizedSmooth") Keys.AimbotHumanizedSmoothMode = toggleMode;
            else if (name == "AimbotIgnoreDowned") Keys.AimbotIgnoreDownedMode = toggleMode;
            else if (name == "AimbotPlayerAi") Keys.AimbotPlayerAiMode = toggleMode;
            else if (name == "AimbotWeaponOnly") Keys.AimbotWeaponOnlyMode = toggleMode;
            else if (name == "AimbotDrawFov") Keys.AimbotDrawFovMode = toggleMode;
            else if (name == "AimbotDrawCrossHair") Keys.AimbotDrawCrossHairMode = toggleMode;
            else if (name == "AimbotDrawTarget") Keys.AimbotDrawTargetMode = toggleMode;
            else if (name == "PlayerEspEnable") Keys.PlayerEspEnableMode = toggleMode;
            else if (name == "PlayerEspBox") Keys.PlayerEspBoxMode = toggleMode;
            else if (name == "PlayerEspSkeleton") Keys.PlayerEspSkeletonMode = toggleMode;
            else if (name == "PlayerEspHeadCircle") Keys.PlayerEspHeadCircleMode = toggleMode;
            else if (name == "PlayerEspLines") Keys.PlayerEspLinesMode = toggleMode;
            else if (name == "PlayerEspDistance") Keys.PlayerEspDistanceMode = toggleMode;
            else if (name == "PlayerEspNickName") Keys.PlayerEspNickNameMode = toggleMode;
            else if (name == "PlayerEspPlatform") Keys.PlayerEspPlatformMode = toggleMode;
            else if (name == "PlayerEspKills") Keys.PlayerEspKillsMode = toggleMode;
            else if (name == "PlayerEspLevel") Keys.PlayerEspLevelMode = toggleMode;
            else if (name == "PlayerEspRank") Keys.PlayerEspRankMode = toggleMode;
            else if (name == "PlayerEspIgnoreDowned") Keys.PlayerEspIgnoreDownedMode = toggleMode;
            else if (name == "ItemEspEnable") Keys.ItemEspEnableMode = toggleMode;
            else if (name == "ItemConsumableEnable") Keys.ItemConsumableEnableMode = toggleMode;
            else if (name == "ItemWeaponEnable") Keys.ItemWeaponEnableMode = toggleMode;
            else if (name == "ItemAmmoEnable") Keys.ItemAmmoEnableMode = toggleMode;
            else if (name == "ItemOtherEnable") Keys.ItemOtherEnableMode = toggleMode;
            else if (name == "RadarEnable") Keys.RadarEnableMode = toggleMode;
            else if (name == "RadarDistance") Keys.RadarDistanceMode = toggleMode;
            else if (name == "RadarVisibleColor") Keys.RadarVisibleColorMode = toggleMode;
            else if (name == "RadarClosestColor") Keys.RadarClosestColorMode = toggleMode;
            else if (name == "RadarAimingAtMeColor") Keys.RadarAimingAtMeColorMode = toggleMode;

            // Update Settings struct immediately
            Settings.Keys.Keys = Keys;
        }

        // Update the Keys struct
        SyncWithKeysStruct();

        // Update Settings struct
        Settings.Keys.Keys = Keys;

        // Save settings
        SaveSettings();

        // Verify mode consistency
        VerifyModeConsistency();
    }
}

// Helper function to get the mode from the Keys struct
bool HotkeySystem::GetModeFromKeysStruct(const std::string& name) {
    if (name == "AimbotEnable") return Keys.AimbotEnableMode;
    else if (name == "AimbotAimLock") return Keys.AimbotAimLockMode;
    else if (name == "AimbotPrediction") return Keys.AimbotPredictionMode;
    else if (name == "AimbotSaveTarget") return Keys.AimbotSaveTargetMode;
    else if (name == "AimbotVisibilityCheck") return Keys.AimbotVisibilityCheckMode;
    else if (name == "AimbotHumanizedSmooth") return Keys.AimbotHumanizedSmoothMode;
    else if (name == "AimbotIgnoreDowned") return Keys.AimbotIgnoreDownedMode;
    else if (name == "AimbotPlayerAi") return Keys.AimbotPlayerAiMode;
    else if (name == "AimbotWeaponOnly") return Keys.AimbotWeaponOnlyMode;
    else if (name == "AimbotDrawFov") return Keys.AimbotDrawFovMode;
    else if (name == "AimbotDrawCrossHair") return Keys.AimbotDrawCrossHairMode;
    else if (name == "AimbotDrawTarget") return Keys.AimbotDrawTargetMode;
    else if (name == "PlayerEspEnable") return Keys.PlayerEspEnableMode;
    else if (name == "PlayerEspBox") return Keys.PlayerEspBoxMode;
    else if (name == "PlayerEspSkeleton") return Keys.PlayerEspSkeletonMode;
    else if (name == "PlayerEspHeadCircle") return Keys.PlayerEspHeadCircleMode;
    else if (name == "PlayerEspLines") return Keys.PlayerEspLinesMode;
    else if (name == "PlayerEspDistance") return Keys.PlayerEspDistanceMode;
    else if (name == "PlayerEspNickName") return Keys.PlayerEspNickNameMode;
    else if (name == "PlayerEspPlatform") return Keys.PlayerEspPlatformMode;
    else if (name == "PlayerEspKills") return Keys.PlayerEspKillsMode;
    else if (name == "PlayerEspLevel") return Keys.PlayerEspLevelMode;
    else if (name == "PlayerEspRank") return Keys.PlayerEspRankMode;
    else if (name == "PlayerEspIgnoreDowned") return Keys.PlayerEspIgnoreDownedMode;
    else if (name == "ItemEspEnable") return Keys.ItemEspEnableMode;
    else if (name == "ItemConsumableEnable") return Keys.ItemConsumableEnableMode;
    else if (name == "ItemWeaponEnable") return Keys.ItemWeaponEnableMode;
    else if (name == "ItemAmmoEnable") return Keys.ItemAmmoEnableMode;
    else if (name == "ItemOtherEnable") return Keys.ItemOtherEnableMode;
    else if (name == "RadarEnable") return Keys.RadarEnableMode;
    else if (name == "RadarDistance") return Keys.RadarDistanceMode;
    else if (name == "RadarVisibleColor") return Keys.RadarVisibleColorMode;
    else if (name == "RadarClosestColor") return Keys.RadarClosestColorMode;
    else if (name == "RadarAimingAtMeColor") return Keys.RadarAimingAtMeColorMode;

    return true; // Default to toggle mode
}

// Helper function to get the mode from the Settings struct
bool HotkeySystem::GetModeFromSettingsStruct(const std::string& name) {
    if (name == "AimbotEnable") return Settings.Keys.Keys.AimbotEnableMode;
    else if (name == "AimbotAimLock") return Settings.Keys.Keys.AimbotAimLockMode;
    else if (name == "AimbotPrediction") return Settings.Keys.Keys.AimbotPredictionMode;
    else if (name == "AimbotSaveTarget") return Settings.Keys.Keys.AimbotSaveTargetMode;
    else if (name == "AimbotVisibilityCheck") return Settings.Keys.Keys.AimbotVisibilityCheckMode;
    else if (name == "AimbotHumanizedSmooth") return Settings.Keys.Keys.AimbotHumanizedSmoothMode;
    else if (name == "AimbotIgnoreDowned") return Settings.Keys.Keys.AimbotIgnoreDownedMode;
    else if (name == "AimbotPlayerAi") return Settings.Keys.Keys.AimbotPlayerAiMode;
    else if (name == "AimbotWeaponOnly") return Settings.Keys.Keys.AimbotWeaponOnlyMode;
    else if (name == "AimbotDrawFov") return Settings.Keys.Keys.AimbotDrawFovMode;
    else if (name == "AimbotDrawCrossHair") return Settings.Keys.Keys.AimbotDrawCrossHairMode;
    else if (name == "AimbotDrawTarget") return Settings.Keys.Keys.AimbotDrawTargetMode;
    else if (name == "PlayerEspEnable") return Settings.Keys.Keys.PlayerEspEnableMode;
    else if (name == "PlayerEspBox") return Settings.Keys.Keys.PlayerEspBoxMode;
    else if (name == "PlayerEspSkeleton") return Settings.Keys.Keys.PlayerEspSkeletonMode;
    else if (name == "PlayerEspHeadCircle") return Settings.Keys.Keys.PlayerEspHeadCircleMode;
    else if (name == "PlayerEspLines") return Settings.Keys.Keys.PlayerEspLinesMode;
    else if (name == "PlayerEspDistance") return Settings.Keys.Keys.PlayerEspDistanceMode;
    else if (name == "PlayerEspNickName") return Settings.Keys.Keys.PlayerEspNickNameMode;
    else if (name == "PlayerEspPlatform") return Settings.Keys.Keys.PlayerEspPlatformMode;
    else if (name == "PlayerEspKills") return Settings.Keys.Keys.PlayerEspKillsMode;
    else if (name == "PlayerEspLevel") return Settings.Keys.Keys.PlayerEspLevelMode;
    else if (name == "PlayerEspRank") return Settings.Keys.Keys.PlayerEspRankMode;
    else if (name == "PlayerEspIgnoreDowned") return Settings.Keys.Keys.PlayerEspIgnoreDownedMode;
    else if (name == "ItemEspEnable") return Settings.Keys.Keys.ItemEspEnableMode;
    else if (name == "ItemConsumableEnable") return Settings.Keys.Keys.ItemConsumableEnableMode;
    else if (name == "ItemWeaponEnable") return Settings.Keys.Keys.ItemWeaponEnableMode;
    else if (name == "ItemAmmoEnable") return Settings.Keys.Keys.ItemAmmoEnableMode;
    else if (name == "ItemOtherEnable") return Settings.Keys.Keys.ItemOtherEnableMode;
    else if (name == "RadarEnable") return Settings.Keys.Keys.RadarEnableMode;
    else if (name == "RadarDistance") return Settings.Keys.Keys.RadarDistanceMode;
    else if (name == "RadarVisibleColor") return Settings.Keys.Keys.RadarVisibleColorMode;
    else if (name == "RadarClosestColor") return Settings.Keys.Keys.RadarClosestColorMode;
    else if (name == "RadarAimingAtMeColor") return Settings.Keys.Keys.RadarAimingAtMeColorMode;

    return true; // Default to toggle mode
}

void HotkeySystem::ResetHotkey(const std::string& name) {
    UpdateHotkey(name, 0, true);
}

void HotkeySystem::ResetAllHotkeys() {
    for (auto& [name, binding] : bindings) {
        ResetHotkey(name);
    }
}

void HotkeySystem::SyncWithKeysStruct() {
    // Update the Keys struct from the bindings in a single pass
    for (const auto& [name, binding] : bindings) {
        // Use a direct approach to update the Keys struct
        if (name == "AimbotEnable") {
            Keys.AimbotEnable = binding.keyCode;
            Keys.AimbotEnableMode = binding.toggleMode;
        } else if (name == "AimbotAimLock") {
            Keys.AimbotAimLock = binding.keyCode;
            Keys.AimbotAimLockMode = binding.toggleMode;
        } else if (name == "AimbotPrediction") {
            Keys.AimbotPrediction = binding.keyCode;
            Keys.AimbotPredictionMode = binding.toggleMode;
        } else if (name == "AimbotSaveTarget") {
            Keys.AimbotSaveTarget = binding.keyCode;
            Keys.AimbotSaveTargetMode = binding.toggleMode;
        } else if (name == "AimbotVisibilityCheck") {
            Keys.AimbotVisibilityCheck = binding.keyCode;
            Keys.AimbotVisibilityCheckMode = binding.toggleMode;
        } else if (name == "AimbotHumanizedSmooth") {
            Keys.AimbotHumanizedSmooth = binding.keyCode;
            Keys.AimbotHumanizedSmoothMode = binding.toggleMode;
        } else if (name == "AimbotIgnoreDowned") {
            Keys.AimbotIgnoreDowned = binding.keyCode;
            Keys.AimbotIgnoreDownedMode = binding.toggleMode;
        } else if (name == "AimbotPlayerAi") {
            Keys.AimbotPlayerAi = binding.keyCode;
            Keys.AimbotPlayerAiMode = binding.toggleMode;
        } else if (name == "AimbotWeaponOnly") {
            Keys.AimbotWeaponOnly = binding.keyCode;
            Keys.AimbotWeaponOnlyMode = binding.toggleMode;
        } else if (name == "AimbotDrawFov") {
            Keys.AimbotDrawFov = binding.keyCode;
            Keys.AimbotDrawFovMode = binding.toggleMode;
        } else if (name == "AimbotDrawCrossHair") {
            Keys.AimbotDrawCrossHair = binding.keyCode;
            Keys.AimbotDrawCrossHairMode = binding.toggleMode;
        } else if (name == "AimbotDrawTarget") {
            Keys.AimbotDrawTarget = binding.keyCode;
            Keys.AimbotDrawTargetMode = binding.toggleMode;
        } else if (name == "PlayerEspEnable") {
            Keys.PlayerEspEnable = binding.keyCode;
            Keys.PlayerEspEnableMode = binding.toggleMode;
        } else if (name == "PlayerEspBox") {
            Keys.PlayerEspBox = binding.keyCode;
            Keys.PlayerEspBoxMode = binding.toggleMode;
        } else if (name == "PlayerEspSkeleton") {
            Keys.PlayerEspSkeleton = binding.keyCode;
            Keys.PlayerEspSkeletonMode = binding.toggleMode;
        } else if (name == "PlayerEspHeadCircle") {
            Keys.PlayerEspHeadCircle = binding.keyCode;
            Keys.PlayerEspHeadCircleMode = binding.toggleMode;
        } else if (name == "PlayerEspLines") {
            Keys.PlayerEspLines = binding.keyCode;
            Keys.PlayerEspLinesMode = binding.toggleMode;
        } else if (name == "PlayerEspDistance") {
            Keys.PlayerEspDistance = binding.keyCode;
            Keys.PlayerEspDistanceMode = binding.toggleMode;
        } else if (name == "PlayerEspNickName") {
            Keys.PlayerEspNickName = binding.keyCode;
            Keys.PlayerEspNickNameMode = binding.toggleMode;
        } else if (name == "PlayerEspPlatform") {
            Keys.PlayerEspPlatform = binding.keyCode;
            Keys.PlayerEspPlatformMode = binding.toggleMode;
        } else if (name == "PlayerEspKills") {
            Keys.PlayerEspKills = binding.keyCode;
            Keys.PlayerEspKillsMode = binding.toggleMode;
        } else if (name == "PlayerEspLevel") {
            Keys.PlayerEspLevel = binding.keyCode;
            Keys.PlayerEspLevelMode = binding.toggleMode;
        } else if (name == "PlayerEspRank") {
            Keys.PlayerEspRank = binding.keyCode;
            Keys.PlayerEspRankMode = binding.toggleMode;
        } else if (name == "PlayerEspIgnoreDowned") {
            Keys.PlayerEspIgnoreDowned = binding.keyCode;
            Keys.PlayerEspIgnoreDownedMode = binding.toggleMode;
        } else if (name == "ItemEspEnable") {
            Keys.ItemEspEnable = binding.keyCode;
            Keys.ItemEspEnableMode = binding.toggleMode;
        } else if (name == "ItemConsumableEnable") {
            Keys.ItemConsumableEnable = binding.keyCode;
            Keys.ItemConsumableEnableMode = binding.toggleMode;
        } else if (name == "ItemWeaponEnable") {
            Keys.ItemWeaponEnable = binding.keyCode;
            Keys.ItemWeaponEnableMode = binding.toggleMode;
        } else if (name == "ItemAmmoEnable") {
            Keys.ItemAmmoEnable = binding.keyCode;
            Keys.ItemAmmoEnableMode = binding.toggleMode;
        } else if (name == "ItemOtherEnable") {
            Keys.ItemOtherEnable = binding.keyCode;
            Keys.ItemOtherEnableMode = binding.toggleMode;
        } else if (name == "RadarEnable") {
            Keys.RadarEnable = binding.keyCode;
            Keys.RadarEnableMode = binding.toggleMode;
        } else if (name == "RadarDistance") {
            Keys.RadarDistance = binding.keyCode;
            Keys.RadarDistanceMode = binding.toggleMode;
        } else if (name == "RadarVisibleColor") {
            Keys.RadarVisibleColor = binding.keyCode;
            Keys.RadarVisibleColorMode = binding.toggleMode;
        } else if (name == "RadarClosestColor") {
            Keys.RadarClosestColor = binding.keyCode;
            Keys.RadarClosestColorMode = binding.toggleMode;
        } else if (name == "RadarAimingAtMeColor") {
            Keys.RadarAimingAtMeColor = binding.keyCode;
            Keys.RadarAimingAtMeColorMode = binding.toggleMode;
        }
    }

    // Update the Settings struct
    Settings.Keys.Keys = Keys;

    // Make sure important keys are monitored
    if (Keys.AimbotEnable > 0) InputManager::MonitorKey(Keys.AimbotEnable);
    if (Keys.AimbotAimLock > 0) InputManager::MonitorKey(Keys.AimbotAimLock);
    if (Keys.PlayerEspEnable > 0) InputManager::MonitorKey(Keys.PlayerEspEnable);

    // Update key states for hold mode features
    // AimbotEnable
    if (!Keys.AimbotEnableMode && Keys.AimbotEnable > 0) {
        bool keyDown = InputManager::IsKeyDown(Keys.AimbotEnable);
        Aimbot.Enable = keyDown;
        Settings.Aim.AimbotConfig.Enable = keyDown;

        auto it = bindings.find("AimbotEnable");
        if (it != bindings.end() && it->second.statePtr != nullptr) {
            *(it->second.statePtr) = keyDown;
        }
    }

    // AimbotAimLock
    if (!Keys.AimbotAimLockMode && Keys.AimbotAimLock > 0) {
        bool keyDown = InputManager::IsKeyDown(Keys.AimbotAimLock);
        Aimbot.AimLock = keyDown;
        Settings.Aim.AimbotConfig.AimLock = keyDown;

        auto it = bindings.find("AimbotAimLock");
        if (it != bindings.end() && it->second.statePtr != nullptr) {
            *(it->second.statePtr) = keyDown;
        }
    }

    // PlayerEspEnable
    if (!Keys.PlayerEspEnableMode && Keys.PlayerEspEnable > 0) {
        bool keyDown = InputManager::IsKeyDown(Keys.PlayerEspEnable);
        Players.Enable = keyDown;
        Settings.Esp.Player.Enable = keyDown;

        auto it = bindings.find("PlayerEspEnable");
        if (it != bindings.end() && it->second.statePtr != nullptr) {
            *(it->second.statePtr) = keyDown;
        }
    }
}

// Function to verify mode consistency across all data structures - optimized for performance
void HotkeySystem::VerifyModeConsistency() {
    // First, make sure the Keys struct is up-to-date with the Settings struct
    if (memcmp(&Keys, &Settings.Keys.Keys, sizeof(Keys)) != 0) {
        Keys = Settings.Keys.Keys;
    }

    bool foundInconsistency = false;

    // Check for inconsistencies and fix them in a single pass
    for (auto& [name, binding] : bindings) {
        bool bindingMode = binding.toggleMode;
        bool keysMode = GetModeFromKeysStruct(name);

        if (bindingMode != keysMode) {
            // Use the Keys struct as the source of truth
            binding.toggleMode = keysMode;
            foundInconsistency = true;
        }
    }

    // If we found inconsistencies, update the Settings struct
    if (foundInconsistency) {
        // Update Settings struct
        Settings.Keys.Keys = Keys;

        // Save settings to ensure the changes persist
        SettingsHelper::SaveSettings();
    }
}

// Helper function to update a specific mode field in the Keys struct
void HotkeySystem::UpdateKeysModeField(const std::string& name, bool toggleMode) {
    if (name == "AimbotEnable") Keys.AimbotEnableMode = toggleMode;
    else if (name == "AimbotAimLock") Keys.AimbotAimLockMode = toggleMode;
    else if (name == "AimbotPrediction") Keys.AimbotPredictionMode = toggleMode;
    else if (name == "AimbotSaveTarget") Keys.AimbotSaveTargetMode = toggleMode;
    else if (name == "AimbotVisibilityCheck") Keys.AimbotVisibilityCheckMode = toggleMode;
    else if (name == "AimbotHumanizedSmooth") Keys.AimbotHumanizedSmoothMode = toggleMode;
    else if (name == "AimbotIgnoreDowned") Keys.AimbotIgnoreDownedMode = toggleMode;
    else if (name == "AimbotPlayerAi") Keys.AimbotPlayerAiMode = toggleMode;
    else if (name == "AimbotWeaponOnly") Keys.AimbotWeaponOnlyMode = toggleMode;
    else if (name == "AimbotDrawFov") Keys.AimbotDrawFovMode = toggleMode;
    else if (name == "AimbotDrawCrossHair") Keys.AimbotDrawCrossHairMode = toggleMode;
    else if (name == "AimbotDrawTarget") Keys.AimbotDrawTargetMode = toggleMode;
    else if (name == "PlayerEspEnable") Keys.PlayerEspEnableMode = toggleMode;
    else if (name == "PlayerEspBox") Keys.PlayerEspBoxMode = toggleMode;
    else if (name == "PlayerEspSkeleton") Keys.PlayerEspSkeletonMode = toggleMode;
    else if (name == "PlayerEspHeadCircle") Keys.PlayerEspHeadCircleMode = toggleMode;
    else if (name == "PlayerEspLines") Keys.PlayerEspLinesMode = toggleMode;
    else if (name == "PlayerEspDistance") Keys.PlayerEspDistanceMode = toggleMode;
    else if (name == "PlayerEspNickName") Keys.PlayerEspNickNameMode = toggleMode;
    else if (name == "PlayerEspPlatform") Keys.PlayerEspPlatformMode = toggleMode;
    else if (name == "PlayerEspKills") Keys.PlayerEspKillsMode = toggleMode;
    else if (name == "PlayerEspLevel") Keys.PlayerEspLevelMode = toggleMode;
    else if (name == "PlayerEspRank") Keys.PlayerEspRankMode = toggleMode;
    else if (name == "PlayerEspIgnoreDowned") Keys.PlayerEspIgnoreDownedMode = toggleMode;
    else if (name == "ItemEspEnable") Keys.ItemEspEnableMode = toggleMode;
    else if (name == "ItemConsumableEnable") Keys.ItemConsumableEnableMode = toggleMode;
    else if (name == "ItemWeaponEnable") Keys.ItemWeaponEnableMode = toggleMode;
    else if (name == "ItemAmmoEnable") Keys.ItemAmmoEnableMode = toggleMode;
    else if (name == "ItemOtherEnable") Keys.ItemOtherEnableMode = toggleMode;
    else if (name == "RadarEnable") Keys.RadarEnableMode = toggleMode;
    else if (name == "RadarDistance") Keys.RadarDistanceMode = toggleMode;
    else if (name == "RadarVisibleColor") Keys.RadarVisibleColorMode = toggleMode;
    else if (name == "RadarClosestColor") Keys.RadarClosestColorMode = toggleMode;
    else if (name == "RadarAimingAtMeColor") Keys.RadarAimingAtMeColorMode = toggleMode;
}

// Optimized SaveSettings function to reduce lag
void HotkeySystem::SaveSettings() {
    // Update the Settings struct with the current Keys struct
    Settings.Keys.Keys = Keys;

    // Save settings to ensure the changes are persisted
    try {
        // Force sync settings before saving
        SettingsHelper::SyncSettings();
        SettingsHelper::SaveSettings();
    } catch (const std::exception& e) {
        // Handle exception silently
    }
}

// These functions have been removed as they were only used for debugging

void HotkeySystem::UpdateFeatureState(const std::string& name, bool newState) {
    // Force update the feature in the global state
    // This is critical to ensure the feature state is properly updated
    if (name.find("Aimbot") == 0) {
        // Handle all Aimbot features
        if (name == "AimbotEnable") {
            // Update both the global variable and the Settings struct
            Aimbot.Enable = newState;
            Settings.Aim.AimbotConfig.Enable = newState;

            // If weapon-specific settings are enabled, also update the current weapon's settings
            if (Settings.Aim.AimbotConfig.ByWeapon && Settings.Config.ConfigMenu.selectedWeapon >= 0) {
                Settings.Aim.WeaponConfigs.weaponAimSettings[Settings.Config.ConfigMenu.selectedWeapon].Enable = newState;
            }

            // Force update any GUI state pointer
            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotAimLock") {
            Aimbot.AimLock = newState;
            Settings.Aim.AimbotConfig.AimLock = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotPrediction") {
            Aimbot.Predict = newState;
            Settings.Aim.AimbotConfig.Predict = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotSaveTarget") {
            Aimbot.SaveTarget = newState;
            Settings.Aim.AimbotConfig.SaveTarget = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotVisibilityCheck") {
            Aimbot.VisibilityCheck = newState;
            Settings.Aim.AimbotConfig.VisibilityCheck = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotHumanizedSmooth") {
            Aimbot.HumanizedSmooth = newState;
            Settings.Aim.AimbotConfig.HumanizedSmooth = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotIgnoreDowned") {
            Aimbot.IgnoreDowned = newState;
            Settings.Aim.AimbotConfig.IgnoreDowned = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotPlayerAi") {
            Aimbot.PlayerAi = newState;
            Settings.Aim.AimbotConfig.PlayerAi = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotWeaponOnly") {
            Aimbot.WeaponOnly = newState;
            Settings.Aim.AimbotConfig.WeaponOnly = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotDrawFov") {
            Aimbot.DrawFov = newState;
            Settings.Aim.AimbotConfig.DrawFov = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotDrawCrossHair") {
            Aimbot.DrawCrossHair = newState;
            Settings.Aim.AimbotConfig.DrawCrossHair = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "AimbotDrawTarget") {
            Aimbot.DrawTarget = newState;
            Settings.Aim.AimbotConfig.DrawTarget = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        }
    } else if (name.find("PlayerEsp") == 0) {
        // Handle all Player ESP features
        if (name == "PlayerEspEnable") {
            Players.Enable = newState;
            Settings.Esp.Player.Enable = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspBox") {
            Players.Box = newState;
            Settings.Esp.Player.Box = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspSkeleton") {
            Players.Skeleton = newState;
            Settings.Esp.Player.Skeleton = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspHeadCircle") {
            Players.HeadCircle = newState;
            Settings.Esp.Player.HeadCircle = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspLines") {
            Players.Lines = newState;
            Settings.Esp.Player.Lines = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspDistance") {
            Players.Distance = newState;
            Settings.Esp.Player.Distance = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspNickName") {
            Players.NickName = newState;
            Settings.Esp.Player.NickName = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspPlatform") {
            Players.Platform = newState;
            Settings.Esp.Player.Platform = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspKills") {
            Players.Kills = newState;
            Settings.Esp.Player.Kills = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }
        } else if (name == "PlayerEspLevel") {
            Players.Level = newState;
            Settings.Esp.Player.Level = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "PlayerEspRank") {
            Players.Rank = newState;
            Settings.Esp.Player.Rank = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "PlayerEspIgnoreDowned") {
            Players.IgnoreDowned = newState;
            Settings.Esp.Player.IgnoreDowned = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        }
    } else if (name.find("Item") == 0) {
        // Handle all Item ESP features
        if (name == "ItemEspEnable") {
            Items.Consumable.Enable = newState;
            Settings.Esp.Item.Consumable.Enable = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "ItemConsumableEnable") {
            Items.Consumable.Enable = newState;
            Settings.Esp.Item.Consumable.Enable = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "ItemWeaponEnable") {
            Items.Weapon.Enable = newState;
            Settings.Esp.Item.Weapon.Enable = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "ItemAmmoEnable") {
            Items.Ammo.Enable = newState;
            Settings.Esp.Item.Ammo.Enable = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "ItemOtherEnable") {
            Items.Other.Enable = newState;
            Settings.Esp.Item.Other.Enable = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        }
    } else if (name.find("Radar") == 0) {
        // Handle all Radar features
        if (name == "RadarEnable") {
            Radar.Enable = newState;
            Settings.Radar.RadarConfig.Enable = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "RadarDistance") {
            Radar.Distance = newState;
            Settings.Radar.RadarConfig.Distance = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "RadarVisibleColor") {
            Radar.VisibleColor = newState;
            Settings.Radar.RadarConfig.VisibleColor = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "RadarClosestColor") {
            Radar.ClosestColor = newState;
            Settings.Radar.RadarConfig.ClosestColor = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        } else if (name == "RadarAimingAtMeColor") {
            Radar.AimingAtMeColor = newState;
            Settings.Radar.RadarConfig.AimingAtMeColor = newState;

            auto binding = GetHotkey(name);
            if (binding && binding->statePtr) {
                *binding->statePtr = newState;
            }


        }
    }
}

// This function is now implemented above
