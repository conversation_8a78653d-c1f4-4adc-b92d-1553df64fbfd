#pragma once
#include <Windows.h>
#include <d3d11.h>
#include <string>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../ImGui/imgui.h"
#include "../ImGui/imgui_internal.h"
#include "../Components/Components.h"
#include "../../Cheat Core/Settings/Settings.h"
#include "../../Cheat Core/Features/Config/Config.h"

extern float accent_color[4];

class CGui
{
public:
    void Initialize(ID3D11Device* g_pd3dDevice);
    void Render(bool& ShowMenu);

    // Different menu tabs
    void RenderMainTab();
    void RenderAimbotTab();
    void RenderVisualsTab();
    void RenderRadarTab();
    void RenderSettingsTab();
    void RenderConfigsTab();
    // Loot category tabs
    void RenderConsumablesTab();
    void RenderWeaponsTab();
    void RenderAmmoTab();
    void RenderOtherItemsTab();

    // Animation helpers
    float EaseOutExpo(float x);
    float EaseInOutQuad(float x);
    bool AnimateNextFeature();
    
    // Gradient UI helpers
    void RenderGradientText(const char* text, ImVec2 pos, ImColor startColor, ImColor endColor, ImFont* font = nullptr);
    void DrawGradientRect(ImRect rect, ImU32 color1, ImU32 color2, float thickness);
    void RenderGradientSeparator(float y_offset, float width_percentage);
    void RenderSectionHeader(const char* title, float titleY = 0, float separatorY = 0);

    // Tab state - maps to ModernUI::NavigationPanel::TabIndex
    int currentTab = 0;
    int previousTab = 0;

    // Animation states
    float menuAlpha = 0.0f;
    float contentAlpha = 0.0f;
    float tabTransitionProgress = 1.0f;
    bool isMenuOpen = false;
    float animationSpeed = 0.15f;
    float lastTabChangeTime = 0.0f;
    float featureStartTime = 0.0f;
    int featureIndex = 0;
private:
 
};

extern CGui pGUi; 