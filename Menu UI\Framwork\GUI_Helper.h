#pragma once
#include "../Components/nav_elements.h"
#include "../../Cheat Core/Settings/Settings.h"

// Helper function to create color states for aimbot features
inline std::vector<nav_elements::ColorState> GetAimbotColorStates(const char* featureName) {
    std::vector<nav_elements::ColorState> colorStates;
    
    // FOV feature
    if (strcmp(featureName, "Draw FOV") == 0) {
        colorStates.push_back({ "FOV", { Settings.Colors.AimbotColors.Fov[0], Settings.Colors.AimbotColors.Fov[1], Settings.Colors.AimbotColors.Fov[2], 1.0f }, Settings.Colors.AimbotColors.Fov });
        return colorStates;
    }
    
    // Crosshair feature
    if (strcmp(featureName, "Draw Crosshair") == 0) {
        colorStates.push_back({ "Crosshair", { Settings.Colors.AimbotColors.CrossHair[0], Settings.Colors.AimbotColors.CrossHair[1], Settings.Colors.AimbotColors.CrossHair[2], 1.0f }, Settings.Colors.AimbotColors.CrossHair });
        return colorStates;
    }
    
    // Target line feature
    if (strcmp(featureName, "Draw Target Line") == 0) {
        colorStates.push_back({ "Target", { Settings.Colors.AimbotColors.Target[0], Settings.Colors.AimbotColors.Target[1], Settings.Colors.AimbotColors.Target[2], 1.0f }, Settings.Colors.AimbotColors.Target });
        return colorStates;
    }
    
    // Return empty vector for non-visual features
    return colorStates;
}

// Helper function to create color states for player ESP features
inline std::vector<nav_elements::ColorState> GetPlayerEspColorStates(const char* featureName) {
    std::vector<nav_elements::ColorState> colorStates;
    
    // Box feature
    if (strcmp(featureName, "Bounding Box") == 0 || strcmp(featureName, "Corner Box") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors.BoxVisible[0], Settings.Colors.PlayerColors.BoxVisible[1], Settings.Colors.PlayerColors.BoxVisible[2], Settings.Colors.PlayerColors.BoxVisible[3] }, Settings.Colors.PlayerColors.BoxVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors.BoxNonVisible[0], Settings.Colors.PlayerColors.BoxNonVisible[1], Settings.Colors.PlayerColors.BoxNonVisible[2], Settings.Colors.PlayerColors.BoxNonVisible[3] }, Settings.Colors.PlayerColors.BoxNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors.KnockedBoxVisible[0], Settings.Colors.PlayerColors.KnockedBoxVisible[1], Settings.Colors.PlayerColors.KnockedBoxVisible[2], Settings.Colors.PlayerColors.KnockedBoxVisible[3] }, Settings.Colors.PlayerColors.KnockedBoxVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors.KnockedBoxNonVisible[0], Settings.Colors.PlayerColors.KnockedBoxNonVisible[1], Settings.Colors.PlayerColors.KnockedBoxNonVisible[2], Settings.Colors.PlayerColors.KnockedBoxNonVisible[3] }, Settings.Colors.PlayerColors.KnockedBoxNonVisible });
        return colorStates;
    }
    
    // Skeleton feature
    if (strcmp(featureName, "Skeleton") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors.SkeletonVisible[0], Settings.Colors.PlayerColors.SkeletonVisible[1], Settings.Colors.PlayerColors.SkeletonVisible[2], 1.0f }, Settings.Colors.PlayerColors.SkeletonVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors.SkeletonNonVisible[0], Settings.Colors.PlayerColors.SkeletonNonVisible[1], Settings.Colors.PlayerColors.SkeletonNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.SkeletonNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors.KnockedSkeletonVisible[0], Settings.Colors.PlayerColors.KnockedSkeletonVisible[1], Settings.Colors.PlayerColors.KnockedSkeletonVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedSkeletonVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors.KnockedSkeletonNonVisible[0], Settings.Colors.PlayerColors.KnockedSkeletonNonVisible[1], Settings.Colors.PlayerColors.KnockedSkeletonNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedSkeletonNonVisible });
        return colorStates;
    }
    
    // Head Circle feature
    if (strcmp(featureName, "Head Circle") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors.HeadVisible[0], Settings.Colors.PlayerColors.HeadVisible[1], Settings.Colors.PlayerColors.HeadVisible[2], 1.0f }, Settings.Colors.PlayerColors.HeadVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors.HeadNonVisible[0], Settings.Colors.PlayerColors.HeadNonVisible[1], Settings.Colors.PlayerColors.HeadNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.HeadNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors.KnockedHeadVisible[0], Settings.Colors.PlayerColors.KnockedHeadVisible[1], Settings.Colors.PlayerColors.KnockedHeadVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedHeadVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors.KnockedHeadNonVisible[0], Settings.Colors.PlayerColors.KnockedHeadNonVisible[1], Settings.Colors.PlayerColors.KnockedHeadNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedHeadNonVisible });
        return colorStates;
    }
    
    // Snapline feature
    if (strcmp(featureName, "Snapline") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors.LineVisible[0], Settings.Colors.PlayerColors.LineVisible[1], Settings.Colors.PlayerColors.LineVisible[2], 1.0f }, Settings.Colors.PlayerColors.LineVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors.LineNonVisible[0], Settings.Colors.PlayerColors.LineNonVisible[1], Settings.Colors.PlayerColors.LineNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.LineNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors.KnockedLineVisible[0], Settings.Colors.PlayerColors.KnockedLineVisible[1], Settings.Colors.PlayerColors.KnockedLineVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedLineVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors.KnockedLineNonVisible[0], Settings.Colors.PlayerColors.KnockedLineNonVisible[1], Settings.Colors.PlayerColors.KnockedLineNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedLineNonVisible });
        return colorStates;
    }
    
    // Distance feature
    if (strcmp(featureName, "Distance") == 0) {
        colorStates.push_back({ "Distance", { Settings.Colors.PlayerColors.Distance[0], Settings.Colors.PlayerColors.Distance[1], Settings.Colors.PlayerColors.Distance[2], 1.0f }, Settings.Colors.PlayerColors.Distance });
        return colorStates;
    }
    
    // Names feature
    if (strcmp(featureName, "Names") == 0) {
        colorStates.push_back({ "Name", { Settings.Colors.PlayerColors.NickName[0], Settings.Colors.PlayerColors.NickName[1], Settings.Colors.PlayerColors.NickName[2], 1.0f }, Settings.Colors.PlayerColors.NickName });
        return colorStates;
    }
    
    // Return empty vector for non-visual features
    return colorStates;
}

// Helper function to create color states for radar features
inline std::vector<nav_elements::ColorState> GetRadarColorStates(const char* featureName) {
    std::vector<nav_elements::ColorState> colorStates;
    
    // Radar feature
    if (strcmp(featureName, "Enable Radar") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors.RadarVisible[0], Settings.Colors.PlayerColors.RadarVisible[1], Settings.Colors.PlayerColors.RadarVisible[2], 1.0f }, Settings.Colors.PlayerColors.RadarVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors.RadarNonVisible[0], Settings.Colors.PlayerColors.RadarNonVisible[1], Settings.Colors.PlayerColors.RadarNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.RadarNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors.KnockedRadarVisible[0], Settings.Colors.PlayerColors.KnockedRadarVisible[1], Settings.Colors.PlayerColors.KnockedRadarVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedRadarVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors.KnockedRadarNonVisible[0], Settings.Colors.PlayerColors.KnockedRadarNonVisible[1], Settings.Colors.PlayerColors.KnockedRadarNonVisible[2], 1.0f }, Settings.Colors.PlayerColors.KnockedRadarNonVisible });
        return colorStates;
    }
    
    // Return empty vector for non-visual features
    return colorStates;
}
