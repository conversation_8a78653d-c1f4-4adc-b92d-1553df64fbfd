#pragma once
#include <windows.h>
#include <cstdint>
#include <algorithm>
#include <iostream>
#include <iomanip>
#include <map>
#include "Animation/ImAnim/custom_functions.h"
#include "../ImGui/imgui_settings.h"

inline float content_anim = 0.f;
namespace nav_elements {
    bool Tab(const char* label, const char* icon, int* v, int number);
    bool TabVS2(const char* icon, const char* name, bool boolean);

    // New horizontal tabs for category selection
    bool HorizontalTab(const char* label, int* v, int number);
    bool HorizontalTabV2(const char* name, bool boolean);

    bool Checkbox(const char* label, bool* v, const char* description);

    bool SliderScalar(const char* label, ImGuiDataType data_type, void* p_data, const void* p_min, const void* p_max, const char* format, ImGuiSliderFlags flags = 0, const char* description = "");
    bool SliderFloat(const char* label, float* v, float v_min, float v_max, const char* description = "", const char* format = "%d", ImGuiSliderFlags flags = 0);
    bool SliderInt(const char* label, int* v, int v_min, int v_max, const char* description = "", const char* format = "%d", ImGuiSliderFlags flags = 0);

    bool Keybind(const char* label, const char* description, int* key, bool* mode);
    bool Keyset(const char* label, int* key);

    // Combo box functions
    bool BeginCombo(const char* label, const char* preview_value, int val, const char* description = "", ImGuiComboFlags flags = 0);
    void EndCombo();
    void MultiCombo(const char* label, bool variable[], const char* labels[], int count);
    bool BeginComboPreview();
    void EndComboPreview();

    bool Combo(const char* label, int* current_item, const char* const items[], int items_count, const char* description = "", int height_in_items = -1);
    bool Combo(const char* label, int* current_item, const char* items_separated_by_zeros, const char* description = "", int height_in_items = -1);
    bool Combo(const char* label, int* current_item, const char* (*getter)(void* user_data, int idx), void* user_data, int items_count, const char* description = "", int popup_max_height_in_items = -1);

    // Selectable functions
    bool SelectableEx(const char* label, bool selected, ImGuiSelectableFlags flags = 0, const ImVec2& size_arg = ImVec2(0, 0));
    bool Selectable(const char* label, bool* p_selected, ImGuiSelectableFlags flags = 0, const ImVec2& size_arg = ImVec2(0, 0));

    bool ToggleButton(const char* first_text, const char* second_text, bool* v, const ImVec2& size_arg);

    bool Button(const char* label, const ImVec2& size_arg, ImColor color = gui.main);

    // New button functions with icons
    bool IconButton(const char* label, const char* icon, const ImVec2& size_arg, ImColor color = gui.main, bool selected = false);
    bool IconOnlyButton(const char* icon, const ImVec2& size_arg, ImColor color = gui.main, bool selected = false);

    void Pickerbox(std::string label, const char* description, bool* v, float col[4]);
    void Keybox(std::string label, const char* description, bool* v, int* key, bool* mode);

    // Color state structure for CheckboxComponent
    struct ColorState {
        std::string label;
        float color[4] = {1.0f, 1.0f, 1.0f, 1.0f};
        float* originalColor = nullptr; // Pointer to the original color in Settings
    };

    // Enhanced checkbox with color pickers and settings button
    bool CheckboxComponent(const char* name, bool* v, const char* description, bool hasHotkey, std::vector<ColorState> colorStates, int* key, bool* mode);
    bool ColorEdit4Ex(const char* label, const char* description, float col[4], ImGuiColorEditFlags flags);
    bool ColorPicker4(const char* label, float col[4], ImGuiColorEditFlags flags, const float* ref_col);
    bool ColorButton(const char* desc_id, const ImVec4& col, ImGuiColorEditFlags flags, const ImVec2& size_arg);

    void BeginGroup();
    void EndGroup();
    void Theme();
    const ImWchar* GetGlyphRangesChinese();

}

