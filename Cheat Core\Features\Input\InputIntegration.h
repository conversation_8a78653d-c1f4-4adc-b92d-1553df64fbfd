#pragma once
#include <string>

// Initialize the input system
void InitializeInputSystem();

// Update the input system (call every frame)
void UpdateInputSystem();

// Set the menu open state
void SetMenuOpenState(bool open);

// Enable or disable the hotkey system
void SetHotkeySystemEnabled(bool enable);

// Reset all hotkeys
void ResetAllHotkeys();

// Update a hotkey
void UpdateHotkey(const std::string& name, int keyCode, bool toggleMode);

// Reset a hotkey
void ResetHotkey(const std::string& name);

// Get the name of a key from its virtual key code
std::string GetKeyName(int keyCode);

// Check if a key is currently down
bool IsKeyDown(int keyCode);

// Check if a key was just pressed this frame
bool IsKeyJustPressed(int keyCode);

// Check if a key was just released this frame
bool IsKeyJustReleased(int keyCode);
