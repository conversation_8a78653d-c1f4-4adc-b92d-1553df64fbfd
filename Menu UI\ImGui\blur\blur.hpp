#pragma once

#include <d3d11.h>
#include <d3dcompiler.h>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../imgui.h"
#include "../imgui_internal.h"
#include "../../../Cheat Core/Framwork/Vectors.h"
#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "d3dcompiler.lib")

// Global state variables
inline ID3D11Device* g_pd3dDevice = nullptr;
inline ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
inline IDXGISwapChain* g_pSwapChain = nullptr;
inline int g_FrameCount = 0;
inline bool g_logging_enabled = true; // Always enable logging for debugging


// Helper for logging
inline void blur_log(const char* format, ...) {
    if (!g_logging_enabled) return;
    
    va_list args;
    va_start(args, format);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    printf("[BLUR DEBUG] %s\n", buffer);
    OutputDebugStringA(("[BLUR DEBUG] " + std::string(buffer) + "\n").c_str());
}

// Shader class for compiling and managing pixel shaders
class GaussianBlurShader {
private:
    ID3D11PixelShader* m_shader = nullptr;
    ID3DBlob* m_shaderBlob = nullptr;
    ID3DBlob* m_errorBlob = nullptr;
    bool m_isHorizontal;

public:
    GaussianBlurShader(bool isHorizontal) : m_isHorizontal(isHorizontal) {}

    ~GaussianBlurShader() {
        blur_log("~GaussianBlurShader - releasing resources");
        if (m_shader) m_shader->Release();
        if (m_shaderBlob) m_shaderBlob->Release();
        if (m_errorBlob) m_errorBlob->Release();
    }

    ID3D11PixelShader* Get() {
        if (!m_shader) {
            Compile();
        }
        return m_shader;
    }

    void Compile() {
        // Simpler and more robust blur shader
        std::string shaderCode = R"(
        Texture2D tex0 : register(t0);
        SamplerState sampler0 : register(s0);

        cbuffer BlurParams : register(b0)
        {
            float2 texelSize;    // 1.0/texture dimensions
            float strength;      // Blur strength (sigma)
            float padding;       // Padding for alignment
        };

        struct PS_INPUT
        {
            float4 pos : SV_POSITION;
            float4 col : COLOR0;
            float2 uv  : TEXCOORD0;
        };

        float4 main(PS_INPUT input) : SV_Target
        {
            // Simple 5-tap blur
            float4 color = float4(0, 0, 0, 0);
            float totalWeight = 0.0;
            float2 direction = )";

        // Add direction-specific code
        if (m_isHorizontal) {
            shaderCode += "float2(texelSize.x, 0.0);";
        } else {
            shaderCode += "float2(0.0, texelSize.y);";
        }

        // Complete the shader
        shaderCode += R"(
            // Center pixel
            color += tex0.Sample(sampler0, input.uv) * 0.4;
            totalWeight += 0.4;
            
            // Sample to the sides
            for (int i = 1; i <= 2; i++) {
                float weight = 0.3 / i;
                
                color += tex0.Sample(sampler0, input.uv + direction * i * strength) * weight;
                color += tex0.Sample(sampler0, input.uv - direction * i * strength) * weight;
                
                totalWeight += weight * 2;
            }
            
            return color / totalWeight;
        }
        )";

        blur_log("Compiling %s blur shader", m_isHorizontal ? "horizontal" : "vertical");
        HRESULT hr = D3DCompile(
            shaderCode.c_str(), shaderCode.length(),
            nullptr, nullptr, nullptr,
            "main", "ps_5_0",
            D3DCOMPILE_ENABLE_STRICTNESS, 0,
            &m_shaderBlob, &m_errorBlob);

        if (FAILED(hr)) {
            if (m_errorBlob) {
                blur_log("Shader compilation error: %s", (char*)m_errorBlob->GetBufferPointer());
            } else {
                blur_log("Shader compilation failed with code 0x%08X", hr);
            }
            return;
        }

        hr = g_pd3dDevice->CreatePixelShader(
            m_shaderBlob->GetBufferPointer(),
            m_shaderBlob->GetBufferSize(),
            nullptr, &m_shader);

        if (FAILED(hr)) {
            blur_log("CreatePixelShader failed with code 0x%08X", hr);
            return;
        }

        blur_log("Shader compiled successfully: %p", m_shader);
    }
};

// Struct for blur parameters
struct BlurParameters {
    Vector2 texelSize;    // 2 floats = 8 bytes
    float strength;       // 4 bytes
    float padding;        // 4 bytes padding for 16-byte alignment
};

// Make sure Vector2 is properly defined for our shader
inline void DumpBlurParametersInfo() {
    blur_log("BlurParameters: sizeof=%d, alignof=%d", 
             sizeof(BlurParameters), 
             __alignof(BlurParameters));
    blur_log("Vector2: sizeof=%d, alignof=%d", 
             sizeof(Vector2), 
             __alignof(Vector2));
}

// Blur render system
class BlurRenderSystem {
private:
    // Render targets and views
    ID3D11Texture2D* m_renderTarget1 = nullptr;
    ID3D11Texture2D* m_renderTarget2 = nullptr;
    ID3D11RenderTargetView* m_rtv1 = nullptr;
    ID3D11RenderTargetView* m_rtv2 = nullptr;
    ID3D11ShaderResourceView* m_srv1 = nullptr;
    ID3D11ShaderResourceView* m_srv2 = nullptr;
    ID3D11ShaderResourceView* m_srvCaptured = nullptr;

    // Constant buffer for blur parameters
    ID3D11Buffer* m_constantBuffer = nullptr;

    // Shaders for horizontal and vertical passes
    GaussianBlurShader m_horizontalShader{true};
    GaussianBlurShader m_verticalShader{false};

    // Screen capture resources
    ID3D11Texture2D* m_captureTexture = nullptr;
    
    // Width and height of render targets
    UINT m_width = 0;
    UINT m_height = 0;

    // Blur strength
    float m_blurStrength = 2.0f;

    // Flag to check if we're initialized
    bool m_initialized = false;

    // Helper to create render targets
    bool CreateRenderTarget(ID3D11Texture2D** texture, ID3D11RenderTargetView** rtv, ID3D11ShaderResourceView** srv, UINT width, UINT height) {
        // Create texture
        D3D11_TEXTURE2D_DESC texDesc;
        ZeroMemory(&texDesc, sizeof(texDesc));
        texDesc.Width = width;
        texDesc.Height = height;
        texDesc.MipLevels = 1;
        texDesc.ArraySize = 1;
        texDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
        texDesc.SampleDesc.Count = 1;
        texDesc.SampleDesc.Quality = 0;
        texDesc.Usage = D3D11_USAGE_DEFAULT;
        texDesc.BindFlags = D3D11_BIND_RENDER_TARGET | D3D11_BIND_SHADER_RESOURCE;
        texDesc.CPUAccessFlags = 0;
        texDesc.MiscFlags = 0;

        HRESULT hr = g_pd3dDevice->CreateTexture2D(&texDesc, NULL, texture);
        if (FAILED(hr)) {
            blur_log("CreateTexture2D failed with code 0x%08X", hr);
            return false;
        }

        // Create render target view
        D3D11_RENDER_TARGET_VIEW_DESC rtvDesc;
        ZeroMemory(&rtvDesc, sizeof(rtvDesc));
        rtvDesc.Format = texDesc.Format;
        rtvDesc.ViewDimension = D3D11_RTV_DIMENSION_TEXTURE2D;
        rtvDesc.Texture2D.MipSlice = 0;

        hr = g_pd3dDevice->CreateRenderTargetView(*texture, &rtvDesc, rtv);
        if (FAILED(hr)) {
            blur_log("CreateRenderTargetView failed with code 0x%08X", hr);
            (*texture)->Release();
            *texture = nullptr;
            return false;
        }

        // Create shader resource view
        D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc;
        ZeroMemory(&srvDesc, sizeof(srvDesc));
        srvDesc.Format = texDesc.Format;
        srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
        srvDesc.Texture2D.MostDetailedMip = 0;
        srvDesc.Texture2D.MipLevels = 1;

        hr = g_pd3dDevice->CreateShaderResourceView(*texture, &srvDesc, srv);
        if (FAILED(hr)) {
            blur_log("CreateShaderResourceView failed with code 0x%08X", hr);
            (*rtv)->Release();
            (*texture)->Release();
            *rtv = nullptr;
            *texture = nullptr;
            return false;
        }

        return true;
    }

    // Create constant buffer
    bool CreateConstantBuffer() {
        // Make sure the buffer size is a multiple of 16 bytes (required by D3D11)
        const UINT alignedSize = (sizeof(BlurParameters) + 15) & ~15;
        
        D3D11_BUFFER_DESC bufferDesc;
        ZeroMemory(&bufferDesc, sizeof(bufferDesc));
        bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
        bufferDesc.ByteWidth = alignedSize;
        bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
        bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
        bufferDesc.MiscFlags = 0;
        bufferDesc.StructureByteStride = 0;

        blur_log("Creating constant buffer with size %d (struct size: %d)", alignedSize, sizeof(BlurParameters));
        
        HRESULT hr = g_pd3dDevice->CreateBuffer(&bufferDesc, NULL, &m_constantBuffer);
        if (FAILED(hr)) {
            blur_log("CreateBuffer failed with code 0x%08X", hr);
            return false;
        }
        return true;
    }

    // Update constant buffer parameters
    void UpdateConstantBuffer(float strength, bool isHorizontal) {
        if (!m_constantBuffer) return;

        D3D11_MAPPED_SUBRESOURCE mappedResource;
        HRESULT hr = g_pd3dDeviceContext->Map(m_constantBuffer, 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedResource);
        if (SUCCEEDED(hr)) {
            BlurParameters* params = (BlurParameters*)mappedResource.pData;
            
            // Create a Vector2 directly in the buffer memory
            Vector2 texelSize;
            texelSize.x = 1.0f / m_width;
            texelSize.y = 1.0f / m_height;
            params->texelSize = texelSize;
            
            params->strength = strength;
            params->padding = 0.0f;
            
            g_pd3dDeviceContext->Unmap(m_constantBuffer, 0);
            blur_log("Updated constant buffer: texelSize=(%f,%f), strength=%f", 
                     texelSize.x, texelSize.y, strength);
        } else {
            blur_log("Failed to map constant buffer: 0x%08X", hr);
        }
    }

    // Capture the backbuffer content
    bool CaptureBackbuffer() {
        if (!g_pSwapChain) {
            blur_log("SwapChain is NULL in CaptureBackbuffer");
            return false;
        }

        // Get backbuffer
        ID3D11Texture2D* backBuffer = nullptr;
        HRESULT hr = g_pSwapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (void**)&backBuffer);
        if (FAILED(hr)) {
            blur_log("GetBuffer failed with code 0x%08X", hr);
            return false;
        }

        // Copy to capture texture
        g_pd3dDeviceContext->CopyResource(m_captureTexture, backBuffer);
        backBuffer->Release();

        return true;
    }

public:
    BlurRenderSystem() {}

    ~BlurRenderSystem() {
        Shutdown();
    }

    void Shutdown() {
        blur_log("BlurRenderSystem::Shutdown");
        
        if (m_srvCaptured) m_srvCaptured->Release();
        if (m_captureTexture) m_captureTexture->Release();
        if (m_srv1) m_srv1->Release();
        if (m_srv2) m_srv2->Release();
        if (m_rtv1) m_rtv1->Release();
        if (m_rtv2) m_rtv2->Release();
        if (m_renderTarget1) m_renderTarget1->Release();
        if (m_renderTarget2) m_renderTarget2->Release();
        if (m_constantBuffer) m_constantBuffer->Release();

        m_srvCaptured = nullptr;
        m_captureTexture = nullptr;
        m_srv1 = nullptr;
        m_srv2 = nullptr;
        m_rtv1 = nullptr;
        m_rtv2 = nullptr;
        m_renderTarget1 = nullptr;
        m_renderTarget2 = nullptr;
        m_constantBuffer = nullptr;
        m_initialized = false;
    }

    // Initialize or recreate render targets if needed
    bool EnsureResources(UINT width, UINT height) {
        // If already initialized with the right size, no need to recreate
        if (m_initialized && m_width == width && m_height == height) {
            return true;
        }

        // Release existing resources
        Shutdown();

        blur_log("Creating blur resources for %ux%u", width, height);
        m_width = width;
        m_height = height;

        // Dump diagnostic information for debugging
        DumpBlurParametersInfo();

        // Create render targets for both passes
        if (!CreateRenderTarget(&m_renderTarget1, &m_rtv1, &m_srv1, width, height)) {
            blur_log("Failed to create first render target");
            Shutdown();
            return false;
        }

        if (!CreateRenderTarget(&m_renderTarget2, &m_rtv2, &m_srv2, width, height)) {
            blur_log("Failed to create second render target");
            Shutdown();
            return false;
        }

        // Create capture texture
        D3D11_TEXTURE2D_DESC texDesc;
        ZeroMemory(&texDesc, sizeof(texDesc));
        texDesc.Width = width;
        texDesc.Height = height;
        texDesc.MipLevels = 1;
        texDesc.ArraySize = 1;
        texDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
        texDesc.SampleDesc.Count = 1;
        texDesc.SampleDesc.Quality = 0;
        texDesc.Usage = D3D11_USAGE_DEFAULT;
        texDesc.BindFlags = D3D11_BIND_SHADER_RESOURCE;
        texDesc.CPUAccessFlags = 0;
        texDesc.MiscFlags = 0;

        HRESULT hr = g_pd3dDevice->CreateTexture2D(&texDesc, NULL, &m_captureTexture);
        if (FAILED(hr)) {
            blur_log("Failed to create capture texture: 0x%08X", hr);
            Shutdown();
            return false;
        }

        // Create SRV for capture texture
        D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc;
        ZeroMemory(&srvDesc, sizeof(srvDesc));
        srvDesc.Format = texDesc.Format;
        srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
        srvDesc.Texture2D.MostDetailedMip = 0;
        srvDesc.Texture2D.MipLevels = 1;

        hr = g_pd3dDevice->CreateShaderResourceView(m_captureTexture, &srvDesc, &m_srvCaptured);
        if (FAILED(hr)) {
            blur_log("Failed to create SRV for capture texture: 0x%08X", hr);
            Shutdown();
            return false;
        }

        // Create constant buffer with proper size and alignment
        const UINT alignedSize = (sizeof(BlurParameters) + 15) & ~15;
        blur_log("BlurParameters size: %d, aligned size: %d", sizeof(BlurParameters), alignedSize);
        
        if (!CreateConstantBuffer()) {
            blur_log("Failed to create constant buffer");
            Shutdown();
            return false;
        }

        // Set a reasonable default strength
        m_blurStrength = 2.0f;
        
        // Precompile shaders
        m_horizontalShader.Get();
        m_verticalShader.Get();

        m_initialized = true;
        blur_log("Blur resources created successfully");
        return true;
    }

    // Set blur strength (sigma)
    void SetBlurStrength(float strength) {
        m_blurStrength = strength;
        blur_log("Blur strength set to %f", strength);
    }

    // Apply the blur effect
    bool ApplyBlur(ImDrawList* drawList, const ImVec2& screenSize, const ImVec2& p_min, const ImVec2& p_max) {
        if (!g_pd3dDevice || !g_pd3dDeviceContext) {
            blur_log("Device or context is NULL");
            return false;
        }

        // Store original state
        ID3D11RenderTargetView* originalRTV = nullptr;
        ID3D11DepthStencilView* originalDSV = nullptr;
        g_pd3dDeviceContext->OMGetRenderTargets(1, &originalRTV, &originalDSV);
        
        D3D11_VIEWPORT originalViewport;
        UINT numViewports = 1;
        g_pd3dDeviceContext->RSGetViewports(&numViewports, &originalViewport);

        // Ensure resources are created
        if (!EnsureResources(static_cast<UINT>(screenSize.x), static_cast<UINT>(screenSize.y))) {
            blur_log("Failed to create resources");
            if (originalRTV) originalRTV->Release();
            if (originalDSV) originalDSV->Release();
            return false;
        }

        // Capture backbuffer content
        if (!CaptureBackbuffer()) {
            blur_log("Failed to capture backbuffer");
            if (originalRTV) originalRTV->Release();
            if (originalDSV) originalDSV->Release();
            return false;
        }

        // Set viewport
        D3D11_VIEWPORT viewport;
        viewport.Width = static_cast<float>(m_width);
        viewport.Height = static_cast<float>(m_height);
        viewport.MinDepth = 0.0f;
        viewport.MaxDepth = 1.0f;
        viewport.TopLeftX = 0.0f;
        viewport.TopLeftY = 0.0f;
        g_pd3dDeviceContext->RSSetViewports(1, &viewport);

        // Clear render targets first
        const float clearColor[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
        g_pd3dDeviceContext->ClearRenderTargetView(m_rtv1, clearColor);
        g_pd3dDeviceContext->ClearRenderTargetView(m_rtv2, clearColor);

        // Save original pixel shader
        ID3D11PixelShader* origPixelShader = nullptr;
        g_pd3dDeviceContext->PSGetShader(&origPixelShader, nullptr, 0);

        // First pass: Horizontal blur (captured texture -> render target 1)
        g_pd3dDeviceContext->OMSetRenderTargets(1, &m_rtv1, nullptr);
        UpdateConstantBuffer(m_blurStrength, true);
        g_pd3dDeviceContext->PSSetConstantBuffers(0, 1, &m_constantBuffer);
        g_pd3dDeviceContext->PSSetShaderResources(0, 1, &m_srvCaptured);
        g_pd3dDeviceContext->PSSetShader(m_horizontalShader.Get(), nullptr, 0);

        // Draw a fullscreen quad manually
        D3D11_MAPPED_SUBRESOURCE mappedResource;
        float vertices[] = {
            -1.0f, -1.0f, 0.0f, 0.0f, 1.0f,  // Bottom-left
            -1.0f,  1.0f, 0.0f, 0.0f, 0.0f,  // Top-left
             1.0f, -1.0f, 0.0f, 1.0f, 1.0f,  // Bottom-right
             1.0f,  1.0f, 0.0f, 1.0f, 0.0f   // Top-right
        };

        // Draw using ImDrawList primitives and our pixel shader
        ImGui::GetBackgroundDrawList()->AddCallback([](const ImDrawList* parent_list, const ImDrawCmd* cmd) {
            // This is a dummy callback to preserve render state
        }, nullptr);
        
        // Draw a white quad that covers the entire render target
        ImGui::GetBackgroundDrawList()->AddRectFilled(ImVec2(0, 0), screenSize, IM_COL32_WHITE);
        
        ImGui::GetBackgroundDrawList()->AddCallback(ImDrawCallback_ResetRenderState, nullptr);

        // Second pass: Vertical blur (render target 1 -> render target 2)
        // Unbind first render target as SRV
        ID3D11ShaderResourceView* nullSRV = nullptr;
        g_pd3dDeviceContext->PSSetShaderResources(0, 1, &nullSRV);
        
        g_pd3dDeviceContext->OMSetRenderTargets(1, &m_rtv2, nullptr);
        UpdateConstantBuffer(m_blurStrength, false);
        g_pd3dDeviceContext->PSSetConstantBuffers(0, 1, &m_constantBuffer);
        g_pd3dDeviceContext->PSSetShaderResources(0, 1, &m_srv1);
        g_pd3dDeviceContext->PSSetShader(m_verticalShader.Get(), nullptr, 0);
        
        // Draw another fullscreen quad
        ImGui::GetBackgroundDrawList()->AddCallback([](const ImDrawList* parent_list, const ImDrawCmd* cmd) {
            // Another dummy callback
        }, nullptr);
        
        ImGui::GetBackgroundDrawList()->AddRectFilled(ImVec2(0, 0), screenSize, IM_COL32_WHITE);
        
        ImGui::GetBackgroundDrawList()->AddCallback(ImDrawCallback_ResetRenderState, nullptr);

        // Restore original render target and viewport
        g_pd3dDeviceContext->OMSetRenderTargets(1, &originalRTV, originalDSV);
        g_pd3dDeviceContext->RSSetViewports(1, &originalViewport);
        if (origPixelShader) {
            g_pd3dDeviceContext->PSSetShader(origPixelShader, nullptr, 0);
            origPixelShader->Release();
        }
        if (originalRTV) originalRTV->Release();
        if (originalDSV) originalDSV->Release();

        // Calculate UV coordinates for the region
        ImVec2 uv_min(p_min.x / screenSize.x, p_min.y / screenSize.y);
        ImVec2 uv_max(p_max.x / screenSize.x, p_max.y / screenSize.y);

        // Draw the blurred region from render target 2 - only draw to the requested region
        drawList->AddCallback([](const ImDrawList* parent_list, const ImDrawCmd* cmd) {
            auto ctx = g_pd3dDeviceContext;
            if (!ctx) return;
            
            // Get our SRV from the user data
            auto srv = (ID3D11ShaderResourceView*)cmd->UserCallbackData;
            if (!srv) return;
            
            // Bind our blurred texture
            ctx->PSSetShaderResources(0, 1, &srv);
        }, m_srv2);
        
        // Draw the specified region using the blur result
        drawList->AddImage(m_srv2, p_min, p_max, uv_min, uv_max, IM_COL32_WHITE);
        
        // Reset render state
        drawList->AddCallback(ImDrawCallback_ResetRenderState, nullptr);
        
        return true;
    }
};

// Helper functions
inline int GetFrameCount() {
    blur_log("GetFrameCount called, returning %d", g_FrameCount);
    return g_FrameCount;
}

inline void set_device(ID3D11Device* device, ID3D11DeviceContext* context) {
    blur_log("set_device called with device=%p, context=%p", device, context);
    g_pd3dDevice = device;
    g_pd3dDeviceContext = context;
    blur_log("Device and context set: g_pd3dDevice=%p, g_pd3dDeviceContext=%p", g_pd3dDevice, g_pd3dDeviceContext);
}

inline void set_swap_chain(IDXGISwapChain* swap_chain) {
    blur_log("set_swap_chain called with swap_chain=%p", swap_chain);
    g_pSwapChain = swap_chain;
    blur_log("Swap chain set: g_pSwapChain=%p", g_pSwapChain);
}

inline void new_frame() {
    g_FrameCount++;
    if (g_FrameCount % 100 == 0) {
        blur_log("new_frame: Frame count incremented to %d", g_FrameCount);
    }
}

// Namespace for organized access
namespace blur {
    // Configuration options
    struct BlurConfig {
        float strength = 3.0f;           
        float alpha = 0.85f;               
        bool enable_overlay = false;      
        ImVec4 overlay_color = ImVec4(0.1f, 0.1f, 0.1f, 0.3f);
        bool enable_logging = true;       
    };

    inline BlurConfig config;
    inline bool initialized = false;
    inline bool has_valid_swap_chain = false;
    inline BlurRenderSystem blurSystem;
    
    // Logging helper function
    inline void log_info(const char* format, ...) {
        if (!config.enable_logging && !g_logging_enabled) return;
        
        va_list args;
        va_start(args, format);
        char buffer[1024];
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        
        printf("[BLUR] %s\n", buffer);
        OutputDebugStringA(("[BLUR] " + std::string(buffer) + "\n").c_str());
    }
    
    // Initialize the blur system
    inline bool initialize(ID3D11Device* device, ID3D11DeviceContext* context, IDXGISwapChain* swap_chain) {
        log_info("initialize called: device=%p, context=%p, swap_chain=%p", device, context, swap_chain);
        
        if (!device) {
            log_info("ERROR: NULL device provided");
            return false;
        }
        
        if (!context) {
            log_info("ERROR: NULL device context provided");
            return false;
        }
        
        ::set_device(device, context);
        if (swap_chain) {
            ::set_swap_chain(swap_chain);
            has_valid_swap_chain = true;
            log_info("swap chain set successfully: %p", swap_chain);
        } else {
            log_info("WARNING: No swap chain provided during initialization");
        }
        
        // No need to create resources yet, will be created on first use
        initialized = true;
        log_info("initialization successful - device=%p, context=%p", g_pd3dDevice, g_pd3dDeviceContext);
        return true;
    }
    
    // Check if we have a valid swap chain
    inline bool check_swap_chain() {
        has_valid_swap_chain = (g_pSwapChain != nullptr);
        log_info("check_swap_chain: %s (addr=%p)", has_valid_swap_chain ? "valid" : "invalid", g_pSwapChain);
        return has_valid_swap_chain;
    }
    
    // Set the swap chain for the blur system
    inline void set_swap_chain(IDXGISwapChain* swap_chain) {
        log_info("set_swap_chain called: old=%p, new=%p", g_pSwapChain, swap_chain);
        ::set_swap_chain(swap_chain);
        has_valid_swap_chain = (swap_chain != nullptr);
        log_info("swap chain %s", has_valid_swap_chain ? "valid" : "invalid");
    }
    
    // Update per frame
    inline void begin_frame() {
        ::new_frame();
        if (config.enable_logging && g_FrameCount % 100 == 0) {
            log_info("Frame %d - Device: %p, Context: %p, SwapChain: %p", 
                     g_FrameCount, g_pd3dDevice, g_pd3dDeviceContext, g_pSwapChain);
        }
    }
    
    // Apply blur effect using the advanced blur system
    inline void apply(ImDrawList* draw_list, const ImVec2& p_min, const ImVec2& p_max, float radius = 0.0f) {
        log_info("apply: draw_list=%p, p_min=(%f,%f), p_max=(%f,%f), radius=%f", 
                 draw_list, p_min.x, p_min.y, p_max.x, p_max.y, radius);
                 
        if (!initialized) {
            log_info("ERROR: blur not initialized");
        }
        
        if (!draw_list) {
            log_info("ERROR: draw_list is NULL");
        }
        
        if (!g_pd3dDevice || !g_pd3dDeviceContext) {
            log_info("ERROR: device or context is NULL (device=%p, context=%p)", 
                     g_pd3dDevice, g_pd3dDeviceContext);
        }
        
        if (!g_pSwapChain) {
            log_info("ERROR: swap chain is NULL");
        }

        // Set blur strength from config to a reasonable value
        blurSystem.SetBlurStrength(config.strength);

        // Screen dimensions from ImGui
        ImVec2 screenSize = ImGui::GetIO().DisplaySize;
        
        // Apply the blur effect
        if (blurSystem.ApplyBlur(draw_list, screenSize, p_min, p_max)) {
            return;
        }
        
        // If we get here, blur failed
        log_info("Failed to apply blur effect, using fallback");
        
   
    }
    
    // Set blur configuration
    inline void set_config(const BlurConfig& new_config) {
        log_info("config updated: strength=%f->%f, alpha=%f->%f, overlay=%s->%s", 
                config.strength, new_config.strength,
                config.alpha, new_config.alpha,
                config.enable_overlay ? "enabled" : "disabled", 
                new_config.enable_overlay ? "enabled" : "disabled");
        config = new_config;
    }
    
    // Get current configuration
    inline const BlurConfig& get_config() {
        log_info("get_config called: strength=%f, alpha=%f, overlay=%s", 
                config.strength, config.alpha, config.enable_overlay ? "enabled" : "disabled");
        return config;
    }
    
    // Dump debug info
    inline void dump_debug_info() {
        log_info("=== Blur Debug Info ===");
        log_info("Initialized: %s", initialized ? "Yes" : "No");
        log_info("Has valid swap chain: %s", has_valid_swap_chain ? "Yes" : "No");
        log_info("Swap chain address: %p", g_pSwapChain);
        log_info("Device address: %p", g_pd3dDevice);
        log_info("Device context address: %p", g_pd3dDeviceContext);
        log_info("Frame count: %d", g_FrameCount);
        log_info("Configuration:");
        log_info("  - Strength: %f", config.strength);
        log_info("  - Alpha: %f", config.alpha);
        log_info("  - Overlay: %s", config.enable_overlay ? "Enabled" : "Disabled");
        log_info("  - Logging: %s", config.enable_logging ? "Enabled" : "Disabled");
        
        // Try to output device information
        if (g_pd3dDevice) {
            D3D_FEATURE_LEVEL featureLevel = g_pd3dDevice->GetFeatureLevel();
            log_info("Device Feature Level: 0x%X", featureLevel);
        }
        
        log_info("=====================");
    }
} 