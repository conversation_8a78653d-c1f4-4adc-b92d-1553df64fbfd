#pragma once
#include <unordered_map>
#include <vector>
#include <winuser.rh>
#include "../Features/Aimbot/Aimbot.h"

struct Aimbots {
    bool Enable = true;
    bool SaveTarget = true;
    bool AimLock = false;
    bool VisibilityCheck = true;
    bool HumanizedSmooth = true;
    bool WeaponOnly = true;
    bool IgnoreDowned = true;
    bool Predict = false;
    bool PlayerAi = true;
    bool ByWeapon = false;


    bool DrawFov = true;
    bool DrawFovFilled = false;
    bool DrawFovOutline = true;
    bool DrawFovRGB = true;

    bool DrawCrossHair = false;
    int DrawCrossHairType = 1;

    bool DrawTarget = true;
    int DrawTargetType = 1;
    float DrawThickness = 2.f;
    float DrawSize = 20.f;

    int HitBox = 0;

    float FOV = 200.f;
    float Smooth = 2.0f;
    float HumanizedSmoothPercent = 2.0f;
    int MaxDistance = 600;

};

struct WeaponSettings {
    std::unordered_map<int, Aimbots> weaponAimSettings;
};
extern WeaponSettings weaponSettings;
extern Aimbots Aimbot;

struct Itemss {
    struct Consumables {
        bool Enable = false;
        bool Name = true;
        bool Distance = true;
        bool Icons = true;
        float IconSize = 20.f;
        int MaxDistance = 300;
        float FontSize = 14.f;

        bool Bandages = true;
        bool Medkit = true;
        bool SmallShieldPotion = true;
        bool ShieldPotion = true;
        bool FlowBerryFizz = true;
        bool ChugSplash = true;
        bool NitroSplash = true;
        bool NukaCola = true;

    }Consumable;
    struct Weapons {
        bool Enable = true;
        bool Name = true;
        bool Distance = true;
        bool Icons = true;
        float IconSize = 20.f;
        int MaxDistance = 300;
        float FontSize = 14.f;
        bool Rarity[6] = { true, true, true, true, true, true };

        bool RangerPistol = true;
        bool HarbingerSMG = true;
        bool ThunderBurstSMG = true;
        bool WarforgedAssaultRifle = true;
        bool TacticalAssaultRifle = true;
        bool EnforcerAssaultRifle = true;
        bool CombatAssaultRifle = true;
        bool CombatShotgun = true;
        bool GatekeeperShotgun = true;
        bool HammerPumpShotgun = true;
        bool FrenzyAutoShotgun = true;
        bool HandCannon = true;
        bool HuntressDMR = true;
        bool BoomBolt = true;
        bool NitroFists = true;
        bool Shockwavegrenade = true;
        bool HeavyImpactSniperRifle = true;


        bool MKSevenAssaultRifle = true;
        bool NewPump_Shotgun = true;
        bool OGPump_Shotgun = true;
        bool BottomlessChugJug = true;
        bool BurstAR = true;
        bool DrumGun = true;
        bool SkyesAR = true;
        bool Grappler = true;
        bool StingerSMG = true;
        bool HeistedBreacherShotgun = true;
        bool HeistedAccelerantShotgun = true;
        bool HeistedExplosiveAR = true;
        bool HeistedBlinkMagSMG = true;
        bool HeistedRunGunSMG = true;
        bool TacticalShotgun = true;
        bool LeverActionShotgun = true;
        bool HeavyShotgun = true;
        bool RangerShotgun = true;
        bool AssaultRifle = true;
        bool ScarAssaultRifle = true;
        bool HammerAssaultRifle = true;
        bool HeavyAssaultRifle = true;
        bool InfantryRifle = true;
        bool SubmachineGun = true;
        bool TacticalSubmachineGun = true;
        bool BoltActionSniperRifle = true;
        bool HuntingRifle = true;
        bool Pistol = true;
        bool Revolver = true;
        bool RocketLauncher = true;
        bool CrashPad = true;

        bool FireflyJar = true;
        bool SovereignShotgun = true;
        bool StrikerAR = true;
        bool HyperSMG = true;
        bool StrikerBurstRifle = true;
        bool DualMicroSMGs = true;
        bool MonarchPistol = true;

        bool DualPistols = true;
        bool RapidFireSMG = true;
        bool SuppressedSMG = true;
        bool SuppressedAssaultRifle = true;
        bool HeavySniperRifle = true;
        bool SemiAutomaticSniperRifle = true;
        bool GrenadeLauncher = true;
        bool RemoteExplosives = true;
        bool RegularGrenades = true;
        bool StinkBomb = true;
        bool BandageBazooka = true;
        bool BoogieBomb = true;
        bool Clinger = true;


    }Weapon;
    struct Ammos {
        bool Enable = false;
        bool Name = true;
        bool Distance = true;
        bool Icons = true;
        float IconSize = 20.f;
        int MaxDistance = 300;
        float FontSize = 14.f;

        bool AmmoLight = true;
        bool AmmoMedium = true;
        bool AmmoHeavy = true;
        bool AmmoShells = true;
        bool AmmoRockets = true;

    }Ammo;

    struct Others {
        bool Enable = true;
        bool Name = true;
        bool Distance = true;
        bool Icons = true;
        float IconSize = 20.f;
        int MaxDistance = 300;
        float FontSize = 14.f;

        bool Chest = true;
        bool Vehicle = true;
        bool Llama = true;
        bool SupplyDrop = true;

    }Other;
};

struct Playerss {
    bool Enable = true;

    int MaxDistance = 800;
    float FontSize = 16.f;

    bool TeamCheck = true;


    bool PlayerAi = true;

    bool Box = true;
    int BoxType = 0;
    bool BoxStyle[2] = { true,true };

    float BoxThickness = 2.f;
    float BoxRounding = 0;
    float SkeletonRounding = 10;

    bool IgnoreDowned = false;

    bool HeadCircle = true;
    float HeadCircleThickness = 2.f;

    bool Lines = true;
    int LinesPosition = 0;
    float LinesThickness = 2.f;

    bool Distance = true;
    bool NickName = true;

    bool Platform = true;
    bool Platform_Text = true;
    bool Platform_Icons = true;

    bool Kills = true;
    bool Kills_Text = true;
    bool Kills_Icons = true;

    bool Level = true;
    bool Level_Text = true;
    bool Level_Icons = true;

    bool Rank = true;
    bool Rank_Text = true;
    bool Rank_Icons = true;

    bool Weapon = true;
    bool WeaponRarity = true;
    bool WeaponAmmo = true;

    bool Skeleton = true;
    bool SkeletonCurved = true;

    float SkeletonThickness = 2.f;

};

struct Keyss {
    // Existing keys
    int HoldTrigger = VK_RBUTTON;
    int HoldPrimary = VK_RBUTTON;
    int HoldSecondary = VK_RBUTTON;
    int SwitchToHead = VK_SHIFT;
    int Toggle = VK_F6;
    int TogglePlayers = VK_F7;
    int ToggleItems = VK_F8;
    int ToggleRadar = VK_F9;
    int TogglePanicMode = VK_F10;
    int Menu = VK_INSERT;

    // Aimbot feature keys
    int AimbotEnable = 0;
    int AimbotAimLock = 0;
    int AimbotPrediction = 0;
    int AimbotSaveTarget = 0;
    int AimbotVisibilityCheck = 0;
    int AimbotHumanizedSmooth = 0;
    int AimbotIgnoreDowned = 0;
    int AimbotPlayerAi = 0;
    int AimbotWeaponOnly = 0;
    int AimbotDrawFov = 0;
    int AimbotDrawCrossHair = 0;
    int AimbotDrawTarget = 0;

    // Player ESP feature keys
    int PlayerEspEnable = 0;
    int PlayerEspBox = 0;
    int PlayerEspSkeleton = 0;
    int PlayerEspHeadCircle = 0;
    int PlayerEspLines = 0;
    int PlayerEspDistance = 0;
    int PlayerEspNickName = 0;
    int PlayerEspPlatform = 0;
    int PlayerEspKills = 0;
    int PlayerEspLevel = 0;
    int PlayerEspRank = 0;
    int PlayerEspIgnoreDowned = 0;

    // Item ESP feature keys
    int ItemEspEnable = 0;
    int ItemConsumableEnable = 0;
    int ItemWeaponEnable = 0;
    int ItemAmmoEnable = 0;
    int ItemOtherEnable = 0;

    // Radar feature keys
    int RadarEnable = 0;
    int RadarDistance = 0;
    int RadarVisibleColor = 0;
    int RadarClosestColor = 0;
    int RadarAimingAtMeColor = 0;

    // Hotkey modes (true = toggle, false = hold)
    bool AimbotEnableMode = true;
    bool AimbotAimLockMode = true;
    bool AimbotPredictionMode = true;
    bool AimbotSaveTargetMode = true;
    bool AimbotVisibilityCheckMode = true;
    bool AimbotHumanizedSmoothMode = true;
    bool AimbotIgnoreDownedMode = true;
    bool AimbotPlayerAiMode = true;
    bool AimbotWeaponOnlyMode = true;
    bool AimbotDrawFovMode = true;
    bool AimbotDrawCrossHairMode = true;
    bool AimbotDrawTargetMode = true;

    bool PlayerEspEnableMode = true;
    bool PlayerEspBoxMode = true;
    bool PlayerEspSkeletonMode = true;
    bool PlayerEspHeadCircleMode = true;
    bool PlayerEspLinesMode = true;
    bool PlayerEspDistanceMode = true;
    bool PlayerEspNickNameMode = true;

    bool PlayerEspPlatformMode = true;
    bool PlayerEspKillsMode = true;
    bool PlayerEspLevelMode = true;
    bool PlayerEspRankMode = true;
    bool PlayerEspIgnoreDownedMode = true;


    bool ItemEspEnableMode = true;
    bool ItemConsumableEnableMode = true;
    bool ItemWeaponEnableMode = true;
    bool ItemAmmoEnableMode = true;
    bool ItemOtherEnableMode = true;

    bool RadarEnableMode = true;
    bool RadarDistanceMode = true;
    bool RadarVisibleColorMode = true;
    bool RadarClosestColorMode = true;
    bool RadarAimingAtMeColorMode = true;
};
struct Triggerbots {
    bool Enable = true;
    bool EnableAllWeapons = false;
    bool EnableOnlyShutguns = true;
    bool ByWeapon = false;

    int Delay = 1;
    float MaxDistance = 10;
    int Hotkey = VK_RBUTTON;  // Pixel tolerance for triggering

};

struct AimbotColorss {
    float Fov[3] = { 0.f, 1.f, 0.f };
    float CrossHair[3] = { 1.f, 0.f, 0.f };
    float Target[3] = { 1.f, 1.f, 0.f };
};

struct PlayersColorss {
    float BoxVisible[4] = { 0.f, 1.f, 0.f, 1.f };
    float BoxNonVisible[4] = { 1.f, 1.f, 1.f, 1.f };
    float KnockedBoxVisible[4] = { 0.f, 0.f, 1.f, 1.f };
    float KnockedBoxNonVisible[4] = { 1.f, 1.f, 1.f, 1.f };

    float BoxFillVisible[4] = { 0.f, 0.f, 0.f, 0.22f };
    float BoxFillNonVisible[4] = { 0.f, 0.f, 0.f, 0.22f };
    float KnockedBoxFillVisible[4] = { 0.f, 0.f, 0.f, 0.22f };
    float KnockedBoxFillNonVisible[4] = { 0.f, 0.f, 0.f, 0.22f };

    float SkeletonVisible[3] = { 0.f, 1.f, 0.f };
    float SkeletonNonVisible[3] = { 1.f, 1.f, 1.f };
    float KnockedSkeletonVisible[3] = { 0.f, 0.f, 1.f };
    float KnockedSkeletonNonVisible[3] = { 1.f, 1.f, 1.f };

    float HeadVisible[3] = { 0.f, 1.f, 0.f };
    float HeadNonVisible[3] = { 1.f, 1.f, 1.f };
    float KnockedHeadVisible[3] = { 0.f, 0.f, 1.f };
    float KnockedHeadNonVisible[3] = { 1.f, 1.f, 1.f };

    float LineVisible[3] = { 0.f, 1.f, 0.f };
    float LineNonVisible[3] = { 1.f, 1.f, 1.f };
    float KnockedLineVisible[3] = { 0.f, 0.f, 1.f };
    float KnockedLineNonVisible[3] = { 1.f, 1.f, 1.f };


    float RadarVisible[3] = { 0.f, 1.f, 0.f };
    float RadarNonVisible[3] = { 1.f, 1.f, 1.f };
    float KnockedRadarVisible[3] = { 0.f, 0.f, 1.f };
    float KnockedRadarNonVisible[3] = { 1.f, 1.f, 1.f };

    float Distance[3] = { 0.f, 0.75f, 1.f };

    float NickName[3] = { 1.f, 1.f, 1.f };         // White
};

struct ConfigsMenus {
    int iConfig = 0;
    int oldiConfig = 0;
    int selectedWeapon = 0;
    int GameMode = 0;
    int Language = 0;
    bool FeaturesDefinition = true;
};
struct radars {
    bool Enable = true;
    bool Distance = true;

    int PositionX = 76.0;
    int PositionY = 325.0;
    int CirleSize = 120.0;
    int RectangleSize = 200.0;

    bool VisibleColor = true;
    bool ClosestColor = true;
    bool AimingAtMeColor = true;
    int MaxDistance = 10.;
    int DistanceFontSize = 10.;
    int RadarType = 0;
};

extern Aimbots Aimbot;
extern Triggerbots Triggerbot;
extern Triggerbots triggerbot[static_cast<int>(WeaponTypes::WeaponCount)];
extern Aimbots aimbot[static_cast<int>(WeaponTypes::WeaponCount)];

extern radars Radar;
extern Itemss Items;
extern Playerss Players;
extern Keyss Keys;
extern AimbotColorss AimbotColors;
extern PlayersColorss PlayersColors;
extern ConfigsMenus ConfigsMenu;
